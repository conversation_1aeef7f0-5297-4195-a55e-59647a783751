import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './Home.css';
import HIDlogo from './assets/hidLogo.png';
function NotFound() {
  const [response, setResponse] = useState(null);
  const [error, setError] = useState(null);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
  const [showPortfolioDropdown, setShowPortfolioDropdown] = useState(false);
  const [selectedOption, setSelectedOption] = useState('ServiceAction');
  //const user = { firstName: 'Prasana Srinivasan',email: '<EMAIL>'};
   // State to track which card is visible

 
   // Effect to automatically change the visible box every 3 seconds
   
  

  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        console.log(user);
      } catch (error) {
        
        setUser(null); // Set user to null in case of an error
      }
      setLoading(false);
    }
    checkAuth();
  }, );

  useEffect(() => {
    const fetchData = async () => {
      const requestData = {
        instanceId: 'i-0065e6c46f8da655f', // Replace with actual instance ID
        region: 'us-east-1', // Replace with actual region
        businesscontact: 'John Doe',
        email: '<EMAIL>',
        accountId: ************, // Replace with actual account ID
        accountname: 'Example Account',
        instancename: 'Example Instance',
        servicenownumber: 'SN123456',
        firstname: 'John Doe', // Example name
      };

      try {
        const response = await fetch('https://umanage.dev.hidglobal.com/api/trigger-ssm/run-terraform', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          console.log(response);
          throw new Error(`http error! status: ${response.status}`);
        }

        const data = await response.json();
        setResponse(data);
      } catch (err) {
        setError(err.message);
      }
    };

    fetchData();
  }, []); // Empty dependency array means this runs once on mount

  return <h1>404 - Page Not Found{user.email}</h1>;
}

export default NotFound;
