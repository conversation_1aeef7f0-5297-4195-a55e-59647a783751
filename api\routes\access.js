

const express = require('express');
const csvParser = require('csv-parser');

const AWS = require('aws-sdk');
//const nodemailer = require('nodemailer');
const fastCsv = require('fast-csv');

const { PassThrough } = require('stream');
const { parse } = require('json2csv'); // For creating CSV logs
//const moment = require('moment'); // To handle timestamps
const router = express.Router();
 
module.exports = (s3, Readable) => {

    const assumeRole = async (accountId, firstname) => {
        const sts = new AWS.STS();
        const params = {
          RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
          RoleSessionName: firstname,
        };
        console.log(params);
        try {
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
        } catch (error) {
          AWS.config.update({ credentials: null });
            console.error('Error assuming role:', error.code, error.message);
            if (error.code === 'AccessDenied') {
              const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
              console.log(`Retrying in ${delay / 1000} seconds...`);
              await new Promise(resolve => setTimeout(resolve, delay)); 
              const data = await sts.assumeRole(params).promise();
              return data.Credentials;
                // Handle AccessDenied specifically, maybe log the accounts involved
            
          
        }else{
          console.error('Error assuming role:', error);
          throw error;
        }}
      };
    
      // Function to initialize AWS SDK with temporary credentials and region
      const initializeAWS = async (credentials, region) => {
        AWS.config.update({
          credentials: new AWS.Credentials(
            credentials.AccessKeyId,
            credentials.SecretAccessKey,
            credentials.SessionToken
          ),
          region: region
        });
      };
    
      // Function to configure AWS services (SSM, S3, EC2, CloudFormation)
      const configureAWS = () => {
        return {
          ssm: new AWS.SSM(),
          s3: new AWS.S3(),
          ec2: new AWS.EC2(),
          cloudFormation: new AWS.CloudFormation()
        };
      };
  router.get('/accounts', async (req, res) => {
    try {
      const params = {
        Bucket: 'server-provision-application',
        Key: 'Data/AWSAccountOwners - Nov 2024.csv'
      };
 
      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }
        console.log(data.Body);
 
        const stream = Readable.from(data.Body);
        const results = [];
        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
            
              account:row[Object.keys(row)[0]],
              AAO:row[Object.keys(row)[1]],
              AAU: row[Object.keys(row)[2]],
             
            });
          })
          .on('end', () => {
            res.json(results);
          })
          .on('error', (err) => {
            res.status(500).send(err.message);
          });
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });
  router.get('/approvers', async (req, res) => {
    try {
      const params = {
        Bucket: 'server-provision-application',
        Key: 'Data/request.csv'
      };
 
      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }
        console.log(data.Body);
 
        const stream = Readable.from(data.Body);
        const results = [];
        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
            
              email:row[Object.keys(row)[0]],
              type:row[Object.keys(row)[1]],
              account: row[Object.keys(row)[2]],
              approvers: row[Object.keys(row)[3]],
              username: row[Object.keys(row)[4]],
              justification: row[Object.keys(row)[5]],
             
            });console.log(results);
          })
          .on('end', () => {
            res.json(results);
          })
          .on('error', (err) => {
            res.status(500).send(err.message);
          });
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });
///approver/trigger-ssm
const csvParser = require('csv-parser');
const { PassThrough } = require('stream');
const fastCsv = require('fast-csv');
const nodemailer = require('nodemailer');

const handleSSMRequest = async (req, res, s3, bucketName, fileKey) => {
  try {
    const transporter = nodemailer.createTransport({
      host: 'relay.assaabloy.net',
      port: 25,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: '',
      },
    });

    const newRow = {
      Email: req.body.email || '',
      Type: req.body.type || '',
      Account: req.body.account || '',
      Approver: req.body.type === 'AAO' ?req.body.AAO:req.body.AAU|| '',
      Username:req.body.username,
        Justification:req.body.justification,
    };
    console.log(newRow);
    const results = [];
    let fileExists = true;

    // Step 1: Check if the CSV file exists and read it
    try {
      const s3Data = await s3.getObject({  Bucket: 'server-provision-application',
        Key: 'Data/request.csv'}).promise();
      const stream = new PassThrough();
      stream.end(s3Data.Body);
        console.log(results);
      await new Promise((resolve, reject) => {
        stream
          .pipe(csvParser())
          .on('data', (row) => results.push(row))
          .on('end', resolve)
          .on('error', reject);
      });

      console.log('Existing CSV data fetched successfully.');
    } catch (err) {
      if (err.code === 'NoSuchKey') {
        fileExists = false;
        console.log('CSV file does not exist. Creating a new one.');
      } else {
        throw err; // Other S3 errors
      }
    }

    // Step 2: Append the new row
    results.push(newRow);
    console.log(results);
    // Step 3: Write updated data back to a CSV buffer
    const csvStream = fastCsv.format({ headers: true });
    const csvBuffer = await new Promise((resolve, reject) => {
      const bufferStream = new PassThrough();
      const chunks = [];
      bufferStream.on('data', (chunk) => chunks.push(chunk));
      bufferStream.on('end', () => resolve(Buffer.concat(chunks)));

      csvStream.pipe(bufferStream);
      if (!fileExists) {
        csvStream.write(['Email', 'Type', 'Account']); // Add headers for a new file
      }
      results.forEach((row) => csvStream.write(row));
      csvStream.end();
    });

    // Step 4: Upload the updated CSV file back to S3
    await s3.putObject({
      Bucket: bucketName,
      Key: fileKey,
      Body: csvBuffer,
      ContentType: 'text/csv',
    }).promise();

    console.log('CSV file updated and uploaded to S3 successfully.');
const modified = req.body.email.split('@')[0].replace(/\./g, ' ');

console.log(modified);
    // Step 5: Send the email
    const mail = await transporter.sendMail({
      from: '<EMAIL>',
      to: req.body.type === 'AAO' ? req.body.AAO : req.body.AAU,
      cc: `<EMAIL>, ${req.body.email}`,
      subject: `User requested for ${req.body.type} access for ${req.body.account} account`,
      html: `
  <p>Hi ${req.body.type === 'AAO' ? 'VP': 'AAO'},</p>
  <p>This is to inform you that <b>${modified}</b> has requested access for a <b>${req.body.type}</b> (AWS Account User) to the <b>${req.body.account}</b> account.</p>
  <p>Please process and approve this request at your earliest convenience:</p>
  <p><a href="https://umanage.dev.hidglobal.com/approver" style="color: blue; text-decoration: none;">Click here to approve the request</a></p>
  <p>Thanks,<br>GDIAS Team</p>
`,

    });

    res.status(200).json({ message: 'Request processed successfully' });
  } catch (error) {
    console.error('Error in handleSSMRequest:', error);
    res.status(500).json({ error: error.message });
  }
};
const getAutomationExecutionDetails = async (ssm, executionId) => {
    try {
      const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      return data.AutomationExecution;
    } catch (error) {
      console.error('Error fetching execution details:', error);
      throw error;
    }
  }; const pollExecutionStatus = async (ssm, req, executionID, interval) => {
    const intervalId = setInterval(async () => {
      try {
        const executionDetails = await getAutomationExecutionDetails(ssm, executionID);
        const status = executionDetails.AutomationExecutionStatus;
        console.log(`Current status of execution ID ${executionID}: ${status}`);

        if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
          clearInterval(intervalId);
          console.log(`Final status of execution ID ${executionID}: ${status}`);
        }
      } catch (error) {
        console.error('Error fetching execution status:', error);
        clearInterval(intervalId);
      }
    }, interval);
  };

  const xlsx = require('xlsx');

const Approver = async (req, res, s3, bucketName, fileKey) => {
    try {
      let groups = '';
      try{
        
        const s3ExcelData = await s3.getObject({
          Bucket: 'server-provision-application', // same bucket where the Excel is
          Key: 'Data/U-Access Groups.xlsx', // replace with the actual Excel path
        }).promise();
        console.log('2');
        const workbook = xlsx.read(s3ExcelData.Body, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const sheet = workbook.Sheets[sheetName];
        const rows = xlsx.utils.sheet_to_json(sheet);
        console.log(rows);

       rows.forEach((row, index) => {
  console.log(`Row ${index + 1} Account Name:`, row['Account Names']);
});

        // Find the row with matching account
        const matchedRow = rows.find(row => row['Account Names']  === req.body.item.account);
      console.log(matchedRow);
        if (!matchedRow) {
          return res.status(404).json({ message: 'Account not found in Excel.' });
        }
      
        // Determine approver email based on type
        if (req.body.item.type === 'AAO') {
          groups = matchedRow.AAO;
        } else if (req.body.item.type === 'AAU') {
          groups = matchedRow.AAU;
        } else {
          return res.status(400).json({ message: 'Unknown access type.' });
        }
      
        // Store in request object for later use
       console.log(groups);
      }catch(e){
        console.log(e);
      }
       
        const result=req.body.item.email.substring(0,8);
        try{try {
            
              const credentials = await assumeRole(************, result);
              await initializeAWS(credentials, 'us-east-1');
          
         // const startTime = new Date();
          const { ssm } = configureAWS();
            //const ssm = new AWS.SSM();
            const groupsasarray = groups.split(',');
            const params = {
              DocumentName: 'AWS_Group_Membership_Automation', // Replace with your SSM Automation Document name
              Parameters: {
                Description: [` ${req.body.item.username} requested for ${req.body.item.type} access for ${req.body.item.account} account.Request is Approved by ${req.body.approver}`],
                ContactingCustomer:[req.body.item.email],
                GroupName: groupsasarray,
                UserName:[req.body.item.username],
                AffectingUser:[req.body.item.email], // Pass the email as a parameter
                ShortDescription: [`User requested for ${req.body.item.type}  access for ${req.body.item.account} account`],
                RequestedFor:[req.body.item.email],
                WatchList:[`<EMAIL>,<EMAIL>,${req.body.approveremail}`] // Pass the description as a parameter
              },
               };
        if(req.body.action === 'accept' ){
            // Trigger the SSM Automation Document
            
            const data = await ssm.startAutomationExecution(params).promise();
            console.log('SSM servicenow Automation triggered successfully:', data.AutomationExecutionId);
            
            // Optionally fetch ticket number or status from the Automation execution output
            const executionId = data.AutomationExecutionId;
           await pollExecutionStatus (ssm, req, executionId, 5000)
            const executionDetails = await getAutomationExecutionDetails(ssm, executionId);
            console.log(executionDetails.Outputs);}
         
           if (req.body.item.type === 'AAO') {
            const bucketName = 'server-provision-application';
const csvKey = `Data/AWSAccountOwners - Nov 2024.csv`;

const s3Data = await s3.getObject({ Bucket: bucketName, Key: csvKey }).promise();
const csvContent = s3Data.Body.toString('utf-8');

const rows = [];

let rowFound = false;

// Parse CSV
const parse = require('fast-csv');
await new Promise((resolve, reject) => {
  parse.parseString(csvContent, { headers: false })
    .on('data', (row) => rows.push(row))
    .on('end', resolve)
    .on('error', reject);
});

// Update or append
for (let i = 0; i < rows.length; i++) {
  const row = rows[i];
  const accountCell = row[0];  // Column A
  
  if (String(accountCell) === String(req.body.item.account)) {
    rowFound = true;
    const colC = row[2] || ''; // Column C
    const existingValues = colC.split(',').map(v => v.trim()).filter(v => v);
  
    if (existingValues.includes(req.body.item.email)) {
      throw new Error(`Duplicate email found: ${req.body.item.email}`);
    }
    
    // Append new instanceIds
    row[2] = colC ? `${colC}, ${req.body.item.email}` : req.body.item.email;
    break;
  }
}

if (!rowFound) {
  throw new Error(`Account not  found: ${req.body.item.account}`);
}

// Convert to CSV string
const { writeToString } = require('fast-csv');
const csvString = await writeToString(rows, { headers: false });

// Upload updated CSV back to S3
await s3.putObject({
  Bucket: bucketName,
  Key: csvKey,
  Body: csvString,
  ContentType: 'text/csv',
}).promise();

           }
          }catch(error) {
            console.error('Error in handleSSMRequest:', error);
            console.log('in updating excel');
            console.log(error);
            throw error;
            
          }
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
  
      const newRow = {
        Email: req.body.email || '',
        Type: req.body.type || '',
        Account: req.body.account || '',
        Approver: req.body.type === 'AAO' ?req.body.AAO:req.body.AAU|| '',
      };
  
      const results = [];
      let fileExists = true;
  
      // Step 1: Check if the CSV file exists and read it
      try {
        const s3Data = await s3.getObject({  Bucket: 'server-provision-application',
          Key: 'Data/request.csv'}).promise();
        const stream = new PassThrough();
        stream.end(s3Data.Body);
  
        await new Promise((resolve, reject) => {
          stream
            .pipe(csvParser())
            .on('data', (row) => results.push(row))
            .on('end', resolve)
            .on('error', reject);
        });
  
       // console.log('Existing CSV data fetched successfully.');
      } catch (err) {
        if (err.code === 'NoSuchKey') {
          fileExists = false;
          console.log('CSV file does not exist. Creating a new one.');
        } else {
          throw err; // Other S3 errors
        }
      }
      //console.log(results);
      // Step 2: Append the new row
      const itemToRemove = req.body.item; // Identify which row to delete
     // console.log(itemToRemove);
      const updatedResults = results.filter(
        (row) => row.Email !== itemToRemove.email || row.Type !== itemToRemove.type || row.Account !== itemToRemove.account
      );
  
      if (updatedResults.length === results.length) {
        return res.status(404).json({ message: 'Item not found in the CSV file.' });
      }
  
     // console.log('Item removed from CSV.');
  
      // Step 3: Write updated data back to a CSV buffer
      const csvStream = fastCsv.format({ headers: true });
      const csvBuffer = await new Promise((resolve, reject) => {
        const bufferStream = new PassThrough();
        const chunks = [];
        bufferStream.on('data', (chunk) => chunks.push(chunk));
        bufferStream.on('end', () => resolve(Buffer.concat(chunks)));
  
        csvStream.pipe(bufferStream);
        if (!fileExists) {
          csvStream.write(['Email', 'Type', 'Account']); // Add headers for a new file
        }
        updatedResults.forEach((row) => csvStream.write(row));
        csvStream.end();
      });
  
      // Step 4: Upload the updated CSV file back to S3
      await s3.putObject({
        Bucket: bucketName,
        Key: fileKey,
        Body: csvBuffer,
        ContentType: 'text/csv',
      }).promise();
  
      //console.log('CSV file updated and uploaded to S3 successfully.');
  try{// Extract details from the request
   

    // Prepare the log entry
    const logEntry = {
      Requester:req.body.item.email,
      ApproverRejector:req.body.approver,
      Action: req.body.action, // e.g., Approved/Rejected
      Type: req.body.item.type,
      Account:req.body.item.account,
      Justification: req.body.item.justification,
    };

   // console.log('Log entry:', logEntry);

    // Step 1: Check if the log file exists in S3
    let logFileExists = true;
    let existingLogs = [];

    try {
      const s3Data = await s3
        .getObject({
          Bucket: 'server-provision-application',
          Key: 'tickets/request-logs.csv',
        })
        .promise();

      const stream = new PassThrough();
      stream.end(s3Data.Body);

      // Parse existing CSV logs
      await new Promise((resolve, reject) => {
        stream
          .pipe(fastCsv.parse({ headers: true }))
          .on('data', (row) => existingLogs.push(row))
          .on('end', resolve)
          .on('error', reject);
      });
    } catch (error) {
      if (error.code === 'NoSuchKey') {
        logFileExists = false; // Log file does not exist yet
      } else {
        throw error;
      }
    }

    // Step 2: Add the new log entry
    existingLogs.push(logEntry);
//console.log(existingLogs);
    // Convert logs to CSV format
    const csvData = parse(existingLogs);
    console.log(csvData);
    // Step 3: Upload the updated log file back to S3
    await s3
      .putObject({
        Bucket: 'server-provision-application',
        Key: 'tickets/request-logs.csv',
        
        Body: csvData,
        ContentType: 'text/csv',
      })
      .promise(); 
      console.log('successfully updated request logs');} catch (error)
      {
    console.error('Error in request logs:', error);
    res.status(500).json({ error: error.message });
  }
  const now = new Date();
const oneHourLater = new Date(now.getTime() + 60 * 60 * 1000); // Add 1 hour

const formattedTime = oneHourLater.toLocaleString('en-US', {
  hour: 'numeric',
  minute: '2-digit',
  hour12: true,
  day: 'numeric',
  month: 'short',
  year: 'numeric',
});
      // Step 5: Send the email
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${req.body.item.email}`,
        cc: `<EMAIL> `,
        subject: `${req.body.action === 'accept' ?'Accepted':'Rejected'} ${req.body.item.type} access for ${req.body.item.account} account`,
        html: `${req.body.action === 'reject' ?`
        <p>Hi ${req.body.item.username},</p> <p>We regret to inform you that your request has been rejected by  ${req.body.approver}. </p> 
        <p>If you would like to know further details, we kindly recommend that you contact  ${req.body.approver} directly to inquire about the reason for the rejection. </p>
         <p>Thanks,<br>Enterprise AWS Cloud Security and Governance</p>`:

   `   <p>Hi ${req.body.item.username},</p>
    <p>Your U-Access  request has been Approved .</p>
    <p>Access will be reflected by: <strong> ${formattedTime} UTC </strong> for the required account. Please use the following link to log in to Umanage:</p>
    <a href="https://umanage.eit.hidglobal.com/" style="color: blue; text-decoration: none;">Click here</a>
    <p>You can refer to the documentation and user guide on how to use Umanage at:
      <a href="https://umanage.eit.hidglobal.com/help" style="color: blue; text-decoration: none;">Click here</a>.
    </p>
    <p>You can also access the AWS Portal here:
      <a href="https://hidglobal.awsapps.com/start/#/?tab=accounts" style="color: blue; text-decoration: none;">Click here</a>.
    </p> <p>Thanks,<br>Enterprise AWS Cloud Security and Governance</p>`} 
    `
    
  ,
});
  
      res.status(200).json({ message: 'Request processed successfully', email: mail });
    } catch (error) {
      console.error('Error in sendingmail:', error);
      res.status(500).json({ error: error.message });
    }
  }catch (error) {
    console.error('Error in approving:', error);
    res.status(500).json({ error: error.message });
  };}
  

  router.post('/request', (req, res) => handleSSMRequest(req, res,s3,'server-provision-application', 'Data/request.csv'));
  router.post('/approver/trigger-ssm', (req, res) => Approver(req, res,s3,'server-provision-application', 'Data/request.csv'));
  return router;
};