import React from 'react';
import './AboutStop.css'; // Ensure to create this CSS file for styling
import Stop from '../assets/about/Stop.png';
const AboutStop = () => (
  <div className="about-stop-container">
    <h2 className="about-stop-title">Stop Action</h2>
    <p className="about-stop-description">
      The Stop action is used to halt a <strong> running instance </strong>. This operation pauses the instance and stops billing for 
      resources, except for storage. It is useful for saving costs when the instance is not needed.
    </p>
    <h3 className="about-stop-subtitle">Steps to Stop an Instance</h3>
    <ol className="about-stop-steps">
      <li className="about-stop-step">
        <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
        
        <div className="about-stop-image-container">
          <img src={Stop} alt="Select Account Sample" className="about-stop-image" />
        </div>
      </li>
      <li className="about-stop-step">
        <strong>Select the Region:</strong> Pick the region assigned to that account, as instance availability is region-specific.
      </li>
      <li className="about-stop-step">
        <strong>Select the Instance:</strong> Locate the instance by either its ID or Name for easy identification.
       
      </li>
      <li className="about-stop-step">
        <strong>Review Details:</strong> Confirm all instance details, such as configuration and status.
      </li>
      <li className="about-stop-step">
        <strong>Acknowledge:</strong> Check the acknowledgment box to confirm understanding of the actions.
      </li>
      <li className="about-stop-step">
        <strong>Click the "Stop" Button:</strong> Execute the stop action. You can either wait in the portal for a 
        status update or log out, as a confirmation email will be sent regarding the result of the action.
      </li>
    </ol>
    
  </div>
);

export default AboutStop;
