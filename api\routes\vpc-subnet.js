const express = require('express');
const csvParser = require('csv-parser');
const router = express.Router();

module.exports = (s3, Readable) => {
  router.get('/', async (req, res) => {
    try {
      const params = {
        Bucket: 'server-provision-application',
        Key: 'Data/vpc_details.csv', // Adjust the path as per your S3 bucket structure
      };

      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }

        const stream = Readable.from(data.Body);
        const results = [];
        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
              VpcId: row[Object.keys(row)[3]],
              VpcName: row[Object.keys(row)[4]],
              SubnetId: row[Object.keys(row)[5]],
              SubnetName: row[Object.keys(row)[6]],
              VpcRegion: row[Object.keys(row)[2]],
              CreateAccountId: row[Object.keys(row)[0]],
              CreateAccountName: row[Object.keys(row)[1]],
              SecurityGroupId: row[Object.keys(row)[7]],
              SecurityGroupName: row[Object.keys(row)[8]],
              AZ: row[Object.keys(row)[9]],
              AvailableIPs: row[Object.keys(row)[10]],
              CIDR: row[Object.keys(row)[11]],
            });
          })
          .on('end', () => {
            
            res.json(results);
          })
          .on('error', (err) => {
            res.status(500).send(err.message);
          });
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });
  router.get('/isolated', async (req, res) => {
    console.log('1234');
    try {
      const params = {
        Bucket: 'server-provision-application',
        Key: 'Data/vpc_details_isolated.csv', // Adjust the path as per your S3 bucket structure
      };

      s3.getObject(params, (err, data) => {
        if (err) {
          console.log(err);
          return res.status(500).send(err.message);
        }

        const stream = Readable.from(data.Body);
        const results = [];
        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
              VpcId: row[Object.keys(row)[3]],
              VpcName: row[Object.keys(row)[4]],
              SubnetId: row[Object.keys(row)[5]],
              SubnetName: row[Object.keys(row)[6]],
              VpcRegion: row[Object.keys(row)[2]],
              CreateAccountId: row[Object.keys(row)[0]],
              CreateAccountName: row[Object.keys(row)[1]],
              SecurityGroupId: row[Object.keys(row)[7]],
              SecurityGroupName: row[Object.keys(row)[8]],
              AZ: row[Object.keys(row)[9]],
              AvailableIPs: row[Object.keys(row)[10]],
              CIDR: row[Object.keys(row)[11]],
            });
          })
          .on('end', () => {
            
            res.json(results);
          })
          .on('error', (err) => {
            console.log(err);
            res.status(500).send(err.message);
          });
      });
    } catch (err) {
      console.log(err);
      res.status(500).send(err.message);
    }
  });

  return router;
};
