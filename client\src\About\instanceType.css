.instance-table-container {
    padding: 20px;
    margin-left: 20px ;
    
    font-family: Arial, sans-serif;
}

.title {
    font-size: 2rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
}

.description {
    text-align: center;
    margin-bottom: 20px;
}

.filters {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.form-control{
    margin-right: 10px;
}

.filter-button {
    padding: 0px 10px 0px 10px ;
    margin: 0 10px;
    font-size: 1rem;
    font-weight:500 ;
    height: 35px;
    cursor: pointer;
    
    background-color: #ffffff;
    color: #103153;
    border-color: #103153;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.filter-button:hover {
    background-color: #103153;
    color: #ffffff;
    border-color: #103153;
    
}

.clear-button {
    background-color: #ffffff;
    color: #103153;
    border-color: #103153;
}

.clear-button:hover {
    background-color: #c82333;
}

.instance-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.instance-table th, .instance-table td {
    border: 1px solid #dddddd;
    text-align: left;
    padding: 8px;
}

.instance-table th {
    background-color: #f2f2f2;
    font-weight: bold;
}
