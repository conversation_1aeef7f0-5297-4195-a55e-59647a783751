.dashboard-container {
  width: 100%;
  height: 800px;
  border: 1px solid #ddd;
  border-radius: 10px;
  overflow: auto; /* Ensure scrolling is enabled */
}
.selection-section {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  transition: max-height 0.3s ease, opacity 0.3s ease;
  position: relative;
}

.selection-section.open {
  max-height: 300px;
  opacity: 1;
}

.selection-section.closed {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
}

.dropdown-section {
  margin-bottom: 20px;
}

.dropdown-heading {
  font-size: 1.2rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.dropdown-description {
  font-size: 0.9rem;
  color: #555;
  margin-bottom: 10px;
}

.dropdown-row {
  display: flex;
  gap: 20px;
}

.re-select {
  flex: 1;
}

.submit-button {
  background-color: #00549B;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 20px;
  transition: background-color 0.3s;
}

.submit-button:hover:enabled {
  background-color: #013057;
}

.submit-button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}


.toggle-button-outside {
  position: absolute; /* Anchor the button to the top-right corner */
  top: 10px;
  right: 10px;
  background-color: #00020300;
  color: #00549B;
  border: none;
  padding: 10px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.5rem;

}

.toggle-button-outside:hover {
  color: #053053;
}

.toggle-button-inside {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background-color: #00559b00;
  color: #00549B;
  border: none;
  padding: 10px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.5rem;
  
}

.toggle-button-inside:hover {
  background-color: #0057b300;
  color:#053053;
}

.dashboard-view {
  position: relative;
  background-color: #ffffff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
}

.dashboard-heading {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.dashboard-description {
  font-size: 1rem;
  color: #555;
  margin-bottom: 20px;
}

.dashboard-container {
  width: 100%;
  height: 800px;
  border: 1px solid #ddd;
  border-radius: 10px;
  overflow: hidden;
}