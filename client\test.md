

# **Access Page Documentation**

The **Access Page** is designed to allow users to request access to specific AWS accounts. It provides a form where users can input their details, select an account, and submit a request. The backend processes the request and provides feedback to the user.

---

## **Key Features**
1. **Account Selection**:
   - Fetches available AWS accounts dynamically from the backend.
   - Displays the accounts in a dropdown for user selection.

2. **User Input**:
   - Accepts user details such as email, username, justification, and access type.

3. **Request Submission**:
   - Sends the access request to the backend for processing.
   - Displays a success or error message based on the backend response.

4. **Loading and Feedback**:
   - Shows a loading indicator while the request is being processed.
   - Provides real-time feedback to the user.

---

## **Code Walkthrough**

### **1. State Management**
The component uses React's `useState` to manage form inputs, processing state, and feedback messages.

```javascript
// State variables for form inputs and processing status
const [email, setEmail] = useState(""); // Stores the user's email
const [username, setUsername] = useState(""); // Stores the AD username
const [justification, setJustification] = useState(""); // Stores the justification for access
const [account, setAccount] = useState(""); // Stores the selected AWS account
const [type, setType] = useState(""); // Stores the type of access requested
const [accounts, setAccounts] = useState([]); // Stores the list of available accounts
const [isProcessing, setIsProcessing] = useState(false); // Tracks if the request is being processed
const [messagestatus, setMessagestatus] = useState(); // Tracks the status of the request (success or error)
const [message, setMessage] = useState(''); // Stores the feedback message
const [data, setData] = useState(''); // Stores the raw account data fetched from the backend
const [alertMessage, setAlertMessage] = useState(""); // Stores the alert message for the user
```

---

### **2. Fetching AWS Accounts**
The `useEffect` hook is used to fetch the list of AWS accounts from the backend when the component loads.

```javascript
useEffect(() => {
  async function checkAuth() {
    try {
      const response = await axios.get('https://umanage.dev.hidglobal.com/api/access/accounts');
      const accounts = response.data.map(item => item.account); // Extract account names
      setData(response.data); // Store the raw data
      setAccounts(accounts); // Store the account names
      console.log(response.data); // Log the fetched data for debugging
    } catch (error) {
      setAccounts(null); // Set accounts to null in case of an error
    }
  }
  checkAuth();
}, []);
```

---

### **3. Handling Form Inputs**
The form inputs are dynamically updated using 

onChange

 handlers.

```javascript
<div className="dropdown-section">
  <h2 className="dropdown-heading">AD Name</h2>
  <input
    type="text"
    className="form-control"
    value={username}
    onChange={(e) => setUsername(e.target.value.toUpperCase())} // Convert input to uppercase
    placeholder="AD name"
  />
</div>
```

---

### **4. Submitting the Access Request**
The `handleTriggerSSM` function sends the access request to the backend.

```javascript
const handleTriggerSSM = () => {
  setIsProcessing(true); // Set processing state to true
  setAlertMessage("Awaiting Process Completion !"); // Display an alert message

  const selectedAccountObject = data.find(item => item.account === account); // Find the selected account object

  axios.post('https://umanage.dev.hidglobal.com/api/access/request', {
    account: selectedAccountObject.account,
    AAO: selectedAccountObject.AAO,
    AAU: selectedAccountObject.AAU,
    email: email.trim(),
    username: username.trim(),
    justification: justification.trim(),
    type: type
  })
    .then((response) => {
      setIsProcessing(false); // Set processing state to false
      setMessagestatus(true); // Set status to success
      setMessage("Access request submitted successfully!"); // Display success message
    })
    .catch((error) => {
      setIsProcessing(false); // Set processing state to false
      setMessagestatus(false); // Set status to error
      setMessage("Failed to submit access request."); // Display error message
    });
};
```

---

### **5. Loading and Feedback**
A loading indicator and feedback messages are displayed based on the processing state and backend response.

```javascript
if (isProcessing) {
  return <LoadingPage />; // Show loading page while data is being processed
}

{message && (
  <div className={`status-card ${messagestatus ? 'success' : 'error'}`}>
    <p>{message}</p>
  </div>
)}
```

---

## **HTML Structure**

### **1. Email Address Input**
```javascript
<div className="dropdown-section">
  <h2 className="dropdown-heading">Email Address</h2>
  <input
    type="email"
    className="form-control"
    value={email}
    onChange={(e) => setEmail(e.target.value)}
    placeholder="<EMAIL>"
  />
</div>
```

---

### **2. AD Name Input**
```javascript
<div className="dropdown-section">
  <h2 className="dropdown-heading">AD Name</h2>
  <input
    type="text"
    className="form-control"
    value={username}
    onChange={(e) => setUsername(e.target.value.toUpperCase())} 
    placeholder="AD name"
  />
</div>
```

---

### **3. Account Name Dropdown**
```javascript
<div className="dropdown-section">
  <h2 className="dropdown-heading">Account Name</h2>
  <select className="form-control" value={account} onChange={(e) => setAccount(e.target.value)}>
    <option value="">Select Account</option>
    {accounts.map(region => (
      <option key={region} value={region}>{region}</option>
    ))}
  </select>
</div>
```

---

### **4. Type Selection Dropdown**
```javascript
{account && (
  <div className="dropdown-section">
    <h2 className="dropdown-heading">Select the Type</h2>
    <select className="form-control" value={type} onChange={(e) => setType(e.target.value)}>
      <option value="">Select type</option>
      <option value="AAO">AAO - AWS Account Owner</option>
      <option value="AAU">AAU - AWS Account User</option>
    </select>
  </div>
)}
```

---

### **5. Access Justification Textarea**
```javascript
<h2 className="dropdown-heading">Access Justification</h2>
<textarea
  className="big-input-simulated"
  value={justification}
  onChange={handleJustificationChange}
  placeholder="..."
  rows="1"
/>
```

---

### **6. Submit Button**
```javascript
<button
  className="submit-button"
  onClick={handleTriggerSSM}
  disabled={!email || !username || !account || !type || !justification}
>
  Submit Request
</button>
```

---

## **Backend Integration**
The backend endpoints used in this page are:
1. **`GET /api/access/accounts`**:
   - Fetches the list of AWS accounts available for access requests.
   - Returns an array of account objects.

2. **`POST /api/access/request`**:
   - Submits the access request with the provided details.
   - Processes the request and returns a success or error response.

---

## **Future Enhancements**
1. **Validation**:
   - Add client-side validation for form inputs (e.g., email format, required fields).

2. **Role-Based Access**:
   - Restrict access to this page based on user roles.

3. **Real-Time Updates**:
   - Use WebSockets or polling to provide real-time updates on the request status.

4. **Improved UI**:
   - Add tooltips and better styling for a more user-friendly experience.

---

Save this as a `.md` file, and you can open it in any Markdown viewer or editor. Let me know if you need further adjustments!