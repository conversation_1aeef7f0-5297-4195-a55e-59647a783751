const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');
const nodemailer = require('nodemailer');
const csvParser = require('csv-parser');
const axios = require('axios');


// Export the function to set up the router with necessary utilities
module.exports = (s3,Readable,generateTicketNumber, storeDataInS3) => {
  let originalCredentials = AWS.config.credentials;
 
  // Function to assume an IAM role and return temporary credentials
  const assumeRole = async (accountId,firstname) => {
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    console.log(firstname);
    console.log(params);
    try {
      const data = await sts.assumeRole(params).promise();
      return data.Credentials;
    } catch (error) {
      AWS.config.update({ credentials: null });
        console.error('Error assuming role:', error.code, error.message);
        if (error.code === 'AccessDenied') {
          const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
            // Handle AccessDenied specifically, maybe log the accounts involved
       
     
    }else{
      console.error('Error assuming role:', error);
      throw error;
    }}
  };
 
  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };
 
  // Function to configure AWS services (SSM, S3, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      cloudFormation: new AWS.CloudFormation()
    };
  };
 
 
  const handleSSMRequest = async (req, res, documentName) => {
    const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} =  req.body;
 console.log("i am in new ssm");
    let firstname= req.body.firstname;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');

    const sendUpdate = (message) => {
      res.write(`${message }\n\n`);
    };
// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
    if (!instanceId || !region || !accountId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
 let params;
 if (documentName === 'AWS-ResizeInstance') {
  params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId],
      InstanceType: [req.body.instanceType], // assuming you have a variable `instanceType`
    }
  };
} else {
  params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId],
      // Add other parameters required by your SSM document here
    }
  };
}
 console.log(params);
 console.log(req.body);
    try {
      // AWS.config.update({ credentials: originalCredentials });
      if(accountId!=************){
      const credentials = await assumeRole(accountId,y);
      await initializeAWS(credentials, region);}else{AWS.config.update({
        region: region
      });}
      const { ssm } = configureAWS();
 
      const ticketNumber = generateTicketNumber();
      let executionID;
      let startTime = new Date();
      
  const day = String(startTime.getDate()).padStart(2, '0');
  const month = String(startTime.getMonth() + 1).padStart(2, '0');
  const year = startTime.getFullYear();

   let qdate=`${day}-${month}-${year}`;
   console.log(qdate);
      const data = await ssm.startAutomationExecution(params).promise();
      executionID = data.AutomationExecutionId;
      console.log(originalCredentials);
      AWS.config.update({ credentials: originalCredentials });
      const getAutomationssmExecutionStatus1 = async (ssm, executionId) => {
        try {
          const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
          console.log('Automation execution data:', data); // Log entire response
          const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
          return data;
        } catch (error) {
          console.error('Error fetching execution status:', error);
          throw error;
        }
      };
      let servicenownumber;
        // Poll status and fetch detailed result
        const pollAutomationssmExecutionStatus1 = async (ssm, executionId, interval = 1000) => {
          return new Promise((resolve, reject) => {
            const intervalId = setInterval(async () => {
              try {
                const data = await getAutomationssmExecutionStatus1(ssm, executionId);
                const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
                console.log('Current status:', status);
               
                sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
                if (['Success', 'Failed', 'TimedOut', 'Cancelled','Pending'].includes(status)) {
                    if ([ 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                        sendUpdate(`Error: ${data.AutomationExecution.CurrentStepName} step is ${status}`);
                    }
                  clearInterval(intervalId);
                  resolve(status);
                }
              } catch (error) {
                clearInterval(intervalId);
                reject(error);
              }
            }, interval);
          });
        };
      // Poll status and fetch detailed result
      const status = await pollAutomationssmExecutionStatus1(ssm, executionID);
      const executionDetails = await getAutomationssmExecutionStatus1(ssm, executionID);
      // const data = await getAutomationssmExecutionStatus1(ssm, executionId);
      const status2= executionDetails.AutomationExecution ? executionDetails.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      console.log(executionDetails);
      console.log(qdate);
      //await storeDataInS3(ticketNumber, executionID,instancename,accountId, documentName, qdate,endTime,status, instanceId,businesscontact,email,accountname,servicenownumber);
     
    // const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} = req.body;
      const endTime =new Date();
      sendUpdate('Execution Successfull Sending Email');
      console.log('Execution Successfull Sending Email');
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${email},<EMAIL>`,
        cc: `${businesscontact} `,
        subject: `${documentName} Notification - ${instanceId}`,
        html: `<p>Hi,</p>  
 
<p>We are pleased to inform you that the EC2 instance operation has been successfully completed. Below are the details of the operation:</p>  
 
<p><strong>Status:</strong>${status}</p>  
<p><strong>Operation:</strong> ${documentName}</p>  
<p><strong>Instance Name:</strong> ${instancename}</p>  
<p><strong>Account ID:</strong> ${accountId}</p>  
<p><strong>Execution Date:</strong> ${startTime}</p>  
<p><strong>Instance ID:</strong> ${instanceId}</p>  
<p><strong>Region:</strong> ${region}</p>  
 
<p>For any inquiries, please reach out to:</p>  
<p><strong>Business Contact:</strong>${businesscontact}</p>  
<p><strong>Service Initialization Engineer:</strong> ${email}</p>  
<p><strong>AWS Account:</strong>${accountname}</p>  
 
<p>You can access the AWS Portal here:  
<a href="https://hidglobal.awsapps.com/" style="color: blue; text-decoration: none;">Click here</a>.</p>  
<p>Thanks,<br>GDIAS Team</p>
 
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</em></p>
 
    `
   
  ,
});
console.log('Execution Successfull  Email sent');
      sendUpdate('Successfull Executed Storing Logs');
      await new Promise(resolve => setTimeout(resolve, 10000));
     
      
      AWS.config.update({ credentials: originalCredentials });
      //sendUpdate('Successfull');
      res.end('Successfull');
     

    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
 
      sendUpdate(`Error: ${err}`);
     
      res.end(`Error: ${err}`);
    }
  };
  const handleRunTerraformRequest = async (req, res, documentName) => {
    console.log("in function");
    const { instanceId, region, businesscontact, email, accountId, accountname, instancename, servicenownumber } = req.body;
 
    let firstname=req.body.firstname;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;
 
// Concatenate the random value to the string
let y = result + randomValue.toString();
    if (!instanceId || !region || !accountId) {
        return res.status(400).json({ error: 'Missing required fields' });
    }
 
    const params = {
        DocumentName: 'AWS-RunPowerShellScript',
        Parameters: {
            commands: [
                `$domain = (Get-WmiObject Win32_ComputerSystem).Domain;` +
                `if ($domain -ne "WORKGROUP") {` +
                `    Write-Output "This instance is part of the domain: $domain"` +
                `} else {` +
                `    Write-Output "This instance is not part of a domain."` +
                `}`
            ]
        },
        Targets: [
            {
                Key: 'instanceids',
                Values: [instanceId]
            }
        ],
        Comment: 'Running PowerShell script from Node.js'
    };
 
    try {
        if (accountId != ************) {
            const credentials = await assumeRole(accountId, y);
            await initializeAWS(credentials, region);
        } else {
            AWS.config.update({ region: region });
        }
        const { ssm } = configureAWS();
 
        const ticketNumber = generateTicketNumber();
        const data = await ssm.sendCommand(params).promise();
        const commandID = data.Command.CommandId;
       
        // Polling for command status
        const commandStatus = await pollCommandStatus(ssm, commandID);
        AWS.config.update({ credentials: originalCredentials });
        // Handle the command output here
        const commandDetails = await getCommandInvocation(ssm, commandID, instanceId);
        console.log('Command execution status:', commandDetails.Status);
        console.log('Command output:', commandDetails.CommandPlugins[0].Output);
 
        //await storeDataInS3(ticketNumber, commandID, accountId, commandDetails.CommandPlugins[0].Output,businesscontact, email, accountname, instancename, servicenownumber);
        AWS.config.update({ credentials: originalCredentials });
        res.status(200).json({
            message: 'SSM Command executed successfully!',
           
            status: commandDetails.CommandPlugins[0].Output,
           
        });
    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
        res.status(500).json({ error: err.message });
    }
};
 
// Function to poll command status
const pollCommandStatus = async (ssm, commandID) => {
    let status;
    do {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for 5 seconds
        const commandInvocations = await ssm.listCommandInvocations({
            CommandId: commandID,
            Details: true,
        }).promise();
        status = commandInvocations.CommandInvocations[0].Status; // Get the status of the first invocation
    } while (status === 'Pending' || status === 'InProgress');
 
    return status;
};
 
// Function to get command invocation details
const getCommandInvocation = async (ssm, commandID, instanceId) => {
    const commandInvocations = await ssm.listCommandInvocations({
        CommandId: commandID,
        Details: true,
    }).promise();
    return commandInvocations.CommandInvocations.find(invocation => invocation.InstanceId === instanceId);
};
 
 
 
const handledomainlinuxSSMRequest = async (req, res, documentName) => {
  const { instanceId, region,  accountId, instanceName,group1,group2,group3,group4} = req.query;
  
console.log(req.body);
  res.setHeader('Content-Type', 'text/event-stream');
res.setHeader('Cache-Control', 'no-cache');
res.setHeader('Connection', 'keep-alive');
const sendUpdate = (message) => {
  res.write(`data: ${JSON.stringify({ message })}\n\n`);
};
  let firstname=req.query.firstname;
  let x=firstname.replace(/\s+/g,'');
  let result=x.substring(0,8);
  let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
console.log(req.query);
  if (!instanceId || !region || !accountId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  const groups=`${req.query.group1}, ${req.query.group2}, ${req.query.group3}, ${req.query.group4}`;
 let params;
 if (documentName === "linux_domain_join") {
   params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [req.query.instanceId],
      
      Assumerole: [`arn:aws:iam::${req.query.accountId}:role/CrossAccountAccessRole`],
      
      GroupName: [groups],
      
     // WatchList:["<EMAIL>,<EMAIL>"],
     // Add other parameters required by your SSM document here
    }
  };
 } else {
  params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId],
     
      Assumerole: [`arn:aws:iam::${req.query.accountId}:role/CrossAccountAccessRole`],
     
      // Add other parameters required by your SSM document here
    }
  };
 }console.log(params);
  try {
    // AWS.config.update({ credentials: originalCredentials });
    console.log('in try');
    if(accountId!=************){
      console.log('in tryin if');
    const credentials = await assumeRole(accountId,y);
    console.log('in try after if');
    await initializeAWS(credentials, region);}else{AWS.config.update({
      region: region
    });}
    const { ssm } = configureAWS();

    const ticketNumber = generateTicketNumber();
    let executionID;
    const startTime = new Date();
    try {
      console.log('in try before ssm');
      const data = await ssm.startAutomationExecution(params).promise();
      console.log('in try after ssm');
      executionID = data.AutomationExecutionId;
    } catch (error) {
      console.error('Error starting automation execution:', error);
    }
   
    // Poll status and fetch detailed result
    const getAutomationssmExecutionStatus = async (ssm, executionId) => {
      try {
        const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
        console.log('Automation execution data:', data); // Log entire response
        const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
        return data;
      } catch (error) {
        console.error('Error fetching execution status:', error);
        throw error;
      }
    };
    let servicenownumber;
      // Poll status and fetch detailed result
      const pollAutomationssmExecutionStatus = async (ssm, executionId, interval = 1000) => {
        return new Promise((resolve, reject) => {
          const intervalId = setInterval(async () => {
            try {
              const data = await getAutomationssmExecutionStatus(ssm, executionId);
              const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
              console.log('Current status:', status);
              const targetStep = data.AutomationExecution.StepExecutions.find(
                step => step.StepName === 'CloseNotes'
              );
          
              if (targetStep && targetStep.Outputs) {
                console.log("Step Outputs:", targetStep.Outputs);
               //servicenownumber= targetStep.Outputs.RITM[0];
                console.log("Step Outputs:", targetStep.Outputs);
              } else {
                console.log("No outputs found for the specified step.");
              }
              sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
              if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
               
                clearInterval(intervalId);
                resolve(status);
              }
            } catch (error) {
              clearInterval(intervalId);
              reject(error);
            }
          }, interval);
        });
      }; console.log('in try before status');
    const status = await pollAutomationssmExecutionStatus(ssm, executionID);
    const executionDetails = await getAutomationssmExecutionStatus(ssm, executionID);
  
  //   if(getAutomationssmExecutionStatus(ssm, executionID).AutomationExecution.AutomationExecutionStatus=='Success'){
  //   sendUpdate(`Please wait Service now ticket is creating  `);
  //   AWS.config.update({ credentials: originalCredentials });
  // const paramsforservice = {
  //   DocumentName: 'User-Snow-Ticket', // Replace with your SSM Automation Document name
  //   Parameters: {
  //     Description: [` ${params} `],
  //     ContactingCustomer:[req.query.email],
  //     AffectingUser:[req.query.email], // Pass the email as a parameter
  //     ShortDescription: [`Domain join Action`],
  //     RequestedFor:[req.query.email],
  //     WatchList:[`<EMAIL>,<EMAIL>`] // Pass the description as a parameter
  //   },
  //    };
  //    const data = await ssm.startAutomationExecution(paramsforservice).promise();
  //    executionID = data.AutomationExecutionId;
  //    try{
      
  //    }catch{

  //    }
  //    sendUpdate(`Service now Ticket created successfully`); 
  // }
  //   console.log('Execution details:', executionDetails);
    //const startTime = new Date(executionDetails.ExecutionStartTime * 1000).toISOString();
//const endTime = new Date(executionDetails.ExecutionEndTime * 1000).toISOString();
   // const startTime = executionDetails.ExecutionStartTime;
    const endTime =new Date();
    const data2 = await getAutomationssmExecutionStatus(ssm, executionID);
    const status2= data2.AutomationExecution ? data2.AutomationExecution.AutomationExecutionStatus : 'Unknown';
    // console.log("status2",data2);
    // console.log(data2.Outputs);
    // const x=data2.AutomationExecution.StepExecutions.find(
    //   step => step.StepName === 'RetrieveRITM'
    // );
    // console.log(x);
    // console.log(x.Outputs);
    // console.log(x.Outputs.RITM);
    // //sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
    //   AWS.config.update({ credentials: originalCredentials });
      sendUpdate('SSM Automation Document execution completed.');
      sendUpdate(`RITM Number is  And been closed`);
     // sendUpdate('OK');
      res.end();
  } catch (err) {
    sendUpdate(`Error: ${err}`);
    res.end();
  }
};



  
const handledomainSSMRequest = async (req, res, documentName) => {
  const { instanceId, region,  accountId, instanceName,group1,group2,group3,group4} = req.query;
  
console.log(req.body);
  res.setHeader('Content-Type', 'text/event-stream');
res.setHeader('Cache-Control', 'no-cache');
res.setHeader('Connection', 'keep-alive');
const sendUpdate = (message) => {
  res.write(`data: ${JSON.stringify({ message })}\n\n`);
};
  let firstname=req.query.firstname;
  let x=firstname.replace(/\s+/g,'');
  let result=x.substring(0,8);
  let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
console.log(req.query);
  if (!instanceId || !region || !accountId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
 let params;
 if (documentName === "windows_domain_join") {
   params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [req.query.instanceId],
      
      Assumerole: [`arn:aws:iam::${req.query.accountId}:role/CrossAccountAccessRole`],
      GroupName1: [req.query.group1],
      GroupName2: [req.query.group2],
      GroupName3: [req.query.group3],
      GroupName4: [req.query.group4],
     
     // WatchList:["<EMAIL>,<EMAIL>"],
     // Add other parameters required by your SSM document here
    }
  };
 } else {
  params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId],
      pass:['u8vJUsg9V)2OWsTFa1ZO'],
      Assumerole: [`arn:aws:iam::${req.query.accountId}:role/CrossAccountAccessRole`],
     
      // Add other parameters required by your SSM document here
    }
  };
 }console.log(params);
  try {
    // AWS.config.update({ credentials: originalCredentials });
    console.log('in try');
    if(accountId!=************){
      console.log('in tryin if');
    const credentials = await assumeRole(accountId,y);
    console.log('in try after if');
    await initializeAWS(credentials, region);}else{AWS.config.update({
      region: region
    });}
    const { ssm } = configureAWS();

    const ticketNumber = generateTicketNumber();
    let executionID;
    const startTime = new Date();
    try {
      console.log('in try before ssm');
      const data = await ssm.startAutomationExecution(params).promise();
      console.log('in try after ssm');
      executionID = data.AutomationExecutionId;
    } catch (error) {
      console.error('Error starting automation execution:', error);
    }
   
    // Poll status and fetch detailed result
    const getAutomationssmExecutionStatus = async (ssm, executionId) => {
      try {
        const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
        console.log('Automation execution data:', data); // Log entire response
        const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
        return data;
      } catch (error) {
        console.error('Error fetching execution status:', error);
        throw error;
      }
    };
    let servicenownumber;
      // Poll status and fetch detailed result
      const pollAutomationssmExecutionStatus = async (ssm, executionId, interval = 1000) => {
        return new Promise((resolve, reject) => {
          const intervalId = setInterval(async () => {
            try {
              const data = await getAutomationssmExecutionStatus(ssm, executionId);
              const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
              console.log('Current status:', status);
              const targetStep = data.AutomationExecution.StepExecutions.find(
                step => step.StepName === 'CloseNotes'
              );
          
              if (targetStep && targetStep.Outputs) {
                console.log("Step Outputs:", targetStep.Outputs);
               //servicenownumber= targetStep.Outputs.RITM[0];
                console.log("Step Outputs:", targetStep.Outputs);
              } else {
                console.log("No outputs found for the specified step.");
              }
              sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
              if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
               
                clearInterval(intervalId);
                resolve(status);
              }
            } catch (error) {
              clearInterval(intervalId);
              reject(error);
            }
          }, interval);
        });
      }; console.log('in try before status');
    const status = await pollAutomationssmExecutionStatus(ssm, executionID);
    const executionDetails = await getAutomationssmExecutionStatus(ssm, executionID);
  
  //   if(getAutomationssmExecutionStatus(ssm, executionID).AutomationExecution.AutomationExecutionStatus=='Success'){
  //   sendUpdate(`Please wait Service now ticket is creating  `);
  //   AWS.config.update({ credentials: originalCredentials });
  // const paramsforservice = {
  //   DocumentName: 'User-Snow-Ticket', // Replace with your SSM Automation Document name
  //   Parameters: {
  //     Description: [` ${params} `],
  //     ContactingCustomer:[req.query.email],
  //     AffectingUser:[req.query.email], // Pass the email as a parameter
  //     ShortDescription: [`Domain join Action`],
  //     RequestedFor:[req.query.email],
  //     WatchList:[`<EMAIL>,<EMAIL>`] // Pass the description as a parameter
  //   },
  //    };
  //    const data = await ssm.startAutomationExecution(paramsforservice).promise();
  //    executionID = data.AutomationExecutionId;
  //    try{
      
  //    }catch{

  //    }
  //    sendUpdate(`Service now Ticket created successfully`); 
  // }
  //   console.log('Execution details:', executionDetails);
    //const startTime = new Date(executionDetails.ExecutionStartTime * 1000).toISOString();
//const endTime = new Date(executionDetails.ExecutionEndTime * 1000).toISOString();
   // const startTime = executionDetails.ExecutionStartTime;
    const endTime =new Date();
    const data2 = await getAutomationssmExecutionStatus(ssm, executionID);
    const status2= data2.AutomationExecution ? data2.AutomationExecution.AutomationExecutionStatus : 'Unknown';
    // console.log("status2",data2);
    // console.log(data2.Outputs);
    // const x=data2.AutomationExecution.StepExecutions.find(
    //   step => step.StepName === 'RetrieveRITM'
    // );
    // console.log(x);
    // console.log(x.Outputs);
    console.log(x.Outputs.RITM);
    //sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
      AWS.config.update({ credentials: originalCredentials });
      sendUpdate('SSM Automation Document execution completed.');
      sendUpdate(`RITM Number is  And been closed`);
     // sendUpdate('OK');
      res.end();
  } catch (err) {
    sendUpdate(`Error: ${err}`);
    res.end();
  }
};


const handleEBSSSMRequest = async (req, res, documentName) => {
  const {  region,email, accountId,accountname, instancename } =  req.body;
  console.log(req.body);
  const {
    instanceId,
    CostCenter = "6420",
    CostCenterDescription = "Infrastructure and NOC",
    SupportTier = "TIER3",
    SupportTierDescription = "ON-DEMAND (automatic first backup)",
    InstanceSource = "INEMB",
    ProvisioningEntity = "HID Engineer",
    BusinessArea = "OtherBA",
    BusinessContact = "Paramjeet Singh",
    BusinessContactEmail = "<EMAIL>",
    BusinessSegment = "9000",
    mapmigrated = "MigC5H22Y5OCL",
    BusinessSegmentDescription = "HID Global",
    TechnicalContact = "Steve Hayter",
    TechnicalContactEmail = "<EMAIL>",
    Environment = "SANDBOX",
    NetworkLocation = "INTERNAL",
    FunctionalArea = "IT",
    ProvisioningEngineer = "Prasana Srinivasan",
    BackupPlan = "BRONZE",
   
    
    volumesize,
    DeviceName,
    existing,
    volumeexistid,
    volumetype
  } = req.body;

  const params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId], // AWS SSM expects arrays for parameters
      RoleArn: [`arn:aws:iam::${accountId}:role/CrossAccountAccessRole`],
      CostCenter: [CostCenter],
      CostCenterDescription: [CostCenterDescription],
      SupportTier: [SupportTier],
      SupportTierDescription: [SupportTierDescription],
      InstanceSource: [InstanceSource],
      ProvisioningEntity: [ProvisioningEntity],
      BusinessArea: [BusinessArea],
      BusinessContact: [BusinessContact],
      BusinessContactEmail: [BusinessContactEmail],
      BusinessSegment: [BusinessSegment],
      mapmigrated: [mapmigrated],
      BusinessSegmentDescription: [BusinessSegmentDescription],
      TechnicalContact: [TechnicalContact],
      TechnicalContactEmail: [TechnicalContactEmail],
      Environment: [Environment],
      NetworkLocation: [NetworkLocation],
      FunctionalArea: [FunctionalArea],
      ProvisioningEngineer: [ProvisioningEngineer],
      BackupPlan: [BackupPlan],
     
      volumesize: [volumesize],
      DeviceName: [DeviceName],
      existing:[existing],
      volumeexistid:[volumeexistid],
      volumetype:[volumetype]
    },
  };

console.log(params);
  let firstname= req.body.firstname;
  let x=firstname.replace(/\s+/g,'');
  let result=x.substring(0,8);
  let randomValue = Math.floor(Math.random() * 9000) + 1000;
  res.setHeader('Content-Type', 'text/plain');
  res.setHeader('Transfer-Encoding', 'chunked');

  const sendUpdate = (message) => {
    res.write(`${message }\n\n`);
  };
// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
  if (!instanceId || !region || !accountId) {
    console.log("some req var are missing");
    return res.status(400).json({ error: 'Missing required fields' });
  }

  

  try {
    // AWS.config.update({ credentials: originalCredentials });
    if(accountId!=************){
    const credentials = await assumeRole(accountId,y);
    await initializeAWS(credentials, region);}else{AWS.config.update({
      region: region
    });}
    const { ssm } = configureAWS();

    const ticketNumber = generateTicketNumber();
    let executionID;
    const startTime = new Date();
    console.log(startTime);
    let data;
    try{
     data = await ssm.startAutomationExecution(params).promise();}catch(err){
      console.log(err);
     }console.log("after trigger");
    executionID = data.AutomationExecutionId;
    console.log(originalCredentials);
    AWS.config.update({ credentials: originalCredentials });
    const getAutomationssmExecutionStatus1 = async (ssm, executionId) => {
      try {
        const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
        console.log('Automation execution data:', data); // Log entire response
        const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
        return data;
      } catch (error) {
        console.error('Error fetching execution status:', error);
        throw error;
      }
    };
    let servicenownumber;
      // Poll status and fetch detailed result
      const pollAutomationssmExecutionStatus1 = async (ssm, executionId, interval = 1000) => {
        return new Promise((resolve, reject) => {
          const intervalId = setInterval(async () => {
            try {
              const data = await getAutomationssmExecutionStatus1(ssm, executionId);
              const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
              console.log('Current status:', status);
             
              sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
              if (['Success', 'Failed', 'TimedOut', 'Cancelled','Pending'].includes(status)) {
                  if ([ 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                      sendUpdate(`Error: ${data.AutomationExecution.CurrentStepName} step is ${status}`);
                  }
                clearInterval(intervalId);
                resolve(status);
              }
            } catch (error) {
              clearInterval(intervalId);
              reject(error);
            }
          }, interval);
        });
      };
    // Poll status and fetch detailed result
    const status = await pollAutomationssmExecutionStatus1(ssm, executionID);
    const executionDetails = await getAutomationssmExecutionStatus1(ssm, executionID);
    // const data = await getAutomationssmExecutionStatus1(ssm, executionId);
    const status2= executionDetails.AutomationExecution ? executionDetails.AutomationExecution.AutomationExecutionStatus : 'Unknown';
    console.log(executionDetails);
    console.log(status2);
  // const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} = req.body;
    const endTime =new Date();
    sendUpdate('Execution Successfull Sending Email');
    console.log('Execution Successfull Sending Email');
    const transporter = nodemailer.createTransport({
      host: 'relay.assaabloy.net',
      port: 25,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: '',
      },
    });

    const mail = await transporter.sendMail({
      from: '<EMAIL>',
      to: `${email}`,
      cc: `${BusinessContact} `,
      subject: `${documentName} Notification - ${instanceId}`,
      html: `<p>Hi,</p>  

<p>We are pleased to inform you that the EC2 instance operation has been successfully completed. Below are the details of the operation:</p>  

<p><strong>Status:</strong>${status}</p>  
<p><strong>Operation:</strong> ${documentName}</p>  
<p><strong>Instance Name:</strong> ${instancename}</p>  
<p><strong>Account ID:</strong> ${accountId}</p>  
<p><strong>Execution Date:</strong> ${startTime}</p>  
<p><strong>Instance ID:</strong> ${instanceId}</p>  
<p><strong>Region:</strong> ${region}</p>  

<p>For any inquiries, please reach out to:</p>  
<p><strong>Business Contact:</strong>${businesscontact}</p>  
<p><strong>Service Initialization Engineer:</strong> ${email}</p>  
<p><strong>AWS Account:</strong>${accountname}</p>  

<p>You can access the AWS Portal here:  
<a href="https://hidglobal.awsapps.com/" style="color: blue; text-decoration: none;">Click here</a>.</p>  
<p>Thanks,<br>GDIAS Team</p>

<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</em></p>

  `
 
,
});
console.log('Execution Successfull  Email sent');
    sendUpdate('Successfull Executed Storing Logs');
    await new Promise(resolve => setTimeout(resolve, 10000));
   
    
   //await storeDataInS3(ticketNumber, executionID,instancename,accountId, documentName, startTime,endTime,status, instanceId,businesscontact,email,accountname,servicenownumber);
    AWS.config.update({ credentials: originalCredentials });
    //sendUpdate('Successfull');
    res.end('Successfull');
   

  } catch (err) {
    AWS.config.update({ credentials: originalCredentials });

    sendUpdate(`Error: ${err}`);
   
    res.end(`Error: ${err}`);
  }
};
  
router.post('/stop', (req, res) => handleSSMRequest(req, res, 'AWS-StopEC2Instance'));
  router.post('/adx', async (req, res) => {
    const { params } = req.body;
    console.log('in adax');
    // Authentication details
    const username = '<EMAIL>';
    const password = 'x5EMe7a4W3Qfb6FDo82N'; // TODO: Set your password

    const baseUrl = 'https://adx-api.assaabloy.net/restApi';

    try {
      // Step 1: Create session and get session ID
      const sessionId = await createSession(baseUrl, username, password);
      // Step 2: Get token with session ID
      const token = await getAuthToken(baseUrl, sessionId);
      // Step 3: Create server with the token
      const generatedName = await createServer(baseUrl, token, params);

      if (generatedName) {
        res.status(200).json({ success: true, message: `✅ Server created successfully. Generated Name: ${generatedName}` });
      } else {
        res.status(200).json({ success: true, message: '✅ Server created, but no name generated.' });
      }
    } catch (error) {
      console.error('❌ Error:', error.message);
      res.status(500).json({ success: false, error: error.message });
    }
  });

  // Function to create a session
  async function createSession(baseUrl, username, password) {
    const url = `${baseUrl}/api/authSessions/create`;
    const requestBody = { username, password };

    const response = await axios.post(url, requestBody, {
      headers: { 'Content-Type': 'application/json' },
    });
    return response.data.sessionId;
  }

  // Function to get authentication token
  async function getAuthToken(baseUrl, sessionId) {
    const url = `${baseUrl}/api/auth`;
    const requestBody = { sessionId, type: 0 };

    const response = await axios.post(url, requestBody, {
      headers: { 'Content-Type': 'application/json' },
    });
    return response.data.token;
  }

  // Function to create a server
  async function createServer(baseUrl, token, params) {
    const apiBaseUrl = `${baseUrl}/restApi`;
    const url = `${apiBaseUrl}/api/directoryObjects/executeCustomCommand`;
    const requestHeaders = { 'Adm-Authorization': token };

    // Request body for server creation
    const requestBody = {
      directoryObject: 'DC=ad,DC=global',
      customCommandId: 'c39819b3-4a52-4476-9950-3e2e6c8bfdd5',
      parameters: [
        { type: 'List', name: 'param-Provider', value: params.provider || 'A' },
        { type: 'List', name: 'param-Region', value: params.region || 'F' },
        { type: 'List', name: 'param-Locale', value: params.locale || 'C' },
        { type: 'List', name: 'param-SiteDesignator', value: params.siteDesignator || '1' },
        { type: 'List', name: 'param-Zone', value: params.zone || '1' },
        { type: 'List', name: 'param-Environment', value: params.environment || 'SBX' },
        { type: 'List', name: 'param-OS', value: params.os || 'MS' },
        { type: 'List', name: 'param-Application', value: params.application || 'TES0' },
        { type: 'List', name: 'param-PatchingGroup', value: params.patchGroup || 'DEVTEST' },
        { type: 'Text', name: 'param-Description', value: params.description || 'This is a test' },
        {
          type: 'ADObject',
          name: 'param-SiteOU',
          value: [
            {
              referenceType: 0,
              key: params.ouDN || 'OU=CNSUH,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global',
            },
          ],
        },
        {
          type: 'ADObject',
          name: 'param-ManagedBy',
          value: [
            {
              referenceType: 0,
              key: params.ownerDN || 'CN=Srinivasan, Prasana,OU=INCHE,OU=UsersInternal,OU=Users,OU=HID,OU=SSC,DC=ad,DC=global',
            },
          ],
        },
      ],
    };

    const response = await axios.post(url, requestBody, { headers: requestHeaders });

    // Extract generated name if available
    const messages = response.data?.innerMessages?.innerMessages?.innerMessages || [];
    console.log(messages);
    const generatedName = messages
      .map((msg) => msg.text.match(/Generated name = (.+)/))
      .filter(Boolean)
      .map((match) => match[1]);
    console.log(generatedName);
    return generatedName.length > 0 ? generatedName[0] : null;
  }

 
router.get('/domainjoin/linux', (req, res) => handledomainlinuxSSMRequest(req, res, 'linux_domain_join'));
router.get('/domainunjoin/linux', (req, res) => handledomainlinuxSSMRequest(req, res, 'linux_domain_unjoin'));
router.get('/domainjoin/windos', (req, res) => handledomainSSMRequest(req, res, 'windows_domain_join'));
router.get('/domainunjoin/windos', (req, res) => handledomainSSMRequest(req, res, 'windows_domain_unjoin'));
 
router.post('/ebs-attach', (req, res) => handleEBSSSMRequest(req, res, 'EBS-Attach'));
  // Route to stop an EC2 instance
  router.post('/resize', (req, res) => handleSSMRequest(req, res, 'AWS-ResizeInstance'));
  router.post('/restart', (req, res) => handleSSMRequest(req, res, 'AWS-RestartEC2Instance'));
  router.post('/start', (req, res) => handleSSMRequest(req, res, 'AWS-StartEC2Instance'));
  router.post('/run-terraform', (req, res) => handleRunTerraformRequest(req, res, 'AWS-RunPowerShellScript'));
  router.post('/quicksight', async (req, res) => {
    console.log("req post ");
    console.log(req.body);
const quicksight = new AWS.QuickSight({
    region: 'us-east-1'
  });
  const array =[
  { businessArea: 'EIT', user: 'EIT-USER' },
  { businessArea: 'PACS', user: 'PACS-USER' },
  { businessArea: 'IAMS', user: 'IAMS-USER' },
  { businessArea: 'SI', user: 'SI-USER' },
  { businessArea: 'EAT', user: 'EAT-USER' },
  { businessArea: 'IDT', user: 'IDT-USER' }
];
    try {
      const { businessArea } = req.body;

    const match = array.find(entry => entry.businessArea === businessArea);
        const params = {
            AwsAccountId: '************',
            DashboardId: `${req.body.dashboardId}`,
            IdentityType: 'QUICKSIGHT',
            //UserArn: 'arn:aws:quicksight:us-east-1:************:user/default/HID-Quicksight-Reader/<EMAIL>',
            UserArn: `arn:aws:quicksight:us-east-1:************:user/default/${match.user}`,
            SessionLifetimeInMinutes: 600,
            Namespace: 'default',
            ResetDisabled: true,
            UndoRedoDisabled: true,
            
      
      };
//       const params = {
//   AwsAccountId: '************',
//   Namespace: 'default',
//   AuthorizedResourceArns: [
//     'arn:aws:quicksight:us-east-1:************:dashboard/97dfcff8-e192-43b2-87c5-688261244d2a'
//   ],
//   ExperienceConfiguration: {
//     Dashboard: {
//       InitialDashboardId: '97dfcff8-e192-43b2-87c5-688261244d2a'
//     }
//   },
//   SessionTags: [
//     {
//       Key: 'account_name',
//       Value: 'IAMS Engineering WORKFORCE'
//     }
//   ],
//   SessionLifetimeInMinutes: 600
// };
try {
  
        const result = await quicksight.getDashboardEmbedUrl(params).promise();
        console.log(result);
       
        res.json({ EmbedUrl: result.EmbedUrl });
      } catch (error) {
        console.error('QuickSight Error:', error);
        res.status(500).json({ error: error.message });
      }
    } catch (err) {
      console.error('Error:', err);
      res.status(500).send(err.message);
    }
  });
 router.get('/approver', async (req, res) => {
 // console.log('in approver');
     try {
       const params = {
          Bucket: 'server-provision-application',
             Key: 'Data/approver_user_account_info.csv'
       };
  
       s3.getObject(params, (err, data) => {
         if (err) {
           return res.status(500).send(err.message);
         }
  
         const stream = Readable.from(data.Body);
         const results = [];
         stream.pipe(csvParser())
           .on('data', (row) => {
             results.push({
                 user: row[Object.keys(row)[0]],
                 accounts: row[Object.keys(row)[1]],
                 approver : row[Object.keys(row)[3]],
             });
           })
           .on('end', () => {
              //console.log(results);
             res.json(results);
           })
           .on('error', (err) => {
            console.log(err);
             res.status(500).send(err.message);
           });
       });
     } catch (err) {
      console.log(err);
       res.status(500).send(err.message);
     }
   });
  return router;
};
 