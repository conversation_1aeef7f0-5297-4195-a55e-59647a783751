const express = require('express');
const router = express.Router();
const { STSClient, AssumeRoleCommand } = require('@aws-sdk/client-sts');
const { CloudFormationClient, CreateStackCommand, DescribeStacksCommand } = require('@aws-sdk/client-cloudformation');
const fs = require('fs');
const path = require('path');
const { waitUntil } = require('@aws-sdk/util-waiter');
const AWS = require('aws-sdk');
module.exports = (storeDataInS3forCreate) => {
  let originalCredentials = AWS.config.credentials;
  const deployCloudFormationStack = async (params) => {
    try {
      const assumeRole = async (accountId,params) => {
        const stsClient = new STSClient({ region: params.Region });
       
    let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y =  randomValue.toString();
try{const assumeRoleCommand = new AssumeRoleCommand({
  RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
  RoleSessionName: y
});

try {
  const data = await stsClient.send(assumeRoleCommand);
  return data.Credentials;
} catch (error) {
  
  console.error('Error assuming role:', error);
  throw error;
}}catch(error){

  AWS.config.update({ credentials: null });
  console.error('Error assuming role:', error.code, error.message);
  if (error.code === 'AccessDenied') {
    const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
    console.log(`Retrying in ${delay / 1000} seconds...`);
    await new Promise(resolve => setTimeout(resolve, delay)); 
    const data =  await assumeRole(accountId,params);;
    return data.Credentials;
      // Handle AccessDenied specifically, maybe log the accounts involved
  

}else{
console.error('Error assuming role:', error);
throw error;
}}
      };

      // Initialize AWS with temporary credentials
      const initializeAWS = async (accountId,params) => {
        console.log(accountId);
        const credentials = await assumeRole(accountId,params);
        return new CloudFormationClient({
          region: params.Region,
          credentials: {
            accessKeyId: credentials.AccessKeyId,
            secretAccessKey: credentials.SecretAccessKey,
            sessionToken: credentials.SessionToken
          }
        });
      };
      console.log("params are");
      console.log(params);
      const cloudFormationClient = await initializeAWS(params.accountId,params);
      let yamlfile='null';
      //const  volume = [params.volume1, params.volume2,params.volume3,params.volume4,params.volume5];
      if(params.from=='withdomain'){
        console.log(params.from);
        console.log("with domain");
         yamlfile=fs.readFileSync(path.join(__dirname, '../yaml', 'withdomain.yaml'), 'utf8');
         console.log(yamlfile);
      }else{
        console.log(params.from);
        console.log("with out domain");
         yamlfile=fs.readFileSync(path.join(__dirname, '../yaml', 'withoutdomain.yaml'), 'utf8')
      }
      console.log(params);
      const cloudFormationParams = {
        StackName: params.InstanceName,
       
        TemplateBody: yamlfile,
        Parameters: [
            { ParameterKey: 'InstanceName', ParameterValue: params.InstanceName },
            { ParameterKey: 'InstanceType', ParameterValue: params.InstanceType },
            { ParameterKey: 'AMI', ParameterValue: params.AMI },
            { ParameterKey: 'SecurityGroupIds', ParameterValue: params.securityGroupIds },
            { ParameterKey: 'SubnetId', ParameterValue: params.subnetId },
           
            { ParameterKey: 'CostCenter', ParameterValue: params.CostCenter },
            { ParameterKey: 'CostCenterDescription', ParameterValue: params.CostCenterDescription },
            { ParameterKey: 'SupportTier', ParameterValue: params.SupportTier },
            { ParameterKey: 'SupportTierDescription', ParameterValue: params.SupportTierDescription },
            { ParameterKey: 'InstanceSource', ParameterValue: params.InstanceName },
            { ParameterKey: 'ProvisioningEntity', ParameterValue: params.ProvisioningEntity },
            { ParameterKey: 'ProvisioningJustification', ParameterValue: params.ProvisioningJustification },
            { ParameterKey: 'BusinessArea', ParameterValue: params.BusinessArea },
            { ParameterKey: 'BusinessContact', ParameterValue: params.BusinessContact },
            { ParameterKey: 'BusinessContactEmail', ParameterValue: params.BusinessContactEmail },
            { ParameterKey: 'BusinessSegment', ParameterValue: params.BusinessSegment },
            { ParameterKey: 'BusinessSegmentDescription', ParameterValue: params.BusinessSegmentDescription },
            { ParameterKey: 'TechnicalContact', ParameterValue: params.TechnicalContact },
            { ParameterKey: 'TechnicalContactEmail', ParameterValue: params.TechnicalContactEmail },
            { ParameterKey: 'Environment', ParameterValue: params.Environment },
            { ParameterKey: 'NetworkLocation', ParameterValue: params.NetworkLocation },
            { ParameterKey: 'FunctionalArea', ParameterValue: params.FunctionalArea },
            { ParameterKey: 'ProvisioningEngineer', ParameterValue: params.ProvisioningEngineer },
            { ParameterKey: 'UseBlockDeviceMappings', ParameterValue: params.createOptionalVolume },
    
            // First volume parameters
            { ParameterKey: 'VolumeSize', ParameterValue: (params.volume1?.ebsVolumeSize || 50).toString() },
            { ParameterKey: 'VolumeType', ParameterValue: (params.volume1?.ebsVolumeType || 'gp3') },
            { ParameterKey: 'DeviceName', ParameterValue: (params.volume1?.DeviceName || '/dev/sda1') },
    
            // Conditional parameters for volume 2
            { ParameterKey: 'VolumeSize1', ParameterValue: (params.volume2?.ebsVolumeSize || 1).toString() },
            { ParameterKey: 'VolumeType1', ParameterValue: (params.volume2?.ebsVolumeType || 'gp3') },
            { ParameterKey: 'DeviceName1', ParameterValue: (params.volume2?.DeviceName || '/dev/xvdh') },
    
            // Conditional parameters for volume 3
            { ParameterKey: 'VolumeSize2', ParameterValue: (params.volume3?.ebsVolumeSize || 1).toString() },
            { ParameterKey: 'VolumeType2', ParameterValue: (params.volume3?.ebsVolumeType || 'gp3') },
            { ParameterKey: 'DeviceName2', ParameterValue: (params.volume3?.DeviceName || '/dev/xvdd') },
    
            // Conditional parameters for volume 4
            { ParameterKey: 'VolumeSize3', ParameterValue: (params.volume4?.ebsVolumeSize || 1).toString() },
            { ParameterKey: 'VolumeType3', ParameterValue: (params.volume4?.ebsVolumeType || 'gp3') },
            { ParameterKey: 'DeviceName3', ParameterValue: (params.volume4?.DeviceName || '/dev/xvda') },
    
            // Backup plan parameter formData.mapMigrated
            { ParameterKey: 'mapmigrated', ParameterValue: params.mapMigrated || '' },
            { ParameterKey: 'BackupPlan', ParameterValue: params.BackupPlan || '' }
        ],
        Capabilities: ['CAPABILITY_NAMED_IAM']
    };
      //console.log(cloudFormationParams);
      const startTime = new Date();
      const createStackCommand = new CreateStackCommand(cloudFormationParams);
      const result = await cloudFormationClient.send(createStackCommand);
      console.log('Stack creation initiated:', result);

      // Poll for stack creation completion
      const stackName = params.InstanceName;

      const checkStackStatus = async () => {
        const describeStacksCommand = new DescribeStacksCommand({ StackName: stackName });
        console.log(describeStacksCommand);
        const data = await cloudFormationClient.send(describeStacksCommand); console.log(data);
        const stack = data.Stacks[0];
        if (stack.StackStatus === 'CREATE_COMPLETE') {
          console.log('Stack creation completed');
          return;
        } else if (stack.StackStatus === 'CREATE_FAILED') {
          await storeDataInS3forCreate(params ,"","", 'Stack creation failed',"");
          throw new Error('Stack creation failed');
        } else if (stack.StackStatus === 'ROLLBACK_FAILED') {//ROLLBACK_COMPLETE
          await storeDataInS3forCreate(params ,"","", 'Stack creation failed Roll Back Failed',"");
          throw new Error('Stack creation failed Roll Back Failed');}
          else if (stack.StackStatus === 'ROLLBACK_IN_PROGRESS') {//ROLLBACK_COMPLETE
            await storeDataInS3forCreate(params ,"","", 'Stack creation failed ROLLBACK_IN_PROGRESS Failed',"");
            throw new Error('Stack creation failed ROLLBACK_IN_PROGRESS Failed');}
            else if (stack.StackStatus === 'UPDATE_FAILED') {//ROLLBACK_COMPLETE
              await storeDataInS3forCreate(params ,"","", 'Stack creation failed ROLLBACK_IN_PROGRESS Failed',"");
              throw new Error('Stack creation failed ROLLBACK_IN_PROGRESS Failed');}
              
          else if (stack.StackStatus === 'ROLLBACK_COMPLETE') {//ROLLBACK_COMPLETE
            await storeDataInS3forCreate(params ,"","", 'Stack creation failed ROLLBACK_COMPLETE Failed',"");
            throw new Error('Stack creation failed ROLLBACK_COMPLETE Failed');}
            else if (stack.StackStatus === 'CREATE_FAILED') {//ROLLBACK_COMPLETE
              await storeDataInS3forCreate(params ,"","", 'Stack creation failed CREATE_FAILED Failed',"");
              throw new Error('Stack creation failed CREATE_FAILED Failed');}
              else if (stack.StackStatus === 'UPDATE_ROLLBACK_IN_PROGRESS') {//ROLLBACK_COMPLETE
                await storeDataInS3forCreate(params ,"","", 'Stack creation failed UPDATE_ROLLBACK_IN_PROGRESS Failed',"");
                throw new Error('Stack creation failed UPDATE_ROLLBACK_IN_PROGRESS Failed');}
                else if (stack.StackStatus === 'UPDATE_ROLLBACK_COMPLETE') {//ROLLBACK_COMPLETE
                  await storeDataInS3forCreate(params ,"","", 'Stack creation failed UPDATE_ROLLBACK_COMPLETE Failed',"");
                  throw new Error('Stack creation failed UPDATE_ROLLBACK_COMPLETE Failed');}
        // Wait before polling again
        await new Promise(resolve => setTimeout(resolve, 20000)); // Wait for 20 seconds
        return checkStackStatus(); // Recursive call
      };

       
       
      const finishTime = new Date();
      const stack = await checkStackStatus();
      const describeStacksCommand = new DescribeStacksCommand({ StackName: stackName });
        console.log(describeStacksCommand);
        const data = await cloudFormationClient.send(describeStacksCommand); console.log(data);
        const stack1= data.Stacks[0];
      console.log(cloudFormationParams);
      console.log('result:');
      console.log(stack1);
      console.log(stack1.Outputs);
      // Extract stack outputs
      //const outputs = stack.Outputs || [];
      // console.log('Stack params:', result.Parameters);
      // // console.log('Stack outputs0:', result.Stacks[0].Outputs);
      // console.log('Stack outputs:', stack.Stack.Outputs);
      const logoutput=stack1.Outputs;
      console.log(logoutput);
     // await storeDataInS3forCreate(params ,startTime,finishTime, result,logoutput);
      AWS.config.update({ credentials: originalCredentials });

      return result;
    } catch (error) {
      AWS.config.update({ credentials: originalCredentials });
     // await storeDataInS3forCreate(params ,"","", errorData.error.message,"");
      console.error('Error deploying stack:', error);
      throw error;
    }
  };

  router.post('/', async (req, res) => {
    try {
      const params = req.body;
      console.log(req.body);
      console.log("i am in success");
      const result= await deployCloudFormationStack(params);
      res.status(200).send({ status: 'success', result });
    } catch (error) {
      console.log("i am in  not success");
      console.log(error);
      res.status(500).send({ status: 'not-success', error });
    }
  });

  return router;
};





// const express = require('express');
// const router = express.Router();
// const { STSClient, AssumeRoleCommand } = require('@aws-sdk/client-sts');
// const { CloudFormationClient, CreateStackCommand, DescribeStacksCommand } = require('@aws-sdk/client-cloudformation');
// const fs = require('fs');
// const path = require('path');
// const { waitUntil } = require('@aws-sdk/util-waiter');
// const AWS = require('aws-sdk');
// const csvParser = require('csv-parser');

// const s3 = new AWS.S3();

// module.exports = (storeDataInS3forCreate) => {
//   let originalCredentials = AWS.config.credentials;
//   async function getServerNamesFromCSV(bucketName, fileName) {
//     const params = {
//       Bucket: bucketName,
//       Key: fileName,
//     };
  
//     try {
//       // Fetch the CSV file from S3
//       const data = await s3.getObject(params).promise();
//       const csvContent = data.Body.toString('utf-8'); // Convert buffer to string
  
//       // Initialize an array to hold server names
//       const serverNames = [];
  
//       // Split the CSV content into lines
//       csvContent
//         .split('\n')
//         .forEach((line, index) => {
//           const cells = line.split(',');
  
//           // Skip the header row that contains 'Name'
//        console.log(cells[13]);
  
//           // Push the server name (assumed to be in the first column)
//           if (cells[13] && cells[13].trim()) {
//             serverNames.push(cells[13].trim());
//           }
//         });
  
//       // Log the names for verification
//       console.log("Server names in the CSV:", serverNames);
  
//       return serverNames;
//     } catch (error) {
//       console.error('Error reading CSV from S3:', error);
//       throw error;
//     }
//   }
//   async function checkServerNameNotExistsInList(params) {
//     const { InstanceName } = params; // Get the server name from frontend params
//     const bucketName =  'server-provision-application';
//     const csvKey = 'Data/server.csv';
//     const fileName =  'Data/server.csv';
  
//     try {
//       // Fetch server names from the CSV file in S3
//       const serverNames = await getServerNamesFromCSV(bucketName, fileName);
  
//       // Check if the servername from frontend exists in the CSV list
//       if (serverNames.includes(InstanceName)) {
//         console.log(`${InstanceName} exists in the server list.`);
//         return false; // Name exists in the list
//       } else {
//         console.log(`${InstanceName} is not in the server list.`);
//         return true; // Name does not exist in the list
//       }
//     } catch (error) {
//       console.error('Error in checking server name:', error);
//       throw error;
//     }
//   }
 
//   const deployCloudFormationStack = async (params) => {
   
//     checkServerNameNotExistsInList(params);
//     try {
//       const assumeRole = async (accountId,params) => {
//         const stsClient = new STSClient({ region: params.Region });
       
//     let randomValue = Math.floor(Math.random() * 9000) + 1000;

// // Concatenate the random value to the string
// let y =  randomValue.toString();
// try{const assumeRoleCommand = new AssumeRoleCommand({
//   RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
//   RoleSessionName: y
// });

// try {
//   const data = await stsClient.send(assumeRoleCommand);
//   return data.Credentials;
// } catch (error) {
  
//   console.error('Error assuming role:', error);
//   throw error;
// }}catch(error){

//   AWS.config.update({ credentials: null });
//   console.error('Error assuming role:', error.code, error.message);
//   if (error.code === 'AccessDenied') {
//     const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
//     console.log(`Retrying in ${delay / 1000} seconds...`);
//     await new Promise(resolve => setTimeout(resolve, delay)); 
//     const data =  await assumeRole(accountId,params);;
//     return data.Credentials;
//       // Handle AccessDenied specifically, maybe log the accounts involved
  

// }else{
// console.error('Error assuming role:', error);
// throw error;
// }}
//       };

//       // Initialize AWS with temporary credentials
//       const initializeAWS = async (accountId,params) => {
//         console.log(accountId);
//         const credentials = await assumeRole(accountId,params);
//         return new CloudFormationClient({
//           region: params.Region,
//           credentials: {
//             accessKeyId: credentials.AccessKeyId,
//             secretAccessKey: credentials.SecretAccessKey,
//             sessionToken: credentials.SessionToken
//           }
//         });
//       };
//       console.log("params are");
//       console.log(params);
//       const cloudFormationClient = await initializeAWS(params.accountId,params);
//       let yamlfile='null';
//       //const  volume = [params.volume1, params.volume2,params.volume3,params.volume4,params.volume5];
//       if(params.from=='withdomain'){
//         console.log(params.from);
//         console.log("with domain");
//          yamlfile=fs.readFileSync(path.join(__dirname, '../yaml', 'withdomain.yaml'), 'utf8');
//          console.log(yamlfile);
//       }else{
//         console.log(params.from);
//         console.log("with out domain");
//          yamlfile=fs.readFileSync(path.join(__dirname, '../yaml', 'withoutdomain.yaml'), 'utf8')
//       }
//       console.log(params);
//       const cloudFormationParams = {
//         StackName: params.InstanceName,
       
//         TemplateBody: yamlfile,
//         Parameters: [
//             { ParameterKey: 'InstanceName', ParameterValue: params.InstanceName },
//             { ParameterKey: 'InstanceType', ParameterValue: params.InstanceType },
//             { ParameterKey: 'AMI', ParameterValue: params.AMI },
//             { ParameterKey: 'SecurityGroupIds', ParameterValue: params.securityGroupIds },
//             { ParameterKey: 'SubnetId', ParameterValue: params.subnetId },
           
//             { ParameterKey: 'CostCenter', ParameterValue: params.CostCenter },
//             { ParameterKey: 'CostCenterDescription', ParameterValue: params.CostCenterDescription },
//             { ParameterKey: 'SupportTier', ParameterValue: params.SupportTier },
//             { ParameterKey: 'SupportTierDescription', ParameterValue: params.SupportTierDescription },
//             { ParameterKey: 'InstanceSource', ParameterValue: params.InstanceName },
//             { ParameterKey: 'ProvisioningEntity', ParameterValue: params.ProvisioningEntity },
//             { ParameterKey: 'ProvisioningJustification', ParameterValue: params.ProvisioningJustification },
//             { ParameterKey: 'BusinessArea', ParameterValue: params.BusinessArea },
//             { ParameterKey: 'BusinessContact', ParameterValue: params.BusinessContact },
//             { ParameterKey: 'BusinessContactEmail', ParameterValue: params.BusinessContactEmail },
//             { ParameterKey: 'BusinessSegment', ParameterValue: params.BusinessSegment },
//             { ParameterKey: 'BusinessSegmentDescription', ParameterValue: params.BusinessSegmentDescription },
//             { ParameterKey: 'TechnicalContact', ParameterValue: params.TechnicalContact },
//             { ParameterKey: 'TechnicalContactEmail', ParameterValue: params.TechnicalContactEmail },
//             { ParameterKey: 'Environment', ParameterValue: params.Environment },
//             { ParameterKey: 'NetworkLocation', ParameterValue: params.NetworkLocation },
//             { ParameterKey: 'FunctionalArea', ParameterValue: params.FunctionalArea },
//             { ParameterKey: 'ProvisioningEngineer', ParameterValue: params.ProvisioningEngineer },
//             { ParameterKey: 'UseBlockDeviceMappings', ParameterValue: params.createOptionalVolume },
    
//             // First volume parameters
//             { ParameterKey: 'VolumeSize', ParameterValue: (params.volume1?.ebsVolumeSize || 50).toString() },
//             { ParameterKey: 'VolumeType', ParameterValue: (params.volume1?.ebsVolumeType || 'gp3') },
//             { ParameterKey: 'DeviceName', ParameterValue: (params.volume1?.DeviceName || '/dev/sda1') },
    
//             // Conditional parameters for volume 2
//             { ParameterKey: 'VolumeSize1', ParameterValue: (params.volume2?.ebsVolumeSize || 1).toString() },
//             { ParameterKey: 'VolumeType1', ParameterValue: (params.volume2?.ebsVolumeType || 'gp3') },
//             { ParameterKey: 'DeviceName1', ParameterValue: (params.volume2?.DeviceName || '/dev/xvdh') },
    
//             // Conditional parameters for volume 3
//             { ParameterKey: 'VolumeSize2', ParameterValue: (params.volume3?.ebsVolumeSize || 1).toString() },
//             { ParameterKey: 'VolumeType2', ParameterValue: (params.volume3?.ebsVolumeType || 'gp3') },
//             { ParameterKey: 'DeviceName2', ParameterValue: (params.volume3?.DeviceName || '/dev/xvdd') },
    
//             // Conditional parameters for volume 4
//             { ParameterKey: 'VolumeSize3', ParameterValue: (params.volume4?.ebsVolumeSize || 1).toString() },
//             { ParameterKey: 'VolumeType3', ParameterValue: (params.volume4?.ebsVolumeType || 'gp3') },
//             { ParameterKey: 'DeviceName3', ParameterValue: (params.volume4?.DeviceName || '/dev/xvda') },
    
//             // Backup plan parameter formData.mapMigrated
//             { ParameterKey: 'mapmigrated', ParameterValue: params.mapMigrated || '' },
//             { ParameterKey: 'BackupPlan', ParameterValue: params.BackupPlan || '' }
//         ],
//         Capabilities: ['CAPABILITY_NAMED_IAM']
//     };
//       //console.log(cloudFormationParams);
//       const startTime = new Date();
//       const createStackCommand = new CreateStackCommand(cloudFormationParams);
//       const result = await cloudFormationClient.send(createStackCommand);
//       console.log('Stack creation initiated:', result);

//       // Poll for stack creation completion
//       const stackName = params.InstanceName;

//       const checkStackStatus = async () => {
//         const describeStacksCommand = new DescribeStacksCommand({ StackName: stackName });
//         console.log(describeStacksCommand);
//         const data = await cloudFormationClient.send(describeStacksCommand); console.log(data);
//         const stack = data.Stacks[0];
//         if (stack.StackStatus === 'CREATE_COMPLETE') {
//           console.log('Stack creation completed');
//           return;
//         } else if (stack.StackStatus === 'CREATE_FAILED') {
//           await storeDataInS3forCreate(params ,"","", 'Stack creation failed',"");
//           throw new Error('Stack creation failed');
//         } else if (stack.StackStatus === 'ROLLBACK_FAILED') {//ROLLBACK_COMPLETE
//           await storeDataInS3forCreate(params ,"","", 'Stack creation failed Roll Back Failed',"");
//           throw new Error('Stack creation failed Roll Back Failed');}
//           else if (stack.StackStatus === 'ROLLBACK_IN_PROGRESS') {//ROLLBACK_COMPLETE
//             await storeDataInS3forCreate(params ,"","", 'Stack creation failed ROLLBACK_IN_PROGRESS Failed',"");
//             throw new Error('Stack creation failed ROLLBACK_IN_PROGRESS Failed');}
//             else if (stack.StackStatus === 'UPDATE_FAILED') {//ROLLBACK_COMPLETE
//               await storeDataInS3forCreate(params ,"","", 'Stack creation failed ROLLBACK_IN_PROGRESS Failed',"");
//               throw new Error('Stack creation failed ROLLBACK_IN_PROGRESS Failed');}
              
//           else if (stack.StackStatus === 'ROLLBACK_COMPLETE') {//ROLLBACK_COMPLETE
//             await storeDataInS3forCreate(params ,"","", 'Stack creation failed ROLLBACK_COMPLETE Failed',"");
//             throw new Error('Stack creation failed ROLLBACK_COMPLETE Failed');}
//             else if (stack.StackStatus === 'CREATE_FAILED') {//ROLLBACK_COMPLETE
//               await storeDataInS3forCreate(params ,"","", 'Stack creation failed CREATE_FAILED Failed',"");
//               throw new Error('Stack creation failed CREATE_FAILED Failed');}
//               else if (stack.StackStatus === 'UPDATE_ROLLBACK_IN_PROGRESS') {//ROLLBACK_COMPLETE
//                 await storeDataInS3forCreate(params ,"","", 'Stack creation failed UPDATE_ROLLBACK_IN_PROGRESS Failed',"");
//                 throw new Error('Stack creation failed UPDATE_ROLLBACK_IN_PROGRESS Failed');}
//                 else if (stack.StackStatus === 'UPDATE_ROLLBACK_COMPLETE') {//ROLLBACK_COMPLETE
//                   await storeDataInS3forCreate(params ,"","", 'Stack creation failed UPDATE_ROLLBACK_COMPLETE Failed',"");
//                   throw new Error('Stack creation failed UPDATE_ROLLBACK_COMPLETE Failed');}
//         // Wait before polling again
//         await new Promise(resolve => setTimeout(resolve, 20000)); // Wait for 20 seconds
//         return checkStackStatus(); // Recursive call
//       };

       
       
//       const finishTime = new Date();
//       const stack = await checkStackStatus();
//       const describeStacksCommand = new DescribeStacksCommand({ StackName: stackName });
//         console.log(describeStacksCommand);
//         const data = await cloudFormationClient.send(describeStacksCommand); console.log(data);
//         const stack1= data.Stacks[0];
//       console.log(cloudFormationParams);
//       console.log('result:');
//       console.log(stack1);
//       console.log(stack1.Outputs);
//       // Extract stack outputs
//       //const outputs = stack.Outputs || [];
//       // console.log('Stack params:', result.Parameters);
//       // // console.log('Stack outputs0:', result.Stacks[0].Outputs);
//       // console.log('Stack outputs:', stack.Stack.Outputs);
//       const logoutput=stack1.Outputs;
//       console.log(logoutput);
//      // await storeDataInS3forCreate(params ,startTime,finishTime, result,logoutput);
//       AWS.config.update({ credentials: originalCredentials });

//       return result;
//     } catch (error) {
//       AWS.config.update({ credentials: originalCredentials });
//       await storeDataInS3forCreate(params ,"","", errorData.error.message,"");
//       console.error('Error deploying stack:', error);
//       throw error;
//     }
//   };

//   router.post('/', async (req, res) => {
//     try {
//       const params = req.body;
//       console.log(req.body);
//       console.log("i am in success");
//       const result= await deployCloudFormationStack(params);
//       res.status(200).send({ status: 'success', result });
//     } catch (error) {
//       console.log("i am in  not success");
//       console.log(error);
//       res.status(500).send({ status: 'not-success', error });
//     }
//   });

//   return router;
// };

