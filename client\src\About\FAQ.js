import React from 'react';
import './faq.css'; // Import the CSS file for styling

const FAQ = () => {
  return (
    <div className="faq-container">
      <h3 className="faq-title">Frequently Asked Questions (FAQ)</h3>

      <div className="faq-item">
        <h4>What is U-manage?</h4>
        <p>
        U-manage is an easy-to-use tool for managing your infrastructure. It doesn’t require expert knowledge of AWS, so different team members can handle infrastructure tasks without needing advanced technical skills.
        </p>
      </div>

      <div className="faq-item">
        <h4>Why U-manage?</h4>
        <p>
          It’s built for ease of use, so both experienced admins and newer team members can handle infrastructure management without needing extensive AWS expertise.
        </p>
      </div>

      <div className="faq-item">
        <h4>What is the version of U-manage?</h4>
        <p>
          Currently, U-manage is being released in version v1.11.24.0. This version includes foundational features for managing infrastructure effectively.
        </p>
      </div>

      <div className="faq-item">
        <h4>How do I access the U-manage Portal?</h4>
        <p>
          To access the U-manage Portal, you'll need to connect via Cloudflare VPN since it is an internal portal. Once connected, you can access the portal using the following URL: <a href="https://umanage.eit.hidglobal.com/" target="_blank" rel="noopener noreferrer">https://umanage.eit.hidglobal.com/</a>.
        </p>
      </div>

      <div className="faq-item">
        <h4>What is the preferred browser for accessing the portal?</h4>
        <p>
          The recommended browser is the one where your Active Directory (AD) account is already logged in. This ensures seamless access without repeated sign-ins. If you use a different browser, you’ll need to log in to AD to authenticate.
        </p>
      </div>

      <div className="faq-item">
        <h4>How do I report issues related to U-manage access?</h4>
        <p>
          Report any access or usage issues by creating a ServiceNow ticket. This ticket should be assigned to the U-manage Team.
        </p>
      </div>

      <div className="faq-item">
        <h4>What actions can be done in U-manage?</h4>
        <p>
          U-manage allows actions like provisioning, starting, stopping, and terminating AWS instances.
        </p>
      </div>

      <div className="faq-item">
        <h4>Will the portal time-out if I am inactive for a certain period?</h4>
        <p>
          Yes, the U-manage portal has a 30-minute inactivity timeout. This helps maintain security by automatically signing out users who are inactive.
        </p>
      </div>

      <div className="faq-item">
        <h4>What should I do if "test firstname" appears in the username at the top right?</h4>
        <p>
        If you see "test firstname" in the username at the top right, please navigate to the login page and authenticate yourself to ensure you're properly logged into the portal
        </p>
      </div>

      <div className="faq-item">
        <h4>Will I be notified after starting, stopping, terminating, or provisioning operations?</h4>
        <p>
          Yes, a Mail Notifier Bot will send notifications for these operations. This notification goes to designated contacts, including the Service Initialization Engineer and the business and technical contacts for each operation, keeping everyone informed.
        </p>
      </div>

      <div className="faq-item">
        <h4>What is Service Action?</h4>
        <p>
          A Service Action is an operation performed on an instance, such as starting, stopping, or terminating services. These actions help manage and control the instance’s functionality.
        </p>
      </div>

      <div className="faq-item">
        <h4>What is a Provision Action?</h4>
        <p>
          A Provision Action involves creating a new instance with specified configurations, networks, and tags. It’s the starting point for setting up a new server.
        </p>
      </div>

      <div className="faq-item">
        <h4>What is Start Instance?</h4>
        <p>
          The Start Service Action powers on a previously stopped instance, making it available for use.
        </p>
      </div>

      <div className="faq-item">
        <h4>What is Stop Instance?</h4>
        <p>
          The Stop Service Action shuts down a running instance, saving costs while preserving its configuration.
        </p>
      </div>

      <div className="faq-item">
        <h4>Is it possible to schedule start/stop operations for AWS instances on my own?</h4>
        <p>
          Since we are releasing Version 1, it does not have this capability yet, but it will be included in Version 2.
        </p>
      </div>

      <div className="faq-item">
        <h4>What is the Domain Checker?</h4>
        <p>
          This is a post-server-creation check to validate whether the server is joined to the domain, requiring that the System Manager agent is enabled and running on the server.
        </p>
      </div>

      <div class="faq-item">
    <h4>What is Terminate Instance?</h4>
    <p>
      Terminating an instance permanently deletes it from your AWS environment, releasing all associated resources. While the instance is terminated immediately, backups will be retained for 7 days. After that period, those backups will also be deleted. A ServiceNow ticket is required to initiate the termination process.
    </p>
  </div>

  <div class="faq-item">
    <h4>How do I close the info icon once it’s open?</h4>
    <p>
    You can close the info icon by clicking it again.
    </p>
  </div>


  <div class="faq-item">
    <h4>Do I have to wait 3 to 5 minutes to know the output of the action?</h4>
    <p>
      No, you can leave the web page once you click the button, and the output of your action will be sent by email.
    </p>
  </div>

  <div class="faq-item">
    <h4>What should I do when the accounts or any other drop-down box is empty?</h4>
    <p>
      Don’t refresh the page, wait for 10 seconds to load the data.
    </p>
  </div>

  <div class="faq-item">
    <h4>How do logs work?</h4>
    <p>
      Once any action is taken, whether it’s a success or failure, the details and the action performed are stored in S3. An RPA bot triggers every 5 minutes to send emails based on these logs.
    </p>
  </div>

  <div class="faq-item">
    <h4>What happens if I want to restore a terminated instance?</h4>
    <p>
      You can restore a terminated EC2 instance from the available backups within 7 days; however, please note that the IP address will change during the restoration process.
    </p>
  </div>

  <div class="faq-item">
    <h4>What is Create Windows Instance?</h4>
    <p>
      Create Windows Instance is a provisioning action to set up a new Windows-based server with domain joined.
    </p>
  </div>

  <div class="faq-item">
    <h4>What is Create Linux Instance?</h4>
    <p>
      Create Linux Instance provisions a new Linux-based server with user-specified configurations without domain joined in Version 1.
    </p>
  </div>

  <div class="faq-item">
    <h4>What is Create Windows Instance without Domain Join?</h4>
    <p>
      This action creates a Windows instance that isn’t joined to any domain, providing a standalone server environment.
    </p>
  </div>

  <div class="faq-item">
    <h4>What naming conventions should I follow for the Servername when provisioning an instance?</h4>
    <p>
      U-manage follows a strict naming convention for hostnames, limited to 15 characters. For example, a hostname could look like this:
    </p>
    <ul>
      <li>Cloud Provider: Amazon AWS</li>
      <li>Region: United States</li>
      <li>Locale: East</li>
      <li>Site Designator: 2</li>
      <li>Availability Zone: A</li>
      <li>Environment: AQ1 (Acquisitions 1)</li>
      <li>OS: MS (Microsoft Windows)</li>
      <li>Application Code: SQL Server</li>
      <li>Server Number: 01</li>
    </ul>
  </div>

  <div class="faq-item">
    <h4>What is an AMI?</h4>
    <p>
      An AMI (Amazon Machine Image) is a pre-configured template for launching instances with a specific OS, software, and configurations. In U-manage, when you select an operating system version, it automatically retrieves the latest available AMI from AWS.
    </p>
  </div>

  <div class="faq-item">
    <h4>How do I choose the instance type for an application?</h4>
    <p>
      Choose an instance type based on the application’s required performance, memory, and processing capacity. Reference: <a href="https://umanage.eit.hidglobal.com/help" target="_blank" rel="noopener noreferrer">https://umanage.eit.hidglobal.com/help</a>
    </p>
  </div>

  <div class="faq-item">
    <h4>How can I view the vCPU and memory for the instance type?</h4>
    <p>
      vCPU and memory details for each instance type are available in AWS documentation or through UManage when selecting an instance. This helps in choosing the right instance for workload requirements. Reference: <a href="https://umanage.eit.hidglobal.com/help" target="_blank" rel="noopener noreferrer">https://umanage.eit.hidglobal.com/help</a>
    </p>
  </div>

  <div class="faq-item">
    <h4>Can I provision high-end instance types in AWS?</h4>
    <p>
      Yes, high-end instance types are available, though approval may be required for cost control.
    </p>
  </div>

  <div class="faq-item">
    <h4>What is EBS Volume?</h4>
    <p>
      An EBS Volume (Elastic Block Store) is AWS’s block storage that provides persistent data storage for instances.
    </p>
  </div>

  <div class="faq-item">
    <h4>Which is the preferred EBS Volume?</h4>
    <p>
      General Purpose (gp3) is commonly preferred for its balanced performance and cost, making it suitable for most workloads with reliable throughput. It is also set as the default in the UManage portal, though users have the option to override it.
    </p>
  </div>

  <div class="faq-item">
    <h4>How many volumes can I add to a new provisioned server?</h4>
    <p>
      While AWS allows up to 28 EBS volumes per instance, the UManage portal limits users to adding a maximum of 4 volumes.
    </p>
  </div>

  <div class="faq-item">
    <h4>Can I overwrite the EBS volume type and size?</h4>
    <p>
      Yes, users can customize the type and size of EBS volumes during instance provisioning. This allows for tailored storage based on application needs.
    </p>
  </div>

  <div class="faq-item">
    <h4>What is a VPC?</h4>
    <p>
      A VPC (Virtual Private Cloud) is a network environment where you can deploy resources securely in AWS. It allows network segmentation and control over IP address ranges and routing.
    </p>
  </div>

  <div class="faq-item">
    <h4>What is a Subnet?</h4>
    <p>
      A subnet is a segment within a VPC that allows resources to communicate within specified IP ranges.
    </p>
  </div>

  <div class="faq-item">
    <h4>What is a Security Group, and how can I select Security Groups?</h4>
    <p>
      Security Groups act as virtual firewalls, controlling inbound and outbound traffic for instances. In the UManage portal, default options include management-sg, application-sg, and database-sg, each pre-configured to allow necessary application ports.
    </p>
  </div>

  <div class="faq-item">
    <h4>What are the mandatory tags?</h4>
    <p>
      These tags help in tracking, billing, and organizing resources.
    </p>
    <ul>
      <li><strong>Business Area</strong><br/>
      <em>Purpose:</em> Specifies the organizational area responsible for the instance.<br/>
      <em>Example:</em> OtherBA</li>

      <li><strong>Business Segment</strong><br/>
      <em>Purpose:</em> Defines the financial or business segment associated with the resource.<br/>
      <em>Example:</em> 9000</li>

      <li><strong>Business Segment Description</strong><br/>
      <em>Purpose:</em> Provides additional context about the business segment.<br/>
      <em>Example:</em> HID Global</li>

      <li><strong>Cost Center</strong><br/>
      <em>Purpose:</em> Allocates instance expenses to a specific cost center.<br/>
      <em>Example:</em> 6420</li>

      <li><strong>Cost Center Description</strong><br/>
      <em>Purpose:</em> Describes the cost center for easier identification.<br/>
      <em>Example:</em> Global Infrastructure and NOC</li>

      <li><strong>Functional Area</strong><br/>
      <em>Purpose:</em> Specifies the functional department managing the resource.<br/>
      <em>Example:</em> IT</li>

      <li><strong>Environment</strong><br/>
      <em>Purpose:</em> Identifies the AWS account where the instance will be provisioned.<br/>
      <em>Example:</em> EIT Sandbox</li>

      <li><strong>Backup Plan</strong><br/>
      <em>Purpose:</em> Designates the backup strategy applied to the instance.<br/>
      <em>Example:</em> bronze</li>

      <li><strong>Network Location</strong><br/>
      <em>Purpose:</em> Defines network access restrictions or location (internal/external).<br/>
      <em>Example:</em> Internal</li>

      <li><strong>Support Tier</strong><br/>
      <em>Purpose:</em> Indicates the support level required for the instance.<br/>
      <em>Example:</em> Tier 3</li>

      <li><strong>Support Tier Description (Optional)</strong><br/>
      <em>Purpose:</em> Provides details about the support level, including SLA.<br/>
      <em>Example:</em> SLA: P3</li>

      <li><strong>Map-Migrated (Optional)</strong><br/>
      <em>Purpose:</em> Marks migrated instances with an identifier for tracking.<br/>
      <em>Example:</em> Mig47460</li>
    
 
    <li><strong>Business Contact</strong><br/>
      <em>Purpose:</em> Lists the business owner or contact person for the instance.<br/>
      <em>Example:</em> Firstname Lastname</li>

      <li><strong>Business Contact Mail</strong><br/>
      <em>Purpose:</em> Provides the email address of the business contact.<br/>
      <em>Example:</em> <EMAIL></li>

      <li><strong>Technical Contact</strong><br/>
      <em>Purpose:</em> Identifies the technical owner or support person for the instance.<br/>
      <em>Example:</em> Firstname Lastname</li>

      <li><strong>Technical Contact Mail</strong><br/>
      <em>Purpose:</em> Lists the email of the technical contact.<br/>
      <em>Example:</em> <EMAIL></li>

      <li><strong>Provision Justification</strong><br/>
      <em>Purpose:</em> Justifies the instance creation, often linked to a ServiceNow ticket.<br/>
      <em>Example:</em> ServiceNow Ticket ID: INC123456</li>

      </ul>
  </div>

  <div class="faq-item">
    <h4>Can we provision resources without mandatory tags?</h4>
    <p>No, mandatory tags must be assigned to resources before provisioning. This ensures proper tracking and compliance with organizational policies.</p>
  </div>

  <div class="faq-item">
    <h4>What is Network Tag?</h4>
    <p>The Network Tag categorizes instances based on their network access type, with allowed values of INTERNAL, EXTERNAL, and ISOLATED.</p>
  </div>

  <div class="faq-item">
    <h4>What is Backup Plan Tag?</h4>
    <p>The Backup Plan Tag indicates if backups are enabled and the frequency at which they occur, which is crucial for data protection and recovery planning. Options include:</p>
    <ul>
      <li><strong>Platinum:</strong> Multiple backups daily</li>
      <li><strong>Gold:</strong> Daily backups</li>
      <li><strong>Silver:</strong> Weekly backups</li>
      <li><strong>bronze:</strong> Monthly backups</li>
      <li><strong>N/A:</strong> No backup</li>
    </ul>
  </div>

  <div class="faq-item">
    <h4>What is map-migrated?</h4>
    <p>The Map-Migrated Tag identifies instances that have been migrated, allowing for tracking and mapping to specific business units or locations.</p>
    <table class="migrated-table">
      <thead>
        <tr>
          <th>MPE ID</th>
          <th>HID Translation</th>
          <th>HID Site Codes</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>MigQNZY3CM3YR</td>
          <td>HID Krakow BU</td>
          <td>PLKRA</td>
        </tr>
        <tr>
          <td>MigWAT8P06BFC</td>
          <td>HID PKI IoT Business Unit (Suresnes)</td>
          <td>FRSRE</td>
        </tr>
        <tr>
          <td>Mig80NKPN5IZK</td>
          <td>HID Prague BU</td>
          <td>CZKAR</td>
        </tr>
        <tr>
          <td>MigIM3B3C28WF</td>
          <td>HID Workforce BU (Suresnes)</td>
          <td>FRSRE</td>
        </tr>
        <tr>
          <td>MigXI2OWQVT4D</td>
          <td>HID Authentication BU (Suresnes)</td>
          <td>FRSRE</td>
        </tr>
        <tr>
          <td>MigV3IGSCYWB3</td>
          <td>Long Beach, California and Chennai</td>
          <td>USLOB, USLOE, INCHE, USALB</td>
        </tr>
        <tr>
          <td>MigC5H22Y5OCL</td>
          <td>Ft Lauderdale, Florida and Bangalore</td>
          <td>USFOR, INBLR, INEMB</td>
        </tr>
        <tr>
          <td>Mig47460</td>
          <td>Crossmatch (Fremont, PBG)</td>
          <td>USFRE, USPAL</td>
        </tr>
        <tr>
          <td>Mig47460</td>
          <td>OCI</td>
          <td>USOCI</td>
        </tr>
      </tbody>
    </table>
  </div>

  <div class="faq-item">
    <h4>What is Support Tier and Description Options?</h4>
    <p>The Support Tier and Description options define the level of support and resolution time for issues based on priority:</p>
    <table class="support-tier-table">
      <thead>
        <tr>
          <th>Support Tier</th>
          <th>Support Tier Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>TIER1</td>
          <td>SLA: P1 (less than 4h resolution time)</td>
        </tr>
        <tr>
          <td>TIER2</td>
          <td>SLA: P2 (less than 24h resolution time)</td>
        </tr>
        <tr>
          <td>TIER3</td>
          <td>SLA: P3 (less than 72h resolution time)</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>

   
  );
};

export default FAQ;
