import React from 'react';
import { FaCheckCircle, FaTimesCircle, FaInfoCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import { MdEmail } from "react-icons/md";
import Loading from './assets/Rocket.gif';
import './NotificationStyles.css';

const ImprovedNotification = ({ 
  message, 
  alertMessage, 
  messagestatus, 
  setMessage, 
  setAlertMessage,
  showEmailNotice = true 
}) => {
  
  const handleClose = () => {
    setMessage(null);
    setAlertMessage(null);
  };

  const handleAlertClose = () => {
    setAlertMessage(null);
  };

  if (!message && !alertMessage) return null;

  return (
    <div className="notification-overlay" onClick={(e) => e.target === e.currentTarget && handleClose()}>
      <div className="notification-container1">
        
        {/* Loading Alert */}
        {alertMessage && !message && (
          <div className="alert-card1">
            <div className="alert-header1">
              <div className="loading-icon1">
                <img src={Loading} alt="Processing..." className="loading-gif" />
              </div>
              <p className="alert-message1">{alertMessage}</p>
              <button 
                className="close-button1" 
                onClick={handleAlertClose}
                aria-label="Close notification"
              >
                <IoIosClose />
              </button>
            </div>
          </div>
        )}

        {/* Success/Error Message */}
        {message && (
          <div className={`status-card1 ${messagestatus ? 'success' : 'error'}`}>
            <div className={`status-icon1 ${messagestatus ? 'pop-animation1' : 'shake-animation1'}`}>
              {messagestatus ? (
                <FaCheckCircle size={30} />
              ) : (
                <FaTimesCircle size={30} />
              )}
            </div>
            <p>{message}</p>
            <button
              className="close-button1"
              onClick={handleClose}
              aria-label="Close notification"
            >
              <IoIosClose />
            </button>
          </div>
        )}
      </div>

      {/* Additional Email Notice */}
      {showEmailNotice && (message || alertMessage) && (
        <div className="additional-notification">
          <p>
            <MdEmail />
            You can close this page. A mail will be sent to you with further details.
          </p>
        </div>
      )}
    </div>
  );
};

export default ImprovedNotification;
