const express = require('express');
const XLSX = require('xlsx');
const router = express.Router();

module.exports = (s3) => {
  router.post('/get-excel-data', async (req, res) => {
    const  filename = req.body.filename;//req.body; // Get the filename from the request body
    console.log(filename);
    if (!filename) {
      return res.status(400).send('Filename is required');
    }

    try {
      const params = {
        Bucket: 'server-provision-application',
        //Key: 'tickets/start and stop logs.xlsx',
       // Replace with your bucket name
        Key: `tickets/${filename}.xlsx` // Adjust as necessary
      };
    //   let x =`tickets/${filename}.xlsx`;
    //   console.log(filename);
      //console.log(params);
      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }

        // Read the Excel file from the buffer
        const workbook = XLSX.read(data.Body, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0]; // Get the first sheet
        const rows = XLSX.utils.sheet_to_json(workbook.Sheets[sheetName]);
       // console.log(rows);
        // Process rows as needed
        let results;
        
             
          
            // Handle other file structures if necessary
            results = rows; // Default case: return raw rows
          
          //return results;
        // Return the results
        res.json({ results });
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });

  return router;
};
