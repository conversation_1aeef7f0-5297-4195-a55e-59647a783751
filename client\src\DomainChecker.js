import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './DomainChecker.css';
import HIDlogo from './assets/hidLogo.png';
import Select from 'react-select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser} from '@fortawesome/free-solid-svg-icons';
import Ulogo from './assets/Ulogo.png';
import { useNavigate } from 'react-router-dom';
import Navbar from './Navbar';
import {  FaTimesCircle } from 'react-icons/fa';
import { TbWorldWww } from "react-icons/tb";
import { IoIosClose } from "react-icons/io";
import Loading from './assets/Rocket.gif';
function DomainChecker() {
  const [data, setData] = useState([]);
  const [data1, setData1] = useState([]);
 
  const [accountNames, setAccountNames] = useState([]);
  const [accountId, setAccountId] = useState([]);
  const [regions, setRegions] = useState([]);
  const navigate = useNavigate();
  const [instances, setInstances] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedAccountID, setSelectedAccountID] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedInstance, setSelectedInstance] = useState('');
  const [selectedBusinessContact, setSelectedBusinessContact] = useState('');
  const [message, setMessage] = useState('');
  const [alertMessage, setAlertMessage] = useState("");
  // const [user, setUser] = useState('<EMAIL>');
  const [isAcknowledged, setIsAcknowledged] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [instanceDetails, setInstanceDetails] = useState({});
  const [firstName, setfirstname] = useState('');
   const [user, setUser] = useState(
  {
    email: '<EMAIL>',
    displayName: 'test displayname',
    firstName: 'test firstname'
  });
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
        setUser(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);
  
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/user')
      .then(response => {
        const fetchedData = response.data;
        console.log('Fetched user data:', fetchedData);
        console.log(user);
        const userEntry = fetchedData.find(entry => entry.user === user.email);
        console.log('User entry:', userEntry);
  
        if (userEntry) {
          const accountIds = userEntry.accounts.split(',').map(account => account.trim());
          console.log('Parsed account IDs:', accountIds);
          setAccountId(accountIds);
        } else {
          setAccountId([]);
        }
      })
      .catch(error => {
        console.error('Error fetching user accounts:', error);
      });
  }, [user]);
  
  useEffect(() => {
    if (accountId.length > 0) {
      axios.get('https://umanage.dev.hidglobal.com/api/s3')
        .then(response => {
          let fetchedData = response.data;
          console.log('Fetched S3 data:', fetchedData);
  
          fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
          console.log('Filtered S3 data:', fetchedData);
  
          const uniqueAccounts = [...new Set(fetchedData.map(item => item.AccountName))];
          console.log('Unique account names:', uniqueAccounts);
  
          setData(fetchedData);
          setAccountNames(uniqueAccounts);
        })
        .catch(error => {
          console.error('Error fetching S3 data:', error);
        });
    }
  }, [accountId]);
  // Ef

  useEffect(() => {
    if (selectedAccount) {
      // Filter regions based on selected account
      const filteredData = data.filter(item => item.AccountName === selectedAccount);
      const uniqueRegions = [...new Set(filteredData.map(item => item.Region))];
      setRegions(uniqueRegions);
    }
  }, [selectedAccount, data]);

  useEffect(() => {
    if (selectedRegion && selectedAccount) {
      // Filter instances based on selected region and account
      const filteredData = data.filter(item => item.Region === selectedRegion && item.AccountName === selectedAccount);
      const filteredData1 = filteredData.filter(item => item.state === "running");
      // Update state to include both id and InstanceName
      setInstances(filteredData1.map(item => ({
        id: item.InstanceId,
        label: `${item.InstanceId} - ${item.InstanceName}`, // Combine InstanceId and InstanceName for display
        name: item.InstanceName
      })));
    }
  }, [selectedRegion, selectedAccount, data]);

  const handleInstanceChange = (selectedOption) => {
    setSelectedInstance(selectedOption.id);

    // Find the selected instance using the selected instance ID
    const instance = data.find((inst) => inst.InstanceId === selectedOption.id);
    if (instance) {
      setInstanceDetails({
        InstanceType: instance.InstanceType,
        InstanceName: instance.InstanceName,
        BusinessArea: instance.BusinessArea,
        CostCenter: instance.CostCenter,
        BusinessSegment: instance.BusinessSegment,
        BusinessContactEmail: instance.BusinessContactEmail,
        Environment: instance.Environment,
        SupportTier: instance.SupportTier,
        ProvisioningEngineer: instance.ProvisioningEngineer,
        TechnicalContact: instance.TechnicalContact,
        FunctionalArea: instance.FunctionalArea,
        BackupPlan: instance.BackupPlan,
      });
      setSelectedAccountID(instance.accountId);
    }

    // Set the business contact based on the instance ID
    const businessContact = data.find((item) => item.InstanceId === selectedOption.id)?.BusinessContact;
    setSelectedBusinessContact(businessContact || '');

    // Smooth scroll to the instance details section
    setTimeout(() => {
      document.querySelector('.instance-details').scrollIntoView({ behavior: 'smooth' });
    }, 200);
  };
  
  const handleTriggerSSM = () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    axios.post('https://umanage.dev.hidglobal.com/api/trigger-ssm/run-terraform', {
      instanceId: selectedInstance,
      accountId: selectedAccountID,
      region: selectedRegion,
      businesscontact: selectedBusinessContact,
      email: user.email,
      accountname:selectedAccount,
      firstname:user.firstName,
      instancename:instanceDetails.InstanceName,
      servicenownumber:'0000'
    })
      .then(response => {
        setMessage(` ${response.data.status}`);
      })
      .catch(error => {
        setMessage(`Error: ${error.response ? error.response.data.error : error.message}`);
      });
  };
  async function logout() {
    try {
      // Send a POST request to the backend logout endpoint
      const response = await fetch('/api/logout', {
        method: 'POST',
        credentials: 'include' // Include cookies with the request
      });
      // Check if the response is successful
      if (response.ok) {
        const result = await response.json();
        console.log(result.message); // Log the success message or handle it as needed
   
        // Optionally, redirect the user to a different page or update the UI
        window.location.href = '/login'; // Redirect to the login page or homepage
      } else {
        console.error('Logout failed');
      }
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };
  
  
  return (
    <div className="Stop-App">
      <Navbar />

      <div className="full-page-content">
        {(message||alertMessage) &&<div  className="notification-container">
          {alertMessage && !message && (
        <div className="alert-card">
          <div className="alert-header">
            <div className="loading-icon">
              <img src={Loading} alt="Loading" className="loading-gif" />
            </div>
            <p className="alert-message">{alertMessage}</p>
            <button className="close-button" onClick={() => setAlertMessage(null)}><IoIosClose /></button>
          </div>
        </div>
      )}

        {/* Status Message Card */}
        {message && (
          <div className={`status-card done`}>
            <div className={`status-icon`}>
            <TbWorldWww size={24} />
            </div>
          <p>{message}</p>
          <button className="close-button"onClick={() => {  setMessage(null); setAlertMessage(null);}}><IoIosClose /></button>
            
        
          </div>
        )}
        </div>}
        <h1 className="main-title">Domain Checker</h1>

        <div className="dropdown-container">
          <div className="dropdown-section">
            <h2 className="dropdown-heading">Account Selection</h2>
            <p className="dropdown-description">Select the account to manage instances.</p>
            <select value={selectedAccount} onChange={(e) => setSelectedAccount(e.target.value)}>
              <option value="">Select Account</option>
              {accountNames.map(account => (
                <option key={account} value={account}>{account}</option>
              ))}
            </select>
          </div>

          <div className="dropdown-section">
            <h2 className="dropdown-heading">Region Selection</h2>
            <p className="dropdown-description">Choose the region for the selected account.</p>
            <select value={selectedRegion} onChange={(e) => setSelectedRegion(e.target.value)} disabled={!selectedAccount}>
              <option value="">Select Region</option>
              {regions.map(region => (
                <option key={region} value={region}>{region}</option>
              ))}
            </select>
          </div>
        </div>

        {selectedRegion && (
          <div className="dropdown-section">
            <h2 className="dropdown-heading">Instance Selection</h2>
            <p className="dropdown-description">Pick an instance from the list.</p>
            <Select
              value={instances.find(instance => instance.id === selectedInstance)} // Find the selected instance
              onChange={handleInstanceChange} // Handle instance change
              options={instances} // Options for the dropdown
              isSearchable={true} // Enable search
              placeholder="Search or select an instance"
            />
          </div>
        )}

        {selectedInstance && (
          <div className="instance-details">
          <p><strong>Instance Type <span className="colon">:</span></strong> {instanceDetails.InstanceType}</p>
          <p><strong>Instance Name <span className="colon">:</span></strong> {instanceDetails.InstanceName}</p>
          <p><strong>Business Area <span className="colon">:</span></strong> {instanceDetails.BusinessArea}</p>
          <p><strong>Cost Center<span className="colon">:</span></strong> {instanceDetails.CostCenter}</p>
          <p><strong>Business Segment<span className="colon">:</span></strong> {instanceDetails.BusinessSegment}</p>
          <p><strong>Business Contact Email<span className="colon">:</span></strong> {instanceDetails.BusinessContactEmail}</p>
          <p><strong>Environment<span className="colon">:</span></strong> {instanceDetails.Environment}</p>
          <p><strong>Support Tier<span className="colon">:</span></strong> {instanceDetails.SupportTier}</p>
          <p><strong>Provisioning Engineer<span className="colon">:</span></strong> {instanceDetails.ProvisioningEngineer}</p>
          <p><strong>Technical Contact<span className="colon">:</span></strong> {instanceDetails.TechnicalContact}</p>
          <p><strong>Functional Area<span className="colon">:</span></strong> {instanceDetails.FunctionalArea}</p>
          <p><strong>Backup Plan<span className="colon">:</span></strong> {instanceDetails.BackupPlan}</p>
          </div>
        )}

        {selectedInstance && (
          <div className="checkbox-container">
            <input 
              type="checkbox" 
              id="acknowledge" 
              checked={isAcknowledged} 
              onChange={() => setIsAcknowledged(!isAcknowledged)} 
            />
            <label htmlFor="acknowledge" className="checkbox-label">
              I acknowledge the risks associated with stopping this instance.
            </label>
          </div>
        )}

        <button 
          className="TriggerSSM" 
          disabled={!isAcknowledged || !selectedInstance  || isProcessing} 
          onClick={handleTriggerSSM}
        >
        Check Domain 
        </button>

      </div>
    </div>
  );
}

export default DomainChecker;
