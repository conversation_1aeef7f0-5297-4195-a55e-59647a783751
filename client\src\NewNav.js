import React, { useState } from 'react';
import './MegaMenuNavbar.css';
import HIDlogo from './assets/hidLogo.png';
import Ulogo from './assets/Ulogo.png';
import { FaPlay, FaStop, FaRedo, FaExpandArrowsAlt, FaTrash, FaNetworkWired, FaCalendarAlt, FaWindows, FaLinux, FaHdd, FaQuestionCircle, FaChartBar, FaCommentDots } from 'react-icons/fa';

const user = {
  firstName: 'Test User',
  email: '<EMAIL>',
};

const logout = () => {
  // Implement logout logic
  alert('Logged out!');
};

const serviceActions = [
  { label: 'Start Instance', href: '/start', icon: <FaPlay className="dropdown-icon" />, desc: 'Launches a stopped EC2 instance.' },
  { label: 'Stop Instance', href: '/stop', icon: <FaStop className="dropdown-icon" />, desc: 'Stops a running EC2 instance.' },
  { label: 'Restart Instance', href: '/restart', icon: <FaRedo className="dropdown-icon" />, desc: 'Restarts a running EC2 instance.' },
  { label: 'Resize Instance', href: '/resize', icon: <FaExpandArrowsAlt className="dropdown-icon" />, desc: 'Change the instance type or size.' },
  { label: 'Terminate Instance', href: '/terminate', icon: <FaTrash className="dropdown-icon" />, desc: 'Permanently deletes an EC2 instance.' },
  { label: 'Domain Checker', href: '/DomainChecker', icon: <FaNetworkWired className="dropdown-icon" />, desc: 'Checks domain-join status.' },
  { label: 'Scheduler Instance', href: '/Scheduler', icon: <FaCalendarAlt className="dropdown-icon" />, desc: 'Schedule start/stop for instances.' },
];

const provisionActions = [
  { label: 'Create Windows Instance', href: '/createwindows', icon: <FaWindows className="dropdown-icon" />, desc: 'Provision a new Windows EC2 instance.' },
  { label: 'Create Linux Instance', href: '/createlinux', icon: <FaLinux className="dropdown-icon" />, desc: 'Provision a new Linux EC2 instance.' },
  { label: 'Attach Volume', href: '/attachvolume', icon: <FaHdd className="dropdown-icon" />, desc: 'Attach a storage volume to an instance.' },
  { label: 'Detach Volume', href: '/detachvolume', icon: <FaHdd className="dropdown-icon" />, desc: 'Detach a storage volume from an instance.' },
];

const helpActions = [
  { label: 'Help Page', href: '/help', icon: <FaQuestionCircle className="dropdown-icon" />, desc: 'View documentation and FAQs.' },
  { label: 'Quicksight', href: '/Quicksight', icon: <FaChartBar className="dropdown-icon" />, desc: 'Access AWS Quicksight dashboards.' },
  { label: 'Feedback', href: '/feedback', icon: <FaCommentDots className="dropdown-icon" />, desc: 'Send us your feedback or report issues.' },
];

const NewNav = () => {
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
  const [showPortfolioDropdown, setShowPortfolioDropdown] = useState(false);
  const [showHelpDropdown, setShowHelpDropdown] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  return (
    <div>
      <nav className="navbar-pro">
        <div className="navbar-container-pro no-side-padding">
          <img src={HIDlogo} alt="HID Logo" className="navbar-logo-pro no-left-margin" />

          <div className="navbar-links-center-pro">
            <a href="/" className="navbar-link-pro">Home</a>
            <div
              className="navbar-link-pro"
              onMouseEnter={() => setShowServiceDropdown(true)}
              onMouseLeave={() => setShowServiceDropdown(false)}
            >
              Service Action
              {showServiceDropdown && (
                <div className="dropdown-menu-pro">
                  {serviceActions.map((item) => (
                    <a href={item.href} className="dropdown-item-pro" key={item.label}>
                      <span className="dropdown-icon-wrap">{item.icon}</span>
                      <span className="dropdown-label-desc-wrap">
                        <span className="dropdown-label-pro">{item.label}</span>
                        <span className="dropdown-desc-pro">{item.desc}</span>
                      </span>
                    </a>
                  ))}
                </div>
              )}
            </div>

            <div
              className="navbar-link-pro"
              onMouseEnter={() => setShowPortfolioDropdown(true)}
              onMouseLeave={() => setShowPortfolioDropdown(false)}
            >
              Provision Action
              {showPortfolioDropdown && (
                <div className="dropdown-menu-pro">
                  {provisionActions.map((item) => (
                    <a href={item.href} className="dropdown-item-pro" key={item.label}>
                      <span className="dropdown-icon-wrap">{item.icon}</span>
                      <span className="dropdown-label-desc-wrap">
                        <span className="dropdown-label-pro">{item.label}</span>
                        <span className="dropdown-desc-pro">{item.desc}</span>
                      </span>
                    </a>
                  ))}
                </div>
              )}
            </div>

            <div
              className="navbar-link-pro"
              onMouseEnter={() => setShowHelpDropdown(true)}
              onMouseLeave={() => setShowHelpDropdown(false)}
            >
              Help & Support
              {showHelpDropdown && (
                <div className="dropdown-menu-pro">
                  {helpActions.map((item) => (
                    <a href={item.href} className="dropdown-item-pro" key={item.label}>
                      <span className="dropdown-icon-wrap">{item.icon}</span>
                      <span className="dropdown-label-desc-wrap">
                        <span className="dropdown-label-pro">{item.label}</span>
                        <span className="dropdown-desc-pro">{item.desc}</span>
                      </span>
                    </a>
                  ))}
                </div>
              )}
            </div>
            <a href="/approverzone" className="navbar-link-pro">Approver Zone</a>
          </div>

          <div
            className="navbar-user-pro"
            onMouseEnter={() => setShowUserMenu(true)}
            onMouseLeave={() => setShowUserMenu(false)}
          >
            <span className="user-name-pro">{user.firstName}</span>
            <img src={Ulogo} alt="User Logo" className="user-icon-pro no-right-margin" />
            {showUserMenu && (
              <div className="user-menu-pro">
                <span className="user-email-pro">{user.email}</span>
                <button className="logout-btn-pro" onClick={logout}>Logout</button>
              </div>
            )}
          </div>
        </div>
      </nav>
    </div>
  );
};

export default NewNav;