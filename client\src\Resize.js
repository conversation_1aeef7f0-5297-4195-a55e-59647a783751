import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './Stop.css';
import HIDlogo from './assets/hidLogo.png';
import Select from 'react-select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser} from '@fortawesome/free-solid-svg-icons';
import { MdInfo  } from "react-icons/md";
import Ulogo from './assets/Ulogo.png';
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import Loading from './assets/Rocket.gif';
import Navbar from './Navbar';
function Restart() {
  const [data, setData] = useState([]);
  const navigate = useNavigate();
  const [data1, setData1] = useState([]);
  const [accountNames, setAccountNames] = useState([]);
  const [accountId, setAccountId] = useState([]);
  const [regions, setRegions] = useState([]);
  const [instances, setInstances] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedAccountID, setSelectedAccountID] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedInstance, setSelectedInstance] = useState('');
  const [selectedBusinessContact, setSelectedBusinessContact] = useState('');
  const [message, setMessage] = useState('');
  // const [user, setUser] = useState('<EMAIL>');
  const [isAcknowledged, setIsAcknowledged] = useState(false);
  const [instanceDetails, setInstanceDetails] = useState({});
  const [firstName, setfirstname] = useState('');
  const [alertMessage, setAlertMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const[instanceType, setInstanceType] = useState([]);
    const [instanceOptions, setInstanceOptions] = useState([]);
    const [selectedInstanceType, setSelectedInstanceType] = useState(''); // State for selected instance type
   const [user, setUser] = useState(
 {
    email: '<EMAIL>',
    displayName: 'Guest',
    firstName: 'Guest'
  });
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
        setUser(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);
  
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/user')
      .then(response => {
        const fetchedData = response.data;
        // console.log('Fetched user data:', fetchedData);
        // console.log(user);
        const userEntry = fetchedData.find(entry => entry.user === user.email);
        // console.log('User entry:', userEntry);
  
        if (userEntry) {
          const accountIds = userEntry.accounts.split(',').map(account => account.trim());
          // console.log('Parsed account IDs:', accountIds);
          setAccountId(accountIds);
        } else {
          setAccountId([]);
        }
      })
      .catch(error => {
        // console.error('Error fetching user accounts:', error);
      });
  }, [user]);
  
  useEffect(() => {
    if (accountId.length > 0) {
      axios.get('https://umanage.dev.hidglobal.com/api/s3')   
        .then(response => {
          let fetchedData = response.data;
          const uniqueInstanceTypes = [...new Set(fetchedData.map(item => item.InstanceType))];

          const instanceOptions = uniqueInstanceTypes.map(instanceType => ({
              value: instanceType,
              label: instanceType,
          }));
          fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
          // console.log('Filtered S3 data:', fetchedData);
  
          const uniqueAccounts = [...new Set(fetchedData.map(item => item.AccountName))];
          // console.log('Unique account names:', uniqueAccounts);
          
      setInstanceOptions(instanceOptions);
          setData(fetchedData);
          setAccountNames(uniqueAccounts);
        })
        .catch(error => {
          // console.error('Error fetching S3 data:', error);
        });
    }
  }, [accountId]);
  // Ef

  useEffect(() => {
    if (selectedAccount) {
      // Filter regions based on selected account
      const filteredData = data.filter(item => item.AccountName === selectedAccount);
      const uniqueRegions = [...new Set(filteredData.map(item => item.Region))];
      setRegions(uniqueRegions);
    }
  }, [selectedAccount, data]);

  useEffect(() => {
    if (selectedRegion && selectedAccount) {
      // Filter instances based on selected region and account
      const filteredData = data.filter(item => item.Region === selectedRegion && item.AccountName === selectedAccount);
      const filteredData1 = filteredData.filter(item => item.state === "running");
      // Update state to include both id and InstanceName
      setInstances(filteredData1.map(item => ({
        id: item.InstanceId,
        label: `${item.InstanceId} - ${item.InstanceName}`, // Combine InstanceId and InstanceName for display
        name: item.InstanceName
      })));
    }
  }, [selectedRegion, selectedAccount, data]);

  const handleInstanceChange = (selectedOption) => {
    setSelectedInstance(selectedOption.id);

    // Find the selected instance using the selected instance ID
    const instance = data.find((inst) => inst.InstanceId === selectedOption.id);
    if (instance) {
      setInstanceDetails({
        InstanceType: instance.InstanceType,
        InstanceName: instance.InstanceName,
        BusinessArea: instance.BusinessArea,
        CostCenter: instance.CostCenter,
        BusinessSegment: instance.BusinessSegment,
        BusinessContactEmail: instance.BusinessContactEmail,
        Environment: instance.Environment,
        SupportTier: instance.SupportTier,
        ProvisioningEngineer: instance.ProvisioningEngineer,
        TechnicalContact: instance.TechnicalContact,
        FunctionalArea: instance.FunctionalArea,
        BackupPlan: instance.BackupPlan,
      });
      setSelectedAccountID(instance.accountId);
    }

    // Set the business contact based on the instance ID
    const businessContact = data.find((item) => item.InstanceId === selectedOption.id)?.BusinessContact;
    setSelectedBusinessContact(businessContact || '');

    // Smooth scroll to the instance details section
    setTimeout(() => {
      document.querySelector('.instance-details').scrollIntoView({ behavior: 'smooth' });
    }, 200);
  };
  const[messagestatus, setMessagestatus] = useState();
  const startListeningForUpdates = () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    const url = `https://umanage.dev.hidglobal.com/api/trigger-ssm/restart?instanceId=${selectedInstance}&accountId=${selectedAccountID}&region=${selectedRegion}&businesscontact=${instanceDetails.BusinessContactEmail}&email=${user.email}&accountname=${selectedAccount}&firstname=${user.firstName}&instancename=${instanceDetails.InstanceName}&servicenownumber=0000`;
    //instanceId, region,  accountId, instanceName,group1,group2,group3,group4} = req.body;
    const eventSource = new EventSource(url);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setAlertMessage(data.message);
      if(data.message.substring(0,4)=="Succ")
        { 
          setAlertMessage("");
          
          setMessagestatus(true);
        }
      if(data.message.substring(0,5)=="Error"){
        setAlertMessage("");
        setMessage(data.message);
        setMessagestatus(false);
      }

    };

  }
  const handleTriggerSSM = async () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion!");
    setMessage(""); // Clear previous messages

    try {
        const response = await fetch('https://umanage.dev.hidglobal.com/api/trigger-ssm/resize', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                instanceId: selectedInstance,
                accountId: selectedAccountID,
                region: selectedRegion,
                businesscontact: instanceDetails.BusinessContactEmail,
                email: user.email,
                accountname: selectedAccount,
                firstname: user.firstName, 
                instancename: instanceDetails.InstanceName,
                servicenownumber: '0000',
                instanceType: selectedInstanceType
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Stream the response in chunks
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let receivedText = "";

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            receivedText = decoder.decode(value, { stream: true });
            setAlertMessage(receivedText); // Append new chunks to message
            if(receivedText.substring(0,4)=="Succ")
              { 
                setAlertMessage("");
                
                setMessagestatus(true);
              }
            if(receivedText.substring(0,5)=="Error"){
              setAlertMessage("");
              setMessage(data.message);
              setMessagestatus(false);
            }
      
        }

        setMessagestatus(true);

    } catch (error) {
        setMessage(`Error: ${error.message}`);
        setMessagestatus(false);
    }
};
  const handleTriggerSSM1 = () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    axios.post('https://umanage.dev.hidglobal.com/api/trigger-ssm/restart', {
      instanceId: selectedInstance,
      accountId: selectedAccountID,
      region: selectedRegion,
      businesscontact: instanceDetails.BusinessContactEmail,
      email: user.email,
      accountname:selectedAccount,
      firstname:user.firstName, 
      instancename:instanceDetails.InstanceName,
      servicenownumber:'0000'
    })
    .then(response => {
       
      setMessage(`Instance Restarted Successfully !!!`);
      setMessagestatus(true);
    
      })
    .catch(error => {
      setMessage(`Error: ${error}`);
      setMessagestatus(false);
    });
  };
  async function logout() {
    try {
      // Send a POST request to the backend logout endpoint
      const response = await fetch('/api/logout', {
        method: 'POST',
        credentials: 'include' // Include cookies with the request
      });
      // Check if the response is successful
      if (response.ok) {
        const result = await response.json();
        // // console.log(result.message); // Log the success message or handle it as needed
   
        // Optionally, redirect the user to a different page or update the UI
        window.location.href = '/login'; // Redirect to the login page or homepage
      } else {
        // // console.error('Logout failed');
      }
    } catch (error) {
      // // console.error('Error during logout:', error);
    }
  };
  console.log("instance Type",selectedInstanceType)
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
  const [showPortfolioDropdown, setShowPortfolioDropdown] = useState(false);
  const [showTicketTooltip, setshowTicketTooltip] = useState(false); 
  return (
    <div className="Stop-App">
       <Navbar />
  <div className="full-page-content">
  {(message||alertMessage) &&<div  className="notification-container">
        {alertMessage && !message && (
      <div className="alert-card">
        <div className="alert-header">
          <div className="loading-icon">
            <img src={Loading} alt="Loading" className="loading-gif" />
          </div>
          <p className="alert-message">{alertMessage}</p>
          <button className="close-button" onClick={() => setAlertMessage(null)}><IoIosClose /></button>
        </div>
      </div>
    )}

      {/* Status Message Card */}
      {message && (
        <div className={`status-card ${messagestatus ? 'success' : 'error'}`}>
          <div className={`status-icon ${messagestatus ? 'pop-animation' : 'shake-animation'}`}>
            {messagestatus ? <FaCheckCircle size={24} /> : <FaTimesCircle size={24} />}
          </div>
        <p>{message}</p>
          <button className="close-button"onClick={() => {  setMessage(null); setAlertMessage(null);}}><IoIosClose /></button>
       
        </div>
      )}
      </div>}

  <p className="main-title label-with-icon">
    Restart Instance  </p>
    <div className="info-icon-container">
      <MdInfo
        className="info-icon"
        size={23} // Adjust the size of the icon
        onClick={() => setshowTicketTooltip(!showTicketTooltip)} // Toggle tooltip visibility
      />
    </div>
    {showTicketTooltip && (
      <div className="tooltip-overlay">
        <div className="tooltip-card">
          <p>
            Only running instances are displayed in this list. If your instance is not listed, it might be stopped. 
          </p>
        </div>
      </div>
    )}

  
  {/* Description below the heading */}
  

  <div className="dropdown-container">
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Account Selection</h2>
      <p className="dropdown-description">Select the account to manage instances.</p>
      <select className="form-control" value={selectedAccount} onChange={(e) => setSelectedAccount(e.target.value)}>
        <option value="">Select Account</option>
        {accountNames.map(account => (
          <option key={account} value={account}>{account}</option>
        ))}
      </select>
    </div>

    <div className="dropdown-section">
      <h2 className="dropdown-heading">Region Selection</h2>
      <p className="dropdown-description">Choose the region for the selected account.</p>
      <select className="form-control" value={selectedRegion} onChange={(e) => setSelectedRegion(e.target.value)} disabled={!selectedAccount}>
        <option value="">Select Region</option>
        {regions.map(region => (
          <option key={region} value={region}>{region}</option>
        ))}
      </select>
    </div>
  </div>

  < div className="dropdown-container">
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Instance Selection</h2>
      <p className="dropdown-description">Pick an instance from the list.</p>
      <Select
        value={instances.find(instance => instance.id === selectedInstance)} // Find the selected instance
        onChange={handleInstanceChange} // Handle instance change
        options={instances} // Options for the dropdown
        isSearchable={true} // Enable search
        placeholder="Search or select an instance"
        className="form-control" // Add form-control class
      />
    </div>
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Instance Type Selection</h2>
      <p className="dropdown-description">Choose the type of instance to resize.</p>
      <Select
                    options={instanceOptions}
                    onChange={option => setSelectedInstanceType(option.value)}
                    placeholder="Select an instance"
                    
      />
    </div>
  </div>

  {selectedInstance && (
    <div className="instance-details">
      <p><strong>Instance Type <span className="colon">:</span></strong> {instanceDetails.InstanceType}</p>
      <p><strong>Instance Name <span className="colon">:</span></strong> {instanceDetails.InstanceName}</p>
      <p><strong>Business Area <span className="colon">:</span></strong> {instanceDetails.BusinessArea}</p>
      <p><strong>Cost Center<span className="colon">:</span></strong> {instanceDetails.CostCenter}</p>
      <p><strong>Business Segment<span className="colon">:</span></strong> {instanceDetails.BusinessSegment}</p>
      <p><strong>Business Contact Email<span className="colon">:</span></strong> {instanceDetails.BusinessContactEmail}</p>
      <p><strong>Environment<span className="colon">:</span></strong> {instanceDetails.Environment}</p>
      <p><strong>Support Tier<span className="colon">:</span></strong> {instanceDetails.SupportTier}</p>
      <p><strong>Provisioning Engineer<span className="colon">:</span></strong> {instanceDetails.ProvisioningEngineer}</p>
      <p><strong>Technical Contact<span className="colon">:</span></strong> {instanceDetails.TechnicalContact}</p>
      <p><strong>Functional Area<span className="colon">:</span></strong> {instanceDetails.FunctionalArea}</p>
      <p><strong>Backup Plan<span className="colon">:</span></strong> {instanceDetails.BackupPlan}</p>
    </div>
  )}

  {selectedInstance && (
    <div className="checkbox-container">
      <input 
        type="checkbox" 
        id="acknowledge" 
        checked={isAcknowledged} 
        onChange={() => setIsAcknowledged(!isAcknowledged)} 
      />
      <label htmlFor="acknowledge" className="checkbox-label">
        I acknowledge the risks associated with Restarting this instance.
      </label>
    </div>
  )}

  <button 
    className="TriggerSSM" 
    disabled={!isAcknowledged || !selectedInstance || isProcessing} 
    onClick={handleTriggerSSM}
  >
    Restart Instance
  </button>
  
</div>

    </div>
  );
}

export default Restart;
