// Home.js
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser } from '@fortawesome/free-solid-svg-icons';
import './Home.css';
import HIDlogo from './assets/hidLogo.png';
import BG from './assets/bg-globe.mp4';
import Ulogo from './assets/Ulogo.png';
import { Fade, Slide }  from "react-awesome-reveal";
import Announcement from './Annouoncement';
import Navbar from './Navbar';
const Home = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState(
  {
    email: '<EMAIL>',
    displayName: 'Alagia Siva Ganesh ',
    firstName: 'Alagia Siva Ganesh'
  });
  const [loading, setLoading] = useState(true);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
 
  const [showPortfolioDropdown, setShowPortfolioDropdown] = useState(false);
  const [showPostDropdown, setShowPosteDropdown] = useState(true);
  const [selectedOption, setSelectedOption] = useState('ServiceAction');
  //const user = { firstName: 'Prasana Srinivasan',email: '<EMAIL>'};
   // State to track which card is visible

 
   // Effect to automatically change the visible box every 3 seconds
   
  console.log('User:', user)

  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        console.log(user);
      } catch (error) {
        
        
      }
      setLoading(false);
    }
    checkAuth();
  }, );

  const handleOptionChange = (e) => {
    setSelectedOption(e.target.value);
  };

  

  async function logout() {
    try {
      // Send a POST request to the backend logout endpoint
      const response = await fetch('/api/logout', {
        method: 'POST',
        credentials: 'include' // Include cookies with the request
      });
      // Check if the response is successful
      if (response.ok) {
        const result = await response.json();
        // console.log(result.message); // Log the success message or handle it as needed
   
        // Optionally, redirect the user to a different page or update the UI
        window.location.href = '/login'; // Redirect to the login page or homepage
      } else {
        // console.error('Logout failed');
      }
    } catch (error) {
      // console.error('Error during logout:', error);
    }
  }
   // State to control the popup visibility
  
  return (
    <div className="home-page">
    <Navbar />
{/* 
<nav className="home-navbar">
    <div className="home-navbar-container">
      <div className="home-navbar-logo">
      <img src={HIDlogo} alt="HID Logo" className="home-navbar-logo" />
      </div>
      <div className="home-navbar-links-center">
      <a href="/" className="home-navbar-link">Home</a>
        <div 
          className="home-navbar-link"
          onMouseEnter={() => setShowServiceDropdown(true)}
          onMouseLeave={() => setShowServiceDropdown(false)}
        >
          Service Action
          {showServiceDropdown && (
            <div className="home-dropdown-menu">
              <a href="/start">Start Instance</a>
              <a href="/stop">Stop Instance</a>
              <a href="/terminate">Terminate Instance</a>
              <a href="/DomainChecker">Domain Checker</a>
            </div>
          )}
        </div>

        <div 
          className="home-navbar-link"
          onMouseEnter={() => setShowPortfolioDropdown(true)}
          onMouseLeave={() => setShowPortfolioDropdown(false)}
        >
          Provision Action
          {showPortfolioDropdown && (
            <div className="home-dropdown-menu">
               <a href="/createwindows">Create Windows Instance</a>
              <a href="/createlinux">Create Linux Instance</a>
              
              
            </div>
          )}
        </div>
        <div 
          className="home-navbar-link"
          onMouseEnter={() => setShowPosteDropdown(true)}
          onMouseLeave={() => setShowPosteDropdown(false)}
        >
          Custom Scheduler
          {showPostDropdown && (
            <div className="home-dropdown-menu">
              <a href="/custsched">Custom Scheduler</a>
              <a href="/custschedlogs">Custom Scheduler Logs</a>
              
              
        </div>
          )}
        </div>
        <a href="/help" className="home-navbar-link">Help</a>
        <a href="/approver" className="home-navbar-link">Approver Zone</a>
      </div>

      
      <div className="home-navbar-user"
       onMouseEnter={() => setShowUserMenu(true)} 
       onMouseLeave={() => setShowUserMenu(false)}>
      <span className="home-user-name">Hi, {user.firstName}!</span>
     
      <img src={Ulogo} alt="HID Logo" className="user-icon" />
     
      {showUserMenu && (
        <div className="home-user-menu">
          <span className="user-email">{user.email}</span>
          <button className="home-logout-btn" onClick={logout}>Logout</button>
        </div>
      )}
    </div>
    </div>
  </nav> */}

  
  <Announcement />

  <div className="home-top-section">
    <video autoPlay muted loop playsInline id="bg-video">
        <source src={BG} type="video/mp4" />
        Your browser does not support the video tag.
    </video>
    <div className="home-left-section">
    <h1>U-Manage</h1>
    
  <p>U-Manage is your one-stop solution to manage infrastructure efficiently and effortlessly. It helps you provision, manage, and monitor your servers with ease, while ensuring security and flexibility.</p>
  <button className="home-goto-btn" onClick={() => document.getElementById('services-section').scrollIntoView({ behavior: 'smooth' })}>
    Go to Services
  </button>

  
    </div>
    
    

  
  </div>
  <div id="services-section" className="home-services-section">
  <div className="home-toggle-container">
    <input
      type="radio"
      name="regime"
      id="old"
      checked={selectedOption === 'ServiceAction'}
      onChange={() => setSelectedOption('ServiceAction')}
    />
    <input
      type="radio"
      name="regime"
      id="new"
      checked={selectedOption === 'Portfolio'}
      onChange={() => setSelectedOption('Portfolio')}
    />

    <label htmlFor="old" className="home-toggle-label old">Service Action</label>
    <label htmlFor="new" className="home-toggle-label new">Provision Action</label>

    <div className="home-slider-tab"></div>
  </div>

  <div className={`home-info-cards ${selectedOption === 'ServiceAction' || selectedOption === 'Portfolio' ? 'show' : ''}`}>
    {selectedOption === 'ServiceAction' ? (
      <>
        <div className="home-card">
          <h3>Stop Instance</h3>
          <p>Manage your instances easily by stopping them when not in use.</p>
          <button className="home-card-btn" onClick={() => navigate('/stop')}>Go to Stop Instance</button>
        </div>
        <div className="home-card">
          <h3>Start Instance</h3>
          <p>Start your instances with a click and ensure availability.</p>
          <button className="home-card-btn" onClick={() => navigate('/start')}>Go to Start Instance</button>
        </div>
        <div className="home-card">
          <h3>Terminate Instance</h3>
          <p>Terminate unused instances and backup will remain for next 7 days.</p>
          <button className="home-card-btn" onClick={() => navigate('/terminate')}>Go to Terminate Instance</button>
        </div>
        <div className="home-card">
          <h3>Domain Checker</h3>
          <p>Check for Domain</p>
          <button className="home-card-btn" onClick={() => navigate('/DomainChecker')}>Go to Domain Checker</button>
        </div>
      </>
    ) : (
      <>
      <div className="home-card">
        <h3>Create Windows Instance</h3>
        <p>Create Windows new instances with domain join.</p>
        <button className="home-card-btn" onClick={() => navigate('/createwindows')}>Go to Create Instance</button>
      </div>
      <div className="home-card">
        <h3>Create Linux Instance</h3>
        <p>Create Linux new instances with domain join.</p>
        <button className="home-card-btn" onClick={() => navigate('/createlinux')}>Go to Create Instance</button>
      </div>
      <div className="home-card">
      <h3>Create Instance without Domain Join</h3>
      <p>Create new instances without domain join.</p>
      <button className="home-card-btn" onClick={() => navigate('/createwithoutdomjoin')}>Go to Create Instance</button>
    </div>
    </>
    )}
  </div>
</div>

</div>

  );
};

export default Home;
