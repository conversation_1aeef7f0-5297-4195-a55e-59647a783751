/* Custom styles for the TimePicker input */
.custom-time-picker {
  width: 50%; /* Set the width to half of the page */
}
/* Instance Details Box Styling */
.instance-details-box {
  border: 1px solid #ccc; /* Light gray border */
  border-radius: 8px; /* Rounded corners */
  padding: 20px; /* Inner spacing */
   /* Spacing around the box */
  background-color: #f9f9f9; /* Light background color */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
}

/* Heading for the Box */
.box-heading {
  font-size: 30px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333; /* Darker text color */
}

.schedule-type-box {
  padding: 20px;
  
  margin-bottom: -20px; /* Add padding inside the box */
}
.recurring-schedule-box {
   border: 1px solid #ccc; /* Light gray border */
  border-radius: 8px; /* Rounded corners */
  padding: 20px; /* Inner spacing */
 /* Spacing around the box */
  background-color: #f9f9f9; /* Light background color */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}