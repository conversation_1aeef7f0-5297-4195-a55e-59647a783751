const express = require('express');
const csvParser = require('csv-parser');
const router = express.Router();

module.exports = (s3, Readable) => {
  router.get('/', async (req, res) => {
    try {
      const params = {
        Bucket: 'server-provision-application',
        Key: 'Data/ami_details.csv'
      };

      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }

        const stream = Readable.from(data.Body);
        const results = [];

        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
              Region: row[Object.keys(row)[0]],
              ImageId: row[Object.keys(row)[1]],
              Name: row[Object.keys(row)[2]],
              CreationDate: row[Object.keys(row)[3]],
              Architecture: row[Object.keys(row)[4]],
              CompatibleInstanceTypes: row[Object.keys(row)[5]],
              Platform: row[Object.keys(row)[6]],  // Ensure Platform is pushed here
            });
          })
          .on('end', () => {
            const instances = results.map(item => {
              return {
                ami: item.ImageId,
                instance: item.CompatibleInstanceTypes.split(',').map(type => type.trim()),
                platform: item.Platform // Add Platform to instances
              };
            });

            // Return the results along with instances
            res.json({
              results,
              instances
            });
          })
          .on('error', (err) => {
            res.status(500).send(err.message);
          });
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });

  return router;
};
