
const imaps = require('imap-simple');
const { simpleParser } = require('mailparser');
const XLSX = require('xlsx');

const config = {
  imap: {
    user: '<EMAIL>',
    password: '',
    host: 'relay.assaabloy.net',
    port: 25,
    tls: true,
    authTimeout: 3000
  }
};

// Your filter criteria
const FILTER_FROM = '<EMAIL>';
const SUBJECT_PREFIX = 'AWS-StartEC2Instance';

async function fetchFilteredEmailsAndSaveToExcel() {
  try {
    const connection = await imaps.connect({ imap: config.imap });
    await connection.openBox('INBOX');

    // Search for recent emails
    const delay = 16 * 24 * 3600 * 1000;
    const since = new Date(Date.now() - delay);
    const searchCriteria = ['ALL', ['SINCE', since.toISOString()]];

    const fetchOptions = {
      bodies: ['HEADER', 'TEXT'],
      markSeen: false
    };

    const messages = await connection.search(searchCriteria, fetchOptions);
    const logData = [];

    for (const item of messages) {
      const all = item.parts.find(part => part.which === 'TEXT');
      const parsed = await simpleParser(all.body);

      // Check for sender and subject prefix
      const fromMatches = parsed.from.text.includes(FILTER_FROM);
      const subjectMatches = parsed.subject && parsed.subject.startsWith(SUBJECT_PREFIX);

      if (fromMatches && subjectMatches) {
        logData.push({
          Date: parsed.date,
          Subject: parsed.subject,
          From: parsed.from.text,
          LogContent: parsed.text.trim()
        });
      }
    }

    // Write to Excel
    if (logData.length === 0) {
      console.log('⚠️ No matching emails found.');
    } else {
      const worksheet = XLSX.utils.json_to_sheet(logData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Logs');
      XLSX.writeFile(workbook, 'FilteredEmailLogs.xlsx');
      console.log('✅ Logs exported to FilteredEmailLogs.xlsx');
    }

    connection.end();
  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

fetchFilteredEmailsAndSaveToExcel();
