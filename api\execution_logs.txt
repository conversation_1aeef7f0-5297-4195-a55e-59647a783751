
Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0d6a33236728b162d"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0cdb15a52949a58fb"
  ],
  "Sentinel_Agent_Installation.ExecutionId": [
    "b175a2e7-c9b5-49bb-ad6e-3ed78f762874"
  ],
  "Sentinel_Agent_Installation.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.Status": [
    "Cancelled"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "c97f298f-fbcb-4f0c-a119-a23883af354b"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0129220"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP30/94e5bb10-fb4a-11ef-92c3-0e259c9bd31b\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP30/94e5bb10-fb4a-11ef-92c3-0e259c9bd31b"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Instance_Stack.StackStatusReason": []
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-036d4d3abb5fbe23f"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-02d5c82431bebd782"
  ],
  "Sentinel_Agent_Installation.ExecutionId": [
    "eb9eb13f-5303-406a-9e67-abc79ec2e522"
  ],
  "Sentinel_Agent_Installation.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.Status": [
    "Success"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "3e36db23-a28f-441e-8276-7dc2075218df"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0129219"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVMSSHD24/32cdbb30-fb4a-11ef-829f-0ebd25ef6105\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVMSSHD24/32cdbb30-fb4a-11ef-829f-0ebd25ef6105"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Instance_Stack.StackStatusReason": []
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-008c6f72119506af6"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0f2f14d36ee1b3871"
  ],
  "Sentinel_Agent_Installation.ExecutionId": [
    "89cc1473-78cb-490c-82d3-8f10c81a49f0"
  ],
  "Sentinel_Agent_Installation.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.Status": [
    "Success"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "2e1a9eb2-9cc2-41c9-b806-4295539fd9dd"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0129221"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP30/0a68c040-fb4f-11ef-a350-0afffe9bd9f9\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP30/0a68c040-fb4f-11ef-a350-0afffe9bd9f9"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Instance_Stack.StackStatusReason": []
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0db88cd41080d3f44"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-04d531607f9f2f255"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "3c9ac65e-8e6e-4bd6-aca3-5557b4f4b94b"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0129222"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXLXAPP67/83488e90-fb5f-11ef-949c-0affc962a52f\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXLXAPP67/83488e90-fb5f-11ef-949c-0affc962a52f"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Instance_Stack.StackStatusReason": []
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-06b6877c8501b88f9"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0c410690c2abc7233"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "caa783a4-1959-4fae-a8d2-0c721cc34f1f"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0129223"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVLXAPP68/d2c964d0-fb64-11ef-abc5-122c3986cb61\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVLXAPP68/d2c964d0-fb64-11ef-abc5-122c3986cb61"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Instance_Stack.StackStatusReason": []
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0a030dc45d590b644"
  ],
  "DescribeInstances.VolumeID1": [
    "vol-068e524780a03d347"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0ce5dc793047828e6"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "089463b0-272b-460c-bc35-f1f935115ddf"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0129224"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVLXAPP70/1573ff30-fd71-11ef-949c-0affc962a52f\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVLXAPP70/1573ff30-fd71-11ef-949c-0affc962a52f"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Instance_Stack.StackStatusReason": []
}



Outputs: {
  "Describe_Stack_Resource.InstanceID": [
    "No output available yet because the step is not successfully executed"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0136210"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"DELETE_IN_PROGRESS\",\"StackStatusReason\":\"User Initiated\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSQL25/10a821a0-fda5-11ef-bdb4-02ab3bfa405d\"}"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSQL25/10a821a0-fda5-11ef-bdb4-02ab3bfa405d"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "DELETE_IN_PROGRESS"
  ],
  "Windows_Instance_Stack.StackStatusReason": [
    "User Initiated"
  ]
}



Outputs: {
  "Describe_Stack_Resource.InstanceID": [
    "i-019e2248f6c8bdf44"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0136213"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSQL25/2af12ff0-fda7-11ef-b7f5-06ed152a3e57\"}"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSQL25/2af12ff0-fda7-11ef-b7f5-06ed152a3e57"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Instance_Stack.StackStatusReason": []
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "***********"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0d3ad596eee155c67"
  ],
  "DescribeInstances.VolumeID1": [
    "vol-0f5607cff40362d03"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0ddb02232f903444b"
  ],
  "Linux_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSQL26/c8dcdc80-fe49-11ef-8e50-021f1835f2fb\"}"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSQL26/c8dcdc80-fe49-11ef-8e50-021f1835f2fb"
  ],
  "Linux_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Linux_Instance_Stack.StackStatusReason": [],
  "Snow_Req_Creation.ExecutionId": [
    "66da5966-70d2-420f-b8d6-b6935f0d71eb"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0136271"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0a98af82ba92a3b8d"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-08602002af70c942e"
  ],
  "Linux_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXLXAPP71/5f96d780-fe4e-11ef-8214-0affebcbdce5\"}"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXLXAPP71/5f96d780-fe4e-11ef-8214-0affebcbdce5"
  ],
  "Linux_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Linux_Instance_Stack.StackStatusReason": [],
  "Snow_Req_Creation.ExecutionId": [
    "da36da42-44f4-4133-9254-5a174ec95b2c"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0136274"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.RootVolume": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.ExecutionId": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.Status": [
    "No output available yet because the step is not successfully executed"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "No output available yet because the step is not successfully executed"
  ],
  "Snow_Req_Creation.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "Snow_Req_Creation.Status": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackStatusReason": [
    "No output available yet because the step is not successfully executed"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.RootVolume": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.ExecutionId": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "Sentinel_Agent_Installation.Status": [
    "No output available yet because the step is not successfully executed"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "No output available yet because the step is not successfully executed"
  ],
  "Snow_Req_Creation.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "Snow_Req_Creation.Status": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackStatusReason": [
    "No output available yet because the step is not successfully executed"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-02423b36a3ecd4f53"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0e91c9613801eb135"
  ],
  "Snow_Req_Creation.ExecutionId": [
    "2c0bba31-ccf8-4484-a8dd-c6ee0656ad33"
  ],
  "Snow_Req_Creation.Output": [
    "RITM0136279"
  ],
  "Snow_Req_Creation.Status": [
    "Success"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:871548869783:stack/AAS1ADEVMSUKH01/99bd5a90-fe5a-11ef-8ce1-0a8507d36c07\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:871548869783:stack/AAS1ADEVMSUKH01/99bd5a90-fe5a-11ef-8ce1-0a8507d36c07"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Instance_Stack.StackStatusReason": []
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.RootVolume": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "No output available yet because the step is not successfully executed"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136298"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:eu-central-1:026090511441:stack/AEC1ADEVMSFAR01/a5977610-fe6a-11ef-a246-0a1440b34fe7"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.RootVolume": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "No output available yet because the step is not successfully executed"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136299"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:eu-central-1:026090511441:stack/AEC1ADEVMSFAR01/8afb5e60-fe6b-11ef-a7e3-0aa58e870677"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "33f31a9887dce6d44d698489cebb3500"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0739cdbdef8d6ba2a"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-03e47d2e99a7b86e9"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136304\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136304"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSAF41/b5cb8490-fe74-11ef-bc5f-0233d294503f\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSAF41/b5cb8490-fe74-11ef-bc5f-0233d294503f"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "c5171e5887102ad44d698489cebb3561"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0c3c7bd4b5a802b00"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-033edf47430e14c0c"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136307\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136307"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSAF42/978fe280-fe76-11ef-9941-0204d9ce8353\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSAF42/978fe280-fe76-11ef-9941-0204d9ce8353"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-077a6604d0ac00284"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-06c90a1ebde2eeb41"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVLXSAF43/b4fb34e0-fe77-11ef-a5d5-02f03a378573"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136308"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-05d4208075a260f99"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0ac111eff98732e65"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:eu-central-1:222634390383:stack/AEC1ADEVLXFAR02/284a8e00-fe78-11ef-ad8f-022d534a7d91"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136311"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "f9da1e1887942a141cf1333e3fbb3588"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0be53fc3f332285b9"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-07ce454c11fb6da3a"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136312\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136312"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSAF44/e2ae5060-fe78-11ef-8205-0a162af71911\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSSAF44/e2ae5060-fe78-11ef-8205-0a162af71911"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "***********"
  ],
  "DescribeInstances.RootVolume": [
    "vol-02d5eb5549843d92c"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-00a5894512c5e2373"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:eu-central-1:026090511441:stack/AEC1ADEVLXFAR02/f26a7e80-fe7c-11ef-a913-06943550ab27"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136321"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0e6dbc45291f1c205"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-04b7101073df51a52"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVLXSAF45/27e8f3c0-fe7d-11ef-b15d-0a6cbb923d55"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136323"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-031ae65eaff98719e"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0b9f5c55ad6e2f048"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVLXSAF46/e75163e0-fe7e-11ef-abc0-02904884c033"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136324"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.RootVolume": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "No output available yet because the step is not successfully executed"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136329"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "4f1e675887dc6a141cf1333e3fbb3551"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-08ab1661d153076a4"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-00ba008b61f05e6de"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136348\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136348"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP74/b4359d50-feab-11ef-82c6-0ec3ca325e99\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP74/b4359d50-feab-11ef-82c6-0ec3ca325e99"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "14cdec0987d826541cf1333e3fbb3597"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-047c477d634c86a25"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0ac8f3ab097b00fd8"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136475\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136475"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP89/0ef96130-000b-11f0-b15b-0e7f5e0f8281\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP89/0ef96130-000b-11f0-b15b-0e7f5e0f8281"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "8b9f1d81871c66541cf1333e3fbb3550"
  ],
  "DescribeInstances.PrivateIP": [
    "***********"
  ],
  "DescribeInstances.RootVolume": [
    "vol-01c28fac7b77de410"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0fade0b2c22a6c920"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136497\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136497"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1BDEVMSWKF16/7c80cb80-0029-11f0-9492-0e3ef2e3d071\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1BDEVMSWKF16/7c80cb80-0029-11f0-9492-0e3ef2e3d071"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-04521b83602febe06"
  ],
  "DescribeInstances.VolumeID1": [
    "vol-0e3619f273c442d64"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXLXAPP89/619b5650-004c-11f0-b09e-0e08a6941cab"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Failed"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Success"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136517"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-004b0e845fad11f10"
  ],
  "DescribeInstances.VolumeID1": [
    "vol-093ff8a51cc560f0d"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXLXAPP92/2f676970-0052-11f0-911c-1252e95b2bed"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Failed"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Success"
  ],
  "RetrieveRITM.RITM": [
    "RITM0129227"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0faeb64a248772c61"
  ],
  "DescribeInstances.VolumeID1": [
    "vol-02312223af04e8a16"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVLXAPP91/1e41edb0-0056-11f0-b4f2-0e9448c18623"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Failed"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Success"
  ],
  "RetrieveRITM.RITM": [
    "RITM0129229"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0038c6402f607d47c"
  ],
  "DescribeInstances.VolumeID1": [
    "vol-0bfe2d94d7a9dd6f4"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXLXAPP92/0b63bc40-0098-11f0-92cd-0e9d3da34d91"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Failed"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Success"
  ],
  "RetrieveRITM.RITM": [
    "RITM0129232"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "96ea709987946a541cf1333e3fbb3540"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0b132af41e1cdb98d"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-09761f2d8d5b91740"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136546\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136546"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVMSSBX79/54465e60-00af-11f0-bbfb-0e4947341479\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVMSSBX79/54465e60-00af-11f0-bbfb-0e4947341479"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-099a540f36f6bd83c"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ASBXLXSBX81/da132fe0-00b0-11f0-a860-12c372962275"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Success"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Failed"
  ],
  "RetrieveRITM.RITM": [
    "RITM0129236"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "e0f361998790aa541cf1333e3fbb35db"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0e0d9249bd59a85f8"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-05da529b80a81f6de"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136559\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136559"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:026090554122:stack/AUE1ASBXMSAPP67/61eb11a0-00c8-11f0-b1fd-128f90116c47\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:026090554122:stack/AUE1ASBXMSAPP67/61eb11a0-00c8-11f0-b1fd-128f90116c47"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-05689735865285221"
  ],
  "DescribeInstances.VolumeID1": [
    "vol-047ef4c041df62a6a"
  ],
  "DescribeInstances.VolumeID2": [
    "vol-083c280181c83a8b7"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:026090554122:stack/AUE1AUATLXNES30/643dcfb0-00d2-11f0-83b2-12d9584b067b"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Success"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Success"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136572"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "83bf2122875022d41cf1333e3fbb3576"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-00e0e09865e27c86d"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-05f7885860f39ff84"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136754\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136754"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1BDEVMSPUS31/d49bbcb0-03dc-11f0-9b03-0affd5b25ccf\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1BDEVMSPUS31/d49bbcb0-03dc-11f0-9b03-0affd5b25ccf"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "71797562871422d41cf1333e3fbb3572"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0eda793077bb3a041"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0bc4c5d3479823b47"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0136757\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0136757"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:026090554122:stack/AAS1ADEVMSPUS32/c42f93f0-03e2-11f0-b16d-024061b3619f\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:026090554122:stack/AAS1ADEVMSPUS32/c42f93f0-03e2-11f0-b16d-024061b3619f"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:871548869783:stack/AAS1ADEVLXDEV23/221e16d0-0582-11f0-bf89-0e2f94aefd89\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:871548869783:stack/AAS1ADEVLXDEV23/221e16d0-0582-11f0-bf89-0e2f94aefd89"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ]
}



Outputs: {
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1AUATMSJQC77/e04a4310-0657-11f0-93b3-0afff5ca8829\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1AUATMSJQC77/e04a4310-0657-11f0-93b3-0afff5ca8829"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "**************"
  ],
  "DescribeStackResource.InstanceID": [
    "i-0aa7ecc0419b2df04"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1BUATMSFID55/4e553a70-0664-11f0-90cb-12f19b34de95"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeStackResource.InstanceID": [
    "i-0f94e77a395b90b51"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ADEVMSENG36/cab5ac60-0666-11f0-8fb0-129fb6ae3c55"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "**************"
  ],
  "DescribeStackResource.InstanceID": [
    "i-0adc3d60d34579701"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1BUATMSFID54/52d68a90-0669-11f0-bcd6-0e0775607411"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeStackResource.InstanceID": [
    "i-0f607d486d71506d3"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ADEVMSENG37/8fd008c0-08af-11f0-8ea1-0affd93c7cc7"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeStackResource.InstanceID": [
    "i-09478c62501103f30"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ADEVMSENG36/025a6ec0-08b1-11f0-b399-0affd52ea213"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "350da1a08760ea101cf1333e3fbb35a9"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-095872b0a5a6d8d02"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-00dd59b289da9748c"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137075\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137075"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:904233098874:stack/AUE1AUATMSSCS01/2d730840-08bd-11f0-8386-12ab64dd2afb\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:904233098874:stack/AUE1AUATMSSCS01/2d730840-08bd-11f0-8386-12ab64dd2afb"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "dc9331ac87282e104d698489cebb358d"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-019958a739908c123"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0ed5452071c775d6a"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137076\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137076"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:904233098874:stack/AUE1AUATMSSCS02/2b689570-08c1-11f0-8942-0affc7938aed\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:904233098874:stack/AUE1AUATMSSCS02/2b689570-08c1-11f0-8942-0affc7938aed"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "3a34716087682e104d698489cebb35fd"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-06163c7372a1bbfc4"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-08d62bd768dd3cd6d"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137079\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137079"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:904233098874:stack/AUE1AUATMSSCS05/933f87d0-08c1-11f0-82aa-12fda864a29b\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:904233098874:stack/AUE1AUATMSSCS05/933f87d0-08c1-11f0-82aa-12fda864a29b"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "af14b12087682e104d698489cebb3549"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-07008a0815be0dddf"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0cbbb038daa023a5a"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137078\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137078"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:904233098874:stack/AUE1AUATMSSCS04/81afae50-08c1-11f0-ad11-0affdc783f17\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:904233098874:stack/AUE1AUATMSSCS04/81afae50-08c1-11f0-ad11-0affdc783f17"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "a5e58649872ca6504d698489cebb3505"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-08489114082db0611"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0eed8e1ba530a68c6"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137188\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137188"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1AUATMSSQL04/f8094770-0a04-11f0-bfeb-06ab5054e75d\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1AUATMSSQL04/f8094770-0a04-11f0-bfeb-06ab5054e75d"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-021ce0c82888a9fbd"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1BDEVLXEWF03/a0c8a080-0bbe-11f0-829b-12f009f7eced"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Success"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Success"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137315"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "67b351438760e6d01cf1333e3fbb354a"
  ],
  "DescribeInstances.PrivateIP": [
    "***********"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0b686fabee0ea1c97"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0472d52d444efc71e"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137412\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137412"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:977098988129:stack/AAS1ADEVMSVAC03/4d9b3230-0ec8-11f0-9be3-0a8a6f91daa7\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:977098988129:stack/AAS1ADEVMSVAC03/4d9b3230-0ec8-11f0-9be3-0a8a6f91daa7"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0db50e4207315a432"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVLXAUE01/88fadc40-0efa-11f0-976d-12c69750a077"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Success"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Success"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137445"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "ca7fc0fb872c6a144d698489cebb35f6"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0957abd92f378295d"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0293bf21bc9a33d3e"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137590\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137590"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSENT02/52807880-1073-11f0-9653-02d22378674d\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:222634390383:stack/AAS1ADEVMSENT02/52807880-1073-11f0-9653-02d22378674d"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "fbc2d4c987b8aa541cf1333e3fbb35bd"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-02f20574f28696d74"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0c43f34a17d3f6f97"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137700\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137700"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVMSJEN01/9c426ac0-1382-11f0-90e7-0e2a5d7821b3\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVMSJEN01/9c426ac0-1382-11f0-90e7-0e2a5d7821b3"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "c30d112e1b4a6a10b0c3c9d3604bcb06"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-098d9143586b677e3"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0b38e0ff1c3c6f715"
  ],
  "Isolated_Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ASBXMSUMA01/840f5190-482d-11f0-ab9f-0afffb6739cf\"}"
  ],
  "Isolated_Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Isolated_Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ASBXMSUMA01/840f5190-482d-11f0-ab9f-0afffb6739cf"
  ],
  "Isolated_Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0141580\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0141580"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "359225621bca6a10b0c3c9d3604bcb3b"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0422cd1484db27cf3"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0510370b058345c03"
  ],
  "Isolated_Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ASBXMSUMA05/d33f81b0-4830-11f0-9f8d-122e5b2498f9\"}"
  ],
  "Isolated_Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Isolated_Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ASBXMSUMA05/d33f81b0-4830-11f0-9f8d-122e5b2498f9"
  ],
  "Isolated_Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0141582\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0141582"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "9c35edaa1b0e2a140584cb34604bcb90"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-06f55847a781ab217"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0f1eb9ca474fb83d6"
  ],
  "Isolated_Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ASBXMSUMA09/69672f20-4832-11f0-802f-128178eecf29\"}"
  ],
  "Isolated_Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Isolated_Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:222634390383:stack/AUE1ASBXMSUMA09/69672f20-4832-11f0-802f-128178eecf29"
  ],
  "Isolated_Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0141583\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0141583"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "ac09616a1b0e6a10b0c3c9d3604bcbab"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0c809f6a144bdde46"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-02bd4ff73ffadb60d"
  ],
  "Isolated_Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSUMA06/bdf08080-4834-11f0-9127-12b4c9d12bb5\"}"
  ],
  "Isolated_Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Isolated_Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSUMA06/bdf08080-4834-11f0-9127-12b4c9d12bb5"
  ],
  "Isolated_Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0141584\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0141584"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "098a3a0a1b56ee147a1242e1b24bcb1c"
  ],
  "DescribeInstances.PrivateIP": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.RootVolume": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "No output available yet because the step is not successfully executed"
  ],
  "Domain_Joining.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137492\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137492"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "No output available yet because the step is not successfully executed"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "98db9f8e1b1e62905a32eac2b24bcb53"
  ],
  "DescribeInstances.PrivateIP": [
    "***********"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0d9f1514467069fb2"
  ],
  "DescribeInstances.VolumeID1": [
    "vol-08178c84b6c9f3506"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-027c34c6ddf2ac634"
  ],
  "Domain_Joining.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0137493\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0137493"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "100"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:ap-south-1:145023138156:stack/AAS1ADEVMSPST05/635035a0-5106-11f0-9a33-023f1b89fc95\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:ap-south-1:145023138156:stack/AAS1ADEVMSPST05/635035a0-5106-11f0-9a33-023f1b89fc95"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "1a7b65451b266690b0c3c9d3604bcbfe"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0db34f3aa154a39dc"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0798ac757e2edb014"
  ],
  "Domain_Joining.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0142911\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0142911"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP22/bf333600-5814-11f0-895a-127a2db77b29\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BSBXMSAPP22/bf333600-5814-11f0-895a-127a2db77b29"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "0657f9ae1b2e6e500584cb34604bcbe2"
  ],
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0cd9826d8bd17bf60"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-09b70f59a13bbb72d"
  ],
  "Domain_Joining.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0143084\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0143084"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:871548869783:stack/AUE1ADEVMSDEV03/912aedc0-5bc5-11f0-a819-0affdc341efd\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:871548869783:stack/AUE1ADEVMSDEV03/912aedc0-5bc5-11f0-a819-0affdc341efd"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "DescribeInstances.PrivateIP": [
    "*************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0fc557c2bddcb37d9"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0ac29c870896eb598"
  ],
  "Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:871548869783:stack/AUE1ADEVLXDEV03/a9d455d0-5bc7-11f0-98b0-12a4dc4bfe7b"
  ],
  "Linux_Snow_Agent_Installer.Status": [
    "Success"
  ],
  "Linux_Snow_Agent_Verification.Status": [
    "Success"
  ],
  "RetrieveRITM.RITM": [
    "RITM0143085"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "f22a84531ba26a900584cb34604bcb89"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-08583fcfbc749b808"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-027689b5f2b761c19"
  ],
  "Isolated_Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVMSDEV01/ae434ef0-5d57-11f0-9cb0-0ee4edc8c2cf\"}"
  ],
  "Isolated_Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Isolated_Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVMSDEV01/ae434ef0-5d57-11f0-9cb0-0ee4edc8c2cf"
  ],
  "Isolated_Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0143229\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0143229"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "4e2c40db1ba26a900584cb34604bcb46"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-03af8be728d05c7ea"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-090889c6120f7b0e0"
  ],
  "Domain_Joining.Output": [
    "No output available yet because the step is not successfully executed"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0143230\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0143230"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ],
  "Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVMSDEV03/d1c577d0-5d58-11f0-907e-0afff60a8d81\"}"
  ],
  "Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVMSDEV03/d1c577d0-5d58-11f0-907e-0afff60a8d81"
  ],
  "Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "Windows_Rapid7_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Sentinel_Agent_Verification.Status": [
    "Success"
  ],
  "Windows_Snow_Agent_Verification.Status": [
    "Success"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "5801c9971b622a14b0c3c9d3604bcbef"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0e3a47c2413da557a"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-0facb59349b7dea6b"
  ],
  "Isolated_Linux_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVLXDEV05/1374c0d0-5d79-11f0-a8ef-1272d9449b89\"}"
  ],
  "Isolated_Linux_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Isolated_Linux_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ADEVLXDEV05/1374c0d0-5d79-11f0-a8ef-1272d9449b89"
  ],
  "Isolated_Linux_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0143245\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0143245"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "8"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "4d6505971ba22a14b0c3c9d3604bcbdb"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0f5d54db2f5db1854"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-005a519d8d108a5cd"
  ],
  "Isolated_Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVMSDEV02/c11eb0e0-5d7b-11f0-b34f-0e816c6c9649\"}"
  ],
  "Isolated_Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Isolated_Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1BDEVMSDEV02/c11eb0e0-5d7b-11f0-b34f-0e816c6c9649"
  ],
  "Isolated_Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0143246\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0143246"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ]
}



Outputs: {
  "CreateIncident.incidentSysId": [
    "6fda45571ba6aa900584cb34604bcb8a"
  ],
  "DescribeInstances.PrivateIP": [
    "************"
  ],
  "DescribeInstances.RootVolume": [
    "vol-0a80059e1d5f09490"
  ],
  "DescribeInstances.VolumeID1": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID2": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID3": [
    "No output available yet because the step is not successfully executed"
  ],
  "DescribeInstances.VolumeID4": [
    "No output available yet because the step is not successfully executed"
  ],
  "Describe_Stack_Resource.InstanceID": [
    "i-03e887c9b4929b840"
  ],
  "Isolated_Windows_Instance_Stack.OutputPayload": [
    "{\"StackStatus\":\"CREATE_COMPLETE\",\"StackId\":\"arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ASBXMSDEV06/18410730-5d7f-11f0-bea3-127a6291475f\"}"
  ],
  "Isolated_Windows_Instance_Stack.PrivateIp": [
    "No output available yet because the step is not successfully executed"
  ],
  "Isolated_Windows_Instance_Stack.StackId": [
    "arn:aws:cloudformation:us-east-1:324651370228:stack/AUE1ASBXMSDEV06/18410730-5d7f-11f0-bea3-127a6291475f"
  ],
  "Isolated_Windows_Instance_Stack.StackStatus": [
    "CREATE_COMPLETE"
  ],
  "RetrieveRITM.OutputPayload": [
    "{\"Payload\":{\"RITM\":\"RITM0143252\"}}"
  ],
  "RetrieveRITM.RITM": [
    "RITM0143252"
  ],
  "Validate_Amazom_machine_Image.Validate_VolumeSize": [
    "30"
  ]
}


