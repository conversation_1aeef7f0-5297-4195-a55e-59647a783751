const express = require('express');
const axios = require('axios');
const router = express.Router();

// Ollama proxy routes - Backend connects to localhost:11434, Frontend connects to backend
const OLLAMA_BASE_URL =  'http://localhost:11434';

// Simple test route
router.get('/test', (req, res) => {
  console.log('🧪 Test route called');
  res.json({
    success: true,
    message: 'Ollama routes are working!',
    timestamp: new Date().toISOString()
  });
});

// Test Ollama connection
router.get('/status', async (req, res) => {
  try {
    const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`, {
      timeout: 5000
    });
    
    res.json({
      success: true,
      connected: true,
      models: response.data.models || [],
      message: 'Ollama is running and accessible'
    });
  } catch (error) {
    res.json({
      success: false,
      connected: false,
      error: error.message,
      message: '<PERSON>llama is not accessible. Please ensure it is running on port 11434.'
    });
  }
});

// Get available models
router.get('/models', async (req, res) => {
  try {
    const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`);
    res.json({
      success: true,
      models: response.data.models || []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to fetch models from Ollama'
    });
  }
});

// Generate response (non-streaming)
router.post('/generate', async (req, res) => {
  try {
    console.log('📝 Simple generate request received');
    const { model, prompt, options = {} } = req.body;
    console.log('📤 Request body:', req.body);
    if (!model || !prompt) {
      return res.status(400).json({
        success: false,
        error: 'Model and prompt are required'
      });
    }

    const payload = {
      model,
      prompt,
      stream: false,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 0.9,
        top_k: options.top_k || 10,        // Reduced for speed
        num_predict: options.num_predict || 50,  // Limit response length
        num_ctx: options.num_ctx || 1024,        // Smaller context for speed
        ...options
      }
    };

    console.log('📤 Sending to Ollama URL:', `${OLLAMA_BASE_URL}/api/generate`);
    console.log('📦 Full payload:', JSON.stringify(payload, null, 2));

    const response = await axios.post(`${OLLAMA_BASE_URL}/api/generate`, payload, {
      timeout: 60000 // 5 minutes timeout for LLM generation
    });

    console.log('✅ Ollama response status:', response.status);
    console.log('✅ Ollama response data:', response.data);

    res.json({
      success: true,
      response: response.data.response,
      model: response.data.model,
      done: response.data.done,
      context: response.data.context
    });

  } catch (error) {
    console.error('Ollama generate error:', error.message);
    console.error('❌ Error details:', {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      code: error.code,
      url: `${OLLAMA_BASE_URL}/api/generate`
    });

    res.status(500).json({
      success: false,
      error: error.message,
      message: `Failed to generate response from Ollama: ${error.message}`,
      ollamaUrl: `${OLLAMA_BASE_URL}/api/generate`
    });
  }
});

// Streaming generate response
router.post('/generate/stream', async (req, res) => {
  try {
    const { model, prompt, options = {} } = req.body;
    
    if (!model || !prompt) {
      return res.status(400).json({
        success: false,
        error: 'Model and prompt are required'
      });
    }

    const payload = {
      model,
      prompt,
      stream: true,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 0.9,
        top_k: options.top_k || 40,
        ...options
      }
    };

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    const ollamaResponse = await axios.post(`${OLLAMA_BASE_URL}/api/generate`, payload, {
      responseType: 'stream',
      timeout: 60000
    });

    let fullResponse = '';

    ollamaResponse.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          
          if (data.response) {
            fullResponse += data.response;
            
            // Send chunk to client
            res.write(`data: ${JSON.stringify({
              chunk: data.response,
              fullResponse: fullResponse,
              done: data.done,
              model: data.model
            })}\n\n`);
          }
          
          if (data.done) {
            res.write(`data: ${JSON.stringify({
              done: true,
              fullResponse: fullResponse,
              model: data.model,
              context: data.context
            })}\n\n`);
            res.end();
            return;
          }
        } catch (parseError) {
          console.warn('Failed to parse Ollama chunk:', line);
        }
      }
    });

    ollamaResponse.data.on('end', () => {
      if (!res.headersSent) {
        res.write(`data: ${JSON.stringify({ done: true, fullResponse })}\n\n`);
        res.end();
      }
    });

    ollamaResponse.data.on('error', (error) => {
      console.error('Ollama stream error:', error);
      if (!res.headersSent) {
        res.write(`data: ${JSON.stringify({ 
          error: error.message, 
          done: true 
        })}\n\n`);
        res.end();
      }
    });

    // Handle client disconnect
    req.on('close', () => {
      if (ollamaResponse.data) {
        ollamaResponse.data.destroy();
      }
    });

  } catch (error) {
    console.error('Ollama streaming error:', error.message);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: error.message,
        message: 'Failed to stream response from Ollama'
      });
    }
  }
});

// Chat endpoint with conversation context
router.post('/chat', async (req, res) => {
  try {
    const { model, messages, options = {} } = req.body;
    
    if (!model || !messages || !Array.isArray(messages)) {
      return res.status(400).json({
        success: false,
        error: 'Model and messages array are required'
      });
    }

    // Convert messages to a single prompt
    let prompt = '';
    messages.forEach(msg => {
      if (msg.role === 'system') {
        prompt += `System: ${msg.content}\n\n`;
      } else if (msg.role === 'user') {
        prompt += `Human: ${msg.content}\n\n`;
      } else if (msg.role === 'assistant') {
        prompt += `Assistant: ${msg.content}\n\n`;
      }
    });
    prompt += 'Assistant: ';

    const payload = {
      model,
      prompt,
      stream: false,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 0.9,
        top_k: options.top_k || 40,
        ...options
      }
    };

    const response = await axios.post(`${OLLAMA_BASE_URL}/api/generate`, payload, {
      timeout: 60000
    });

    res.json({
      success: true,
      response: response.data.response,
      model: response.data.model,
      done: response.data.done,
      context: response.data.context
    });

  } catch (error) {
    console.error('Ollama chat error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to process chat request'
    });
  }
});

// Chat with streaming (alternative path for debugging)
router.post('/chatstream', async (req, res) => {
  try {
    console.log('🚀 Received streaming chat request on /chatstream');
    console.log('📤 Request body:', JSON.stringify(req.body, null, 2));
    // ... rest of the function stays the same
  } catch (error) {
    console.error('❌ Chat streaming error:', error.message);
    res.status(500).json({ success: false, error: error.message });
  }
});

// Chat with streaming (original path)
router.post('/chat/stream', async (req, res) => {
  try {
    console.log('🚀 Received streaming chat request');
    console.log('📤 Request body:', JSON.stringify(req.body, null, 2));

    const { model, messages, options = {} } = req.body;

    if (!model || !messages || !Array.isArray(messages)) {
      console.error('❌ Invalid request - missing model or messages');
      return res.status(400).json({
        success: false,
        error: 'Model and messages array are required'
      });
    }

    console.log('✅ Request validation passed');
    console.log('🤖 Using model:', model);
    console.log('💬 Messages count:', messages.length);

    // Simple: Just use the last user message as prompt
    const lastMessage = messages[messages.length - 1];
    const prompt = lastMessage.content;

    console.log('📝 Generated prompt:', prompt.substring(0, 200) + '...');

    const payload = {
      model,
      prompt,
      stream: true,
      options: {
        temperature: options.temperature || 0.7,
        top_p: options.top_p || 0.9,
        top_k: options.top_k || 40,
        ...options
      }
    };

    console.log('📦 Ollama payload:', JSON.stringify(payload, null, 2));

    // Set up Server-Sent Events
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    console.log('📡 Sending request to Ollama:', `${OLLAMA_BASE_URL}/api/generate`);

    const ollamaResponse = await axios.post(`${OLLAMA_BASE_URL}/api/generate`, payload, {
      responseType: 'stream',
      timeout: 300000 // 5 minutes for streaming
    });

    console.log('✅ Ollama response received, starting stream...');

    let fullResponse = '';

    ollamaResponse.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n').filter(line => line.trim());
      console.log('📦 Received chunk lines:', lines.length);

      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          console.log('📊 Parsed chunk data:', data);

          if (data.response) {
            fullResponse += data.response;
            console.log('📤 Sending to client:', data.response);

            res.write(`data: ${JSON.stringify({
              chunk: data.response,
              fullResponse: fullResponse,
              done: data.done,
              model: data.model
            })}\n\n`);
          }

          if (data.done) {
            console.log('🏁 Stream completed, full response length:', fullResponse.length);
            res.write(`data: ${JSON.stringify({
              done: true,
              fullResponse: fullResponse,
              model: data.model
            })}\n\n`);
            res.end();
            return;
          }
        } catch (parseError) {
          console.warn('⚠️ Failed to parse Ollama chunk:', line, parseError.message);
        }
      }
    });

    ollamaResponse.data.on('end', () => {
      console.log('📡 Ollama stream ended');
      if (!res.headersSent) {
        res.write(`data: ${JSON.stringify({ done: true, fullResponse })}\n\n`);
        res.end();
      }
    });

    ollamaResponse.data.on('error', (error) => {
      console.error('❌ Ollama stream error:', error);
      if (!res.headersSent) {
        res.write(`data: ${JSON.stringify({
          error: error.message,
          done: true
        })}\n\n`);
        res.end();
      }
    });

    req.on('close', () => {
      console.log('🔌 Client disconnected');
      if (ollamaResponse.data) {
        ollamaResponse.data.destroy();
      }
    });

  } catch (error) {
    console.error('❌ Chat streaming error:', error.message);
    console.error('❌ Error stack:', error.stack);
    if (!res.headersSent) {
      res.status(500).json({
        success: false,
        error: error.message,
        message: 'Failed to stream chat response'
      });
    }
  }
});

// Pull/download a model
router.post('/pull', async (req, res) => {
  try {
    const { name } = req.body;
    
    if (!name) {
      return res.status(400).json({
        success: false,
        error: 'Model name is required'
      });
    }

    // Set up Server-Sent Events for progress updates
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*'
    });

    const ollamaResponse = await axios.post(`${OLLAMA_BASE_URL}/api/pull`, 
      { name }, 
      { responseType: 'stream' }
    );

    ollamaResponse.data.on('data', (chunk) => {
      const lines = chunk.toString().split('\n').filter(line => line.trim());
      
      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          res.write(`data: ${JSON.stringify(data)}\n\n`);
          
          if (data.status === 'success') {
            res.end();
            return;
          }
        } catch (parseError) {
          console.warn('Failed to parse pull progress:', line);
        }
      }
    });

    ollamaResponse.data.on('end', () => {
      res.end();
    });

    ollamaResponse.data.on('error', (error) => {
      res.write(`data: ${JSON.stringify({ 
        error: error.message 
      })}\n\n`);
      res.end();
    });

  } catch (error) {
    console.error('Ollama pull error:', error.message);
    res.status(500).json({
      success: false,
      error: error.message,
      message: 'Failed to pull model'
    });
  }
});

module.exports = router;
