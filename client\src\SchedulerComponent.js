// SchedulerComponent.js
//import React, { useState } from 'react';
// import axios from 'axios';

// const SchedulerComponent = () => {
//     const [scheduleExpression, setScheduleExpression] = useState('');
//     const [target, setTarget] = useState('');
//     const [parameters, setParameters] = useState('');
//     const [region, setRegion] = useState('');
//     const [firstname, setFirstname] = useState('');
//     const [accountId, setAccountId] = useState('');
//     const [message, setMessage] = useState('');
//     const [error, setError] = useState('');

//     const handleSubmit = async (e) => {
//         e.preventDefault();
//         setMessage('');
//         setError('');

//         try {
//             const response = await axios.post('https://umanage.dev.hidglobal.com/api/schedule/Schedule', {
//                 scheduleExpression,
//                 target,
//                 parameters: JSON.parse(parameters), // Expecting JSON input
//                 region,
//                 firstname,
//                 accountId,
//             });
//             setMessage('Scheduler created successfully: ' + response.data.ruleArn);
//         } catch (error) {
//             setError('Error scheduling: ' + error.response?.data?.error || error.message);
//         }
//     };

//     return (
//         <div>
//             <h2>Task Scheduler</h2>
//             <form onSubmit={handleSubmit}>
//                 <div>
//                     <label>
//                         Schedule Expression:
//                         <input
//                             type="text"
//                             value={scheduleExpression}
//                             onChange={(e) => setScheduleExpression(e.target.value)}
//                             placeholder="e.g. rate(5 minutes)"
//                             required
//                         />
//                     </label>
//                 </div>
//                 <div>
//                     <label>
//                         Target (SSM Document Name):
//                         <input
//                             type="text"
//                             value={target}
//                             onChange={(e) => setTarget(e.target.value)}
//                             required
//                         />
//                     </label>
//                 </div>
//                 <div>
//                     <label>
//                         Parameters (JSON):
//                         <textarea
//                             value={parameters}
//                             onChange={(e) => setParameters(e.target.value)}
//                             placeholder='{"param1": ["value1"], "param2": ["value2"]}'
//                             required
//                         />
//                     </label>
//                 </div>
//                 <div>
//                     <label>
//                         Region:
//                         <input
//                             type="text"
//                             value={region}
//                             onChange={(e) => setRegion(e.target.value)}
//                             required
//                         />
//                     </label>
//                 </div>
//                 <div>
//                     <label>
//                         First Name:
//                         <input
//                             type="text"
//                             value={firstname}
//                             onChange={(e) => setFirstname(e.target.value)}
//                             required
//                         />
//                     </label>
//                 </div>
//                 <div>
//                     <label>
//                         Account ID:
//                         <input
//                             type="text"
//                             value={accountId}
//                             onChange={(e) => setAccountId(e.target.value)}
//                             required
//                         />
//                     </label>
//                 </div>
//                 <button type="submit">Schedule Task</button>
//             </form>
//             {message && <p style={{ color: 'green' }}>{message}</p>}
//             {error && <p style={{ color: 'red' }}>{error}</p>}
//         </div>
//     );
// };

// export default SchedulerComponent;
import React, { useState } from 'react';
import axios from 'axios';

const SchedulerComponent= () => {
  const [accountId, setAccountId] = useState('');
  const [instanceIds, setInstanceIds] = useState('');
  const [region, setRegion] = useState('');
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setMessage('');
    setError('');

    // Split instance IDs by comma and trim spaces
    const idsArray = instanceIds.split(',').map(id => id.trim()).filter(id => id);

    if (!accountId || idsArray.length === 0) {
      setError('Please provide an account ID and at least one instance ID.');
      return;
    }

    try {
      const response = await axios.post('https://umanage.dev.hidglobal.com/api/schedule/AddSchedule', {
        accountId,
        instanceIds: idsArray,
        region:region,
        email:'<EMAIL>',
        filename:'us-pacific.xlsx'
      });

      setMessage(response.data.message);
    } catch (err) {
      setError(err.response?.data?.error || 'An error occurred while updating the Excel file.');
    }
  };

  return (
    <div>
      <h2>Update Instance IDs</h2>
      <form onSubmit={handleSubmit}>
        <div>
          <label>
            Account ID:
            <input
              type="text"
              value={accountId}
              onChange={(e) => setAccountId(e.target.value)}
              required
            />
          </label>
        </div>
        <div>
          <label>
            Region:
            <input
              type="text"
              value={region}
              onChange={(e) => setRegion(e.target.value)}
              required
            />
          </label>
        </div>
        <div>
          <label>
            Instance IDs (comma separated):
            <input
              type="text"
              value={instanceIds}
              onChange={(e) => setInstanceIds(e.target.value)}
              required
            />
          </label>
        </div>
        <button type="submit">Update Excel</button>
      </form>
      {message && <p style={{ color: 'green' }}>{message}</p>}
      {error && <p style={{ color: 'red' }}>{error}</p>}
    </div>
  );
};

export default SchedulerComponent;
