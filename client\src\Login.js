import React from 'react';

import './Login.css';
import hidLogo from './assets/hidLogo.png'; 
import AWSlogo from './assets/AWS_logo.png'; 
 
function Login() {
  
  const handleLogin = () => {
    // Redirect to your backend endpoint that initiates SAML authentication
    window.location.href = 'https://umanage.dev.hidglobal.com/'; // Adjust this to your actual login URL
  };
 
  return (
   <div className="login-page">
    <div className="login-container">
      <div className="login-left">
        <img src={hidLogo} alt="HID Logo" className="login-logo" />
        <h2>Sign in to HID

        </h2>
      </div>
      <div className="login-right">
        <img src={AWSlogo} alt="AWS Cloud" className="cloud-image" />
        <button onClick={handleLogin} className="login-button">
          AWS Login
        </button>
      </div>
    </div>
  </div>

  );
}
 
export default Login;