.aboutabout-container {
    display: flex;
    flex-direction: column;
    margin-left: 15%;
    margin-top: 13%;
    justify-content: space-between; /* Ensures content is spaced properly */
    padding: 20px;
    background-color: #1e3a8a; /* Light background color */
    border-radius: 8px; /* Rounded corners */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
    height: 50%;
    width:70%; /* Full viewport height */
    overflow: hidden; /* Prevent scrolling */
    position: relative; /* For positioning footer and logo */
  }
  .aboutabout-container h2
  {
    color: #ffffff !important; /* Explicitly set white color for these elements */
    text-align: center;
    font-weight: bold !important; /* Remove underline from the mailto link */
}
.aboutabout-container p
{
    color: #cfc8c8 !important; /* Explicitly set white color for these elements */
     /* Remove underline from the mailto link */
}
.aboutabout-footer span
{
    color: #ffffff !important; /* Explicitly set white color for these elements */
     /* Remove underline from the mailto link */
}
.aboutabout-footer a {
    color: #cfc8c8 !important; /* Explicitly set white color for these elements */
     /* Remove underline from the mailto link */
}
  .aboutabout-content {
    flex: 1; /* Allows content to grow and take available space */
  }
  
  .aboutabout-footer {
    display: flex;
    justify-content: space-between; /* Space out footer elements */
    align-items: center;
    font-size: 12px; /* Adjust as needed */
    width: 100%; /* Full width */
    padding-bottom: 10px; /* Space at the bottom */
  }
  
  .version-info {
    color: #6c757d; /* Subtle gray color */
  }
  
  .support-info {
    color: #6c757d; /* Subtle gray color */
    text-align: center;
    width: 100%; /* Center align within the footer */
  }
  
  .logo-container {
    position: absolute;
    bottom: 10px; /* Align with footer */
    right: 20px; 
    /* Align with container padding */
  }
  
  .logo {
    width: 80px; /* Adjust the logo size as needed */
    height: auto;
    border-radius: 10px;
    border: 3px solid white; /* Add a white border around the logo */
     /* Optional: Add padding inside the border to create space between the logo and the border */
    background-color:#ffffff; /* Ensure the background is transparent, so the logo doesn't blend with the border */
}