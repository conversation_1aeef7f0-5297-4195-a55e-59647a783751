/* General Page Styling */

/* Page Title */
.page-title {
    font-size: 36px;
    font-weight: bold;
    color: #02569b;
    margin-bottom: 20px;
}

/* Form Description */
.form-description {
    font-size: 14px;
    color: #666;
    margin-top: 5px;
}

/* Radio Button Group */
.radio-group {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: bold;
    color: #333;
    cursor: pointer;
}

.radio-option input[type="radio"] {
    accent-color: #007bff;
    width: 18px;
    height: 18px;
}

.new-volume-config {
    margin-top: 20px;
    padding: 15px;
    background: #f7f7f7;
    border: 1px solid #ddd;
    border-radius: 8px;
}

.config-title {
    font-size: 20px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
}

.submit-button {
    width: 200px;
    padding: 12px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    font-size: 16px;
    transition: background-color 0.3s;
}

.submit-button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
}

.submit-button:hover:enabled {
    background-color: #0056b3;
}

.message {
    margin-top: 20px;
    font-weight: bold;
    color: green;
    font-size: 16px;
}