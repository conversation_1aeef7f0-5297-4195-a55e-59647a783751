.faq-container {
    
    margin: 0 auto;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  }
  
  .faq-title {
    font-size: 2.5rem;
    color: #00549b;
    margin-bottom: 20px;
    text-align: center;
  }
  
  .faq-item {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #ffffff;
    border-radius: 5px;
    border: 1px solid #ddd;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .faq-item h4 {
    margin: 0 0 10px;
    color: #333;
    font-weight: bold;
  }
  
  .faq-item p, .faq-item ul {
    margin: 0;
    color: #555;
    line-height: 1.6;
  }
  
  .faq-item a {
    color: #00549b;
    text-decoration: none;
  }
  
  .faq-item a:hover {
    text-decoration: underline;
  }
  
  .migrated-table, .support-tier-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
  }
  
  .migrated-table th, .migrated-table td,
  .support-tier-table th, .support-tier-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }
  
  .migrated-table th, .support-tier-table th {
    background-color: #f2f2f2;
    color: #333;
  }
  