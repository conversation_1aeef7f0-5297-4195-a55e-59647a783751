import React from 'react';
import './AboutStart.css'; 

const AboutResize = () => {
    return (
        <div className="about-start-container">
            <h2 className="about-start-title">Resize Action</h2>
            <p className="about-start-description">
                The Resize action is used to change the <strong>instance type </strong> of an existing virtual machine. This is useful when your workload requirements
                 have changed — for example, upgrading to a larger instance for more performance or downgrading to save costs. Resizing allows you to 
                 scale resources like CPU, memory, and network performance without launching a new instance, helping optimize both performance and budget.
             <br></br>
            <strong>Note: </strong> When resizing an instance, it will be automatically restarted. The process involves stopping the instance, applying the new instance type, and then starting it again.
            </p>
            <h3 className="about-start-subtitle">Steps to Resize an Instance</h3>
            <ol className="about-start-steps">
              <li className="about-start-step">
                <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
                <div className="about-start-image-container">
                  {/* <img src={Start} alt="Select Account Sample" className="about-start-image" /> */}
                </div>
              </li>
              <li className="about-start-step">
                <strong>Select the Region:</strong> Pick the region associated with that account, as instance availability is region-specific.
              </li>
              <li className="about-start-step">
                <strong>Select the Instance:</strong> Locate the instance by its ID or Name for easy identification.
                
              </li>
              <li className="about-start-step">
                <strong>Select the Instance Type:</strong>Select the desired instance type to match your application's performance and resource requirements.
                
              </li>
              <li className="about-start-step">
                <strong>Review Details:</strong> Confirm the instance details, such as configuration and status.
              </li>
              <li className="about-start-step">
                <strong>Acknowledge:</strong> Check the acknowledgment box to confirm your understanding of the action.
              </li>
              <li className="about-start-step">
                <strong>Click the "Resize" Button:</strong> Execute the Resize action. You can either wait in the portal for a 
                status update or log out, as a confirmation email will be sent indicating the result of the action.
              </li>
            </ol>
            
          {/* </div>
        <div className="about-section"> */}
            {/* <h1>About Resize</h1>
            <p>
                The Resize feature allows you to change the instance type of your running or stopped instances. 
                This is useful for scaling up or down based on workload requirements.
            </p>
            <h2>Key Features</h2>
            <ul>
                <li>Resize instances to a larger or smaller type.</li>
                <li>Support for multiple instance families.</li>
                <li>Ensure minimal downtime during resizing.</li>
            </ul>
            <h2>How to Use</h2>
            <ol>
                <li>Navigate to the Resize page.</li>
                <li>Select the account, availability zone, and instance.</li>
                <li>Choose the new instance type.</li>
                <li>Confirm the resize operation to apply the changes.</li>
            </ol> */}
        </div>
    );
};

export default AboutResize;