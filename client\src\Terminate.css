/* Start-App Specific Styles */
.Start-App {
  
    background-color: #f0f8ff;
  }
  
  
  .start-heading {
    margin-top: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #003366;
  }
  
  .start-form-group {
    margin-bottom: 15px;
  }
  
  .start-label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
  }
  
  .start-select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
  }
  
  .start-trigger-btn {
    background-color: #03274a;
    color: white;
    border: none;
    padding: 10px 20px;
    cursor: pointer;
    font-weight: bold;
  }
  
  .start-message {
    margin-top: 10px;
    color: #28a745;
    font-weight: bold;
  }
  /* Align Radio Buttons with H2 */
.radio-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}



.radio-group {
  display: flex;
  align-items: center;
  gap: 15px;
}

.radio-option {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #333;
}

.radio-option input[type="radio"] {
  margin-right: 8px;
  accent-color: #1976d2;
}

.dropdown-description {
  font-size: 14px;
  color: #666;
}
