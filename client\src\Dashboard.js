import React, { useRef, useState ,useEffect} from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { FaRegEye, FaRegEyeSlash } from "react-icons/fa"; 
import './Dashboard.css';
import Navbar from './Navbar';
import Select from 'react-select';
import { QuickSightEmbedding } from 'amazon-quicksight-embedding-sdk';
import { createEmbeddingContext } from 'amazon-quicksight-embedding-sdk';
function Dashboard() {
  const containerRef = useRef(null);
  const [loading, setLoading] = useState(false);
   const navigate = useNavigate();
  const [error, setError] = useState('');
  const [user, setUser] = useState(
    {
      email: '<EMAIL>',
      displayName: 'test displayname',
      firstName: 'test firstname'
    });
  const [accountId, setAccountId] = useState([]);
  const [accountNames, setAccountNames] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedDashboard, setSelectedDashboard] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const regions = ['us-east-1', 'us-west-2', 'eu-west-1'];
   const[dashboardId, setDashboardId] = useState('');
  const [accounts, setAccounts] = useState([]); // State to store accounts fetched from the API
  const [businessAreaOptions, setBusinessAreaOptions] = useState([]);
  const [selectedBA, setSelectedBA] = useState('');
   const [approverRole,setApproverRole] =useState(''); 
  const [isSelectionVisible, setIsSelectionVisible] = useState(true); // State to toggle visibility of selection section
   const dashboardIds = [];
   if (selectedBA) {
    dashboardIds.push(
    // { value: '23d4146c-4579-4caf-858b-07f254071aba', label: 'U-manage Metrics' },
    { value: '7e600713-7d11-4c56-bff4-b09f3ba562f9', label: 'Resource Inventory' },
    // { value: '00ae3106-34f8-416f-a8d0-6a266514fe7a', label: 'Migration Dashboard' },
    );
  
  // Add restricted dashboards if approverRole is 'Approver'
  if (approverRole === 'Approver') {
    dashboardIds.push(
      { value: 'f45c2bfe-edcf-4f0c-bd58-1a06d76d0163', label: 'U-Access Request & AWS Users Dashboard' },
      { value: '12b68754-816d-487d-ae1d-821e9b7f8962', label: 'CUDOS Dashboard' }
    );
  }
}
  
  console.log(dashboardIds); // Verify the dashboard IDs

console.log(dashboardIds);

const BusinessArea = [
  { account_id: '***********', business_area: 'SI' },
  { account_id: '***********', business_area: 'SI' },
  { account_id: '***********', business_area: 'IAMS' },
  { account_id: '***********', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'SI' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '***********', business_area: 'PACS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IDT' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'IDT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '***********', business_area: 'EAT' },
  { account_id: '***********', business_area: 'EAT' },
  { account_id: '***********', business_area: 'EIT' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'SI' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'IAMS' },
];
const handleToggleVisibility = () => {
  setIsSelectionVisible(!isSelectionVisible);
};


 // State to store selected business area
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
         // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);
  
  useEffect(() => {
    let x=[];
    axios.get('https://umanage.dev.hidglobal.com/api/user')
      .then(response => {
        const fetchedData = response.data;
        // console.log(user);
        // console.log(fetchedData); 
         //console.log('Fetched user data:', fetchedData);
       // console.log(user);
        const userEntry = fetchedData.find(entry => entry.user === user.email);
        //console.log('User entry:', userEntry);
  
        if (userEntry) {
          const accountIds = userEntry.accounts.split(',').map(account => account.trim());
          //console.log(accountIds);
          x=accountIds;
          // console.log('Parsed account IDs:', accountIds);
          setAccountId(accountIds);
        } else {
          setAccountId([]);
        }
      })
      .catch(error => {
        // console.error('Error fetching user accounts:', error);
      });
      axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
      .then((response) => {
        const uniqueAccounts = Array.from(new Set(response.data.map(item => item.CreateAccountId)))
           .map(accountId => response.data.find(item => item.CreateAccountId === accountId));
           let fetchedData = response.data;
          
          fetchedData = uniqueAccounts.filter(item => x.includes(item.CreateAccountId));
             //console.log('Filtered S3 data:', fetchedData);
    
            //const uniqueAccounts = [...new Set(fetchedData.map(item => item.CreateAccountName))];
             //console.log('Unique account names:', uniqueAccounts);
        setAccounts(fetchedData); // Store the entire account object
        //console.log("account"+accounts);
      })
      .catch((error) => {
        // console.error('Error fetching accounts:', error);
      });
  }, [user]);
  console.log('Account IDs:', accountId); // Log the account IDs for debugging
  useEffect(() => {
    if (accountId.length > 0) {
      // Filter business areas based on selected account IDs
      const filteredBusinessAreas = BusinessArea.filter((item) =>
        accountId.includes(item.account_id)
      ).map((item) => ({
        value: item.business_area,
        label: item.business_area,
      }));

      // Remove duplicates from businessAreaOptions
      const uniqueBusinessAreas = Array.from(
        new Set(filteredBusinessAreas.map((option) => option.value))
      ).map((value) => ({
        value,
        label: value,
      }));

      setBusinessAreaOptions(uniqueBusinessAreas);
    } else {
      setBusinessAreaOptions([]);
    }
  }, [accountId]);

  console.log('Business Area Options:', businessAreaOptions);
   // Log the business area options for debugging
   const [test, setTest] = useState([]);
   useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/trigger-ssm/approver')
      .then(response => {
        const fetchedData = response.data;
        setTest(fetchedData);
        // Filter fetchedData based on user.email
        const userSpecificData = fetchedData.filter(data => data.user === user.email && data.approver!=='');
        setTest(userSpecificData);
        setApproverRole(userSpecificData[0].approver);
        // Set ApproverRole if user-specific data exists
      })
      .catch(error => {
        console.error('Error fetching approver data:', error);
      });
  }, [user.email]);
  console.log("All data",test);
  console.log('Approver Role:', approverRole); 
  console.log("Selected Account:", selectedAccount);
  console.log("Selected Dashboard:", selectedDashboard);
  useEffect(() => {
    if (accountId.length > 0) {
      axios.get('https://umanage.dev.hidglobal.com/api/s3')
        .then(response => {
          let fetchedData = response.data;
           //console.log('Fetched S3 data:', fetchedData);
  
          fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
           //console.log('Filtered S3 data:', fetchedData);
  
          const uniqueAccounts = [...new Set(fetchedData.map(item => item.AccountName))];
           //console.log('Unique account names:', uniqueAccounts);
  
          
          setAccountNames(uniqueAccounts);
        })
        .catch(error => {
          // console.error('Error fetching S3 data:', error);
        });
    }
  }, [accountId]);
  const handleEmbed = async () => {
    setLoading(true);
    setError('');
  
    try {
      const response = await fetch('https://umanage.dev.hidglobal.com/api/trigger-ssm/quicksight', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ servicenownumber: '0000', businessArea: selectedBA, dashboardId: selectedDashboard }),
      });
  
      const data = await response.json();
  
      if (!data.EmbedUrl) {
        setError('Embed URL not found in response.');
        return;
      }
  
      console.log('in fetch');
      console.log(data.EmbedUrl);
  
      // Clear the container before embedding the new dashboard
      if (containerRef.current) {
        containerRef.current.innerHTML = ''; // Clear the container
      }
  
      const embeddingContext = await createEmbeddingContext(); // ✅ Correct usage
  
      embeddingContext.embedDashboard({
        url: data.EmbedUrl,
        container: containerRef.current,
        height: '800px',
        width: '100%',
        scrolling: 'yes',
        footerPaddingEnabled: true,
        printEnabled: false,
        resizeHeightOnSizeChangedEvent: true,
      });
    } catch (err) {
      console.error('Error embedding dashboard:', err);
      setError('Failed to load dashboard.');
    } finally {
      setLoading(false);
    }
  };
  const handleBusinessAreaChange = (selectedOption) => {
    setSelectedBA(selectedOption.value); // Update selected account
    setSelectedDashboard(''); // Reset selected dashboard when account changes
  };
  const handleDashboardChange = (selectedOption) => {
    setSelectedDashboard(selectedOption.value); // Update selected dashboard
  }
  return (
    <div>
    <Navbar />

   
      {/* Button Outside the Container */}
      

      {/* Selection & Submit Section */}
      {isSelectionVisible && (
        <div className="selection-section">
          {/* Button Inside the Container */}
          <button className="toggle-button-inside" onClick={handleToggleVisibility}>
            <FaRegEyeSlash />
          </button>

          <div className="dropdown-section">
            <h2 className="dropdown-heading">Business Area & Dashboard Selection</h2>
            <p className="dropdown-description">Choose the business area and dashboard to view.</p>
            <div className="dropdown-row">
              <Select
                className="re-select"
                value={businessAreaOptions.find(option => option.value === selectedBA)}
                onChange={handleBusinessAreaChange}
                options={businessAreaOptions}
                isSearchable={true}
                placeholder="Search or select a Business Area"
              />
              <Select
                className="re-select"
                value={dashboardIds.find(option => option.value === selectedDashboard)}
                onChange={handleDashboardChange}
                options={dashboardIds}
                isSearchable={true}
                placeholder="Search or select a Dashboard"
              />
            </div>
            <button className="submit-button" onClick={handleEmbed} disabled={!selectedBA || !selectedDashboard}>
              Submit
            </button>
          </div>
        </div>
      )}

      {/* Dashboard Section */}
      <div className="dashboard-view">
      {!isSelectionVisible && (
        <button className="toggle-button-outside" onClick={handleToggleVisibility}>
          <FaRegEye />
        </button>
      )}
        <h2 className="dashboard-heading">Dashboard View</h2>
        <p className="dashboard-description">Your selected dashboard will appear here.</p>
        <div
        id="dashboardContainer"
        ref={containerRef}
        style={{ width: '100%', height: '800px', border: '1px solid #ccc' }}
      />
      </div>
    </div>
  
  );
}

export default Dashboard;
