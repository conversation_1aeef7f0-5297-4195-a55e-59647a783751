import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import axios from 'axios';

import { DataGrid } from '@mui/x-data-grid';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import DeleteOutlineSharpIcon from '@mui/icons-material/DeleteOutlineSharp';
import CircularProgress from '@mui/material/CircularProgress';
import Loading from './assets/Rocket.gif';
import { IoIosClose } from "react-icons/io";

const Scheduler = () => {
    const [logs, setLogs] = useState([]);
     const [user, setUser] = useState( {
        email: '<EMAIL>',
        displayName: 'Guest',
        firstName: 'Guest'
      });
    const [mySchedules, setMySchedules] = useState([]);
    const [myScheduleList, setMyScheduleList] = useState([]);
    const [removeMessage, setremoveMessage] = useState('');
    const [removeError, setremoveError] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [alertMessage, setAlertMessage] = useState(''); 
    const [message, setMessage] = useState('');
    const [messagestatus, setMessagestatus] = useState(false);
    const [accountId, setAccountId] = useState([]);
    useEffect(() => {
        async function checkAuth() {
          try {
            const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
            setUser(response.data.user);
          } catch (error) {
            // Set user to null in case of an error
          }
        }
        checkAuth();
      }, []);
      useEffect(() => {
        axios.get('https://umanage.dev.hidglobal.com/api/user')
          .then(response => {
            const fetchedData = response.data;
             //console.log('Fetched user data:', fetchedData);
           // console.log(user);
            const userEntry = fetchedData.find(entry => entry.user === user.email);
             //console.log('User entry:', userEntry);
      
            if (userEntry) {
              const accountIds = userEntry.accounts.split(',').map(account => account.trim());
              // console.log('Parsed account IDs:', accountIds);
              setAccountId(accountIds);
            } else {
              setAccountId([]);
            }
            
          })
          .catch(error => {
            // console.error('Error fetching user accounts:', error);
          });
          axios.get('https://umanage.dev.hidglobal.com/api/user')
          .then(response => {const fetchedData = response.data;
                const filteredSchedules = fetchedData.filter(schedule =>
                  accountId.includes(String(schedule.accountId))
                );
                fetchedData.forEach(schedule => {
                  console.log('Schedule Account ID:', schedule.accountId);
                  console.log('Account ID Array:', accountId);
                  console.log('Match Found:', accountId.includes(schedule.accountId));
                });
                setMySchedules(fetchedData);
                
              }) 
              .catch(error => {
                // console.error('Error fetching user accounts:', error);
              });
            
            
      }, [user]);
    console.log('Account IDs:', accountId); // Log the account IDs to verify 
      
    
    useEffect(() => {
      let intervalId;
    
      async function fetchSchedules() {
        try {
          const response = await axios.get('https://umanage.dev.hidglobal.com/api/customschedule/CustomscheduleLogs/');
          const fetchedData = response.data;
    
          // Filter schedules where accountId matches
          const filteredSchedules = fetchedData.filter(schedule =>
            accountId.includes(String(schedule.accountId))
          );
    
          setMySchedules(filteredSchedules);
        } catch (error) {
          console.error('Error fetching schedules:', error);
        }
      }
    
      // Fetch schedules initially
      fetchSchedules();
    
      // Set up interval to refresh every 5 seconds
      intervalId = setInterval(fetchSchedules, 5000);
    
      // Cleanup interval on component unmount
      return () => clearInterval(intervalId);
    }, [user.email, accountId]);
     
      useEffect(() => {
        // Check if "************" exists in the accountId array
        const accountExists = accountId.includes("************");
      
        // Log true or false
        console.log('Does account ID "************" exist?', accountExists);
      }, [accountId]);
      console.log('My Schedules:', mySchedules);
      const handleRemoveClick = async (schedule) => {
        setIsProcessing(true);
        setAlertMessage("Awaiting Process Completion!");
        setMessage(""); // Clear previous messages
    
        try {
            const response = await fetch('https://umanage.dev.hidglobal.com/api/customschedule/RemovecustomSchedule', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                 
            InstanceID: schedule.InstanceID ,
            StackName: schedule.StackName ,
            StartCronJob: schedule.StartCronJob ,
            StopCronJob: schedule.StopCronJob ,
            Timezone: schedule.Timezone ,
            StackId: schedule.StackId ,
            accountId: schedule.accountId ,
            Region: schedule.Region ,
            ProvisioningEngineer: schedule.ProvisioningEngineer ,
            type: 'UPDATE',
                })
            });
    
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
    
            // Stream the response in chunks
            const reader = response.body.getReader();
            const decoder = new TextDecoder();
            let receivedText = "";
    
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;
                
                receivedText = decoder.decode(value, { stream: true });
                setAlertMessage(receivedText); // Append new chunks to message
                if(receivedText.substring(0,4)=="Succ")
                  { 
                    setAlertMessage("");
                    
                    setMessagestatus(true);
                  }
                if(receivedText.substring(0,5)=="Error"){
                  setAlertMessage("");
                  setMessage("test");
                  setMessagestatus(false);
                }
          
            }
    
            setMessagestatus(true);
    
        } catch (error) {
            setMessage(`Error: ${error.message}`);
            setMessagestatus(false);
        }
    };
    
    
    const [mySchedules1, setMySchedules1] = useState([
      {
        InstanceID: 'i-1234567890abcdef',
        StackName: 'MyStack1',
        StartCronJob: '0 8 * * *',
        StopCronJob: '0 20 * * *',
        Timezone: 'America/Los_Angeles',
        StackId: 'stack-123',
        accountId: '************',
        Region: 'us-west-2',
        ProvisioningEngineer: 'John Doe',
        IP: '***********',
        Name: 'Instance1',
        State: 'Running',
        accountname: 'HID Global',
        type: 'CREATE',
        ritm: 'RITM123456',
        time : "2025-06-03T14:12:49.247Z" ,
      },
      {
        InstanceID: 'i-0987654321fedcba',
        StackName: 'MyStack2',
        StartCronJob: '0 9 * * *',
        StopCronJob: '0 21 * * *',
        Timezone: 'America/New_York',
        StackId: 'stack-456',
        accountId: '************',
        Region: 'us-east-1',
        ProvisioningEngineer: 'Jane Smith',
        IP: '***********',
        Name: 'Instance2',
        State: 'Processing',
        accountname: 'Caylent',
        type: 'UPDATE',
        ritm: 'RITM654321',
        time : "2025-06-03T14:12:49.247Z" ,
      },
    ]);
    const columns = [
      { field: 'ritm', headerName: 'Reference #', width: 150 }, // Maps to `ritm`
      { field: 'accountname', headerName: 'Account Name', width: 150 }, // Maps to `accountname`
      { field: 'InstanceID', headerName: 'Instance ID', width: 200 }, // Maps to `InstanceID`
      { field: 'Name', headerName: 'Instance Name', width: 150 }, // Maps to `Name`
      { field: 'IP', headerName: 'Instance IP', width: 150 }, // Maps to `IP`
      { field: 'Region', headerName: 'Region', width: 150 }, // Maps to `Region`
      { field: 'StartCronJob', headerName: 'Starts At ', width: 150 }, // Maps to `StartCronJob`
      { field: 'StopCronJob', headerName: 'Stops At', width: 150 }, // Maps to `StopCronJob`
      { field: 'Timezone', headerName: 'Timezone', width: 200 },
      { field: 'type', headerName: 'ScheduleType', width: 150 }, // Maps to `Timezone`
      { field: 'ProvisioningEngineer', headerName: 'Requested By', width: 150 }, 
      { field: 'time',   headerName: 'Requested on(UTC)',  width: 150, }, // Maps to `State`
      // Maps to `ProvisioningEngineer`
      {
        field: 'action',
        headerName: 'Action',
        width: 150,
        renderCell: (params) => (
          <button 
           className={`sched-log-remove ${params.row.State === 'Processing' ? 'processing' : 'removable'}`}
           disabled={params.row.State === "Processing"} 
           onClick={() => handleRemoveClick(params.row)}
            >
             {params.row.State === 'Processing' ? (
                <CircularProgress size={20} style={{ color: '#666' }} />
              ) : (
                <DeleteOutlineSharpIcon />
              )}
          </button>
        ),
      },
    ];
    const handleRemoveClick1 = (rowData) => {
      console.log('Remove clicked for:', rowData);
  
      // Example: Remove the row from the table
      
    };
    // Add an `id` field to each row (required by DataGrid)
    const rows = mySchedules.map((row, index) => ({ id: index, ...row }));
      return(
        <div>
           {/* <Navbar /> */}
          
    {(message||alertMessage) &&<div  className="notification-container">
            {alertMessage && !message && (
          <div className="alert-card">
            <div className="alert-header">
              <div className="loading-icon">
                <img src={Loading} alt="Loading" className="loading-gif" />
              </div>
              <p className="alert-message">{alertMessage}</p>
              <button className="close-button" onClick={() => setAlertMessage(null)}><IoIosClose /></button>
            </div>
          </div>
        )}
    
          {/* Status Message Card */}
          {message && (
            <div className={`status-card ${messagestatus ? 'success' : 'error'}`}>
              <div className={`status-icon ${messagestatus ? 'pop-animation' : 'shake-animation'}`}>
                {messagestatus ? <FaCheckCircle size={24} /> : <FaTimesCircle size={24} />}
              </div>
             <p>{message}</p>
              <button className="close-button"onClick={() => {  setMessage(null); setAlertMessage(null);}}><IoIosClose /></button>
              
           
            </div>
          )}
          </div>}
          <div style={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={rows}
          columns={columns}
          pageSize={7}
          rowsPerPageOptions={[5, 10, 20]}
          sx={{
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: '#f5f5f5', // Optional: Add a background color for the header
              fontSize: '1.0rem', // Make the font size larger
              fontWeight: 'bold', // Make the text bold
               // Optional: Make the text uppercase
            },
            '& .MuiDataGrid-columnHeaderTitle': {
              fontWeight: 'bold', // Ensure the column header title is bold
            },
          }}
        />
      </div>
                     
                    </div>
      );

};

export default Scheduler;