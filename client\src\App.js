import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Route, Routes,Navigate } from 'react-router-dom';
import axios from 'axios';
import Home from './Home'; // Assuming you have a Home component
import Help from './AboutPage'; // Assuming you have an About component
import NotFound from './NotFound'; // Assuming you have a NotFound component
import Stop from './Stop';
import Start from './Start';
import Create from './Create';
import CreateMig from './CreateMIG';
import CreateLinux from './CreateLinux';
import CustomCreateLinux from './CustomCreate';
import CreateWithoutDomainJoin from './CreateWithoutDomainJoin';
import Login from './Login';
import Terminate from './Terminate';
import DomainChecker from './DomainChecker';
import Logs from './Logs';
import SchedulerComponent from './SchedulerComponent';
import Migration from './Migration';
import Scheduler from './Scheduler';
import SchedulerCopy from './Scheduler copy';
import Access from './Access';
import Approver from './Approver';
import Sample from './Sample';
import Sample2 from './Sample2';
import MSdomJoin from './MSDomainJoin';
import Test from './Test';
import LXdomJoin from './LXDomainJoin';
import MSdomUnjoin from './MSDomainUnjoin';
import LXdomUnjoin from './LXDomainUnjoin';
import Feedback from './feedback';
import DataTable from './Migration';
import Restart  from './Restart';
import AccountMigration from './AccountMigration';
import AccountMigrationClient from './AccountMigrationClient';
import AttachVolume from './AttachVolume';
import DetachVolume from './DetachVolume';
import Resize from './Resize';
import Dashboard from './Dashboard';
import Clipboard from './Clipboard';
import CreateClipBoard from './CreateClipboard';
//import Samplejson from './Samplejson';
import Tags from './Tags';
//import Samplejson from './Samplejson';
import CustomScheduler from './CustomScheduler';
import CustSched from './CustSched';
import CustSchedLogs from './CustSchedLogs';
import Create1 from './Create copy';
import CreateLinuxx from './JsonExtraction';
import Accountform from './Accountform';
import CreatWindowsIso from './CreateWindowsIso';
import SchedulerCust from './SchedulerCust';
import Serverlogs from './CustSchedLogs copy';
import CreateLinuxIso from './CreateLinuxIso';
import CreateLinuxSeg from './CreateLinuxSeg';
import CreateWindowsIso from './CreateWindowsIso';
import CreateWindowsSeg from './CreateWindowsSeg';
function App() {
  const [user, setUser] = useState(
    {
      email: '<EMAIL>',
      displayName: 'test displayname',
      firstName: 'test firstname'
    });
  const [loading, setLoading] = useState(true);
 
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        // console.log(user);
        // console.log(response.data.user);
      } catch (error) {
       
        setUser(null);
      }
      setLoading(false);
    }
    checkAuth();
  }, []);


  if (loading) {
    return <div>Loading...</div>;
  }
  return (
    <Router>
      <Routes>
      <Route path="/home" element={<Home />}></Route>
        <Route path="/help/:section?" element={<Help /> } />
     
        <Route path="/stop" element={user ?<Stop />: <Navigate to="/login" />} />
        <Route path="/restart" element={user ?<Restart />: <Navigate to="/login" />} />
        <Route path="/logs" element={<Logs />} />
        <Route path="/tags" element={<Tags />} />
        <Route path="/scheduler" element={<Scheduler />} />
        <Route path="/scheduler2" element={<SchedulerCopy />} />
        <Route path="/scheduler-component" element={<SchedulerComponent />} />
        <Route path="/start" element={user ?<Start /> : <Navigate to="/login" />} />
        <Route path="/Quicksight" element={<Dashboard /> } />
        <Route path="/approver" element={user ?<Approver /> : <Navigate to="/login" />} />
        <Route path="/login" element={<Login />} />
        <Route path="/windows-domain-join" element={<MSdomJoin />} />
         <Route path="/accountform" element={<Accountform />} />
        <Route path="/linux-domain-join" element={<LXdomJoin />} />
        <Route path="/windows-domain-unjoin" element={<MSdomUnjoin />} />
        <Route path="/test-join" element={<Test />} />
        <Route path="/linux-domain-unjoin" element={<LXdomUnjoin />} />
        <Route path="/U-access" element={<Access />} />
        {/* <Route path="/" element={<Migration />} /> */}
        <Route path="/Migration" element={<Sample />} />
        <Route path="/UserMigration" element={<Sample2 />} />
        <Route path="/table" element={<DataTable />} />
        <Route path="/Feedback" element={<Feedback />} />
        <Route path="/createwindows" element={<Create />} />
        <Route path="/Accountmigration" element={<AccountMigration />} />
        <Route path="/jsonextract" element={<CreateLinuxx />} />
        {/* //<Route path="/Accountmigrationdev" element={<Samplejson />} /> CreateLinux */}
        <Route path="/Accountmigrationclient" element={<AccountMigrationClient />} />
        <Route path="/createwindowsmig" element={<CreateMig />} />
        <Route path="/createlinux" element={<CreateLinux />} />
        <Route path="/customcreatelinux" element={<CustomCreateLinux />} />
        <Route path="/createwithoutdomjoin" element={user ?<CreateWithoutDomainJoin />: <Navigate to="/login" />} />
        <Route path="/terminate" element={user ?<Terminate />: <Navigate to="/login" />} />
        <Route path="/DomainChecker" element={user ?<DomainChecker />: <Navigate to="/login" />} />
        <Route path="*" element={<NotFound />} /> {/* For handling 404 - not found */}
        <Route path="/AttachVolume" element={<AttachVolume />} />
        <Route path="/DetachVolume" element={<DetachVolume />} />
        <Route path="/Resize" element={<Resize />} />
        <Route path="/Clipboard" element={<Clipboard />} />
        <Route path="/CreateClipBoard" element={<CreateClipBoard />} />
        <Route path="/CustomScheduler" element={<CustomScheduler />} />
        <Route path="/CustSched" element={<CustSched />} />
        <Route path="/CustSchedLogs" element={<CustSchedLogs />} />
        <Route path="/createwindows1" element={<Create1 />} />
        <Route path="/CreateWindowsIso" element={<CreateWindowsIso />} />
        <Route path="/SchedulerCust" element={<SchedulerCust />} />
        <Route path="/resourceinventory" element={<Serverlogs />} />
        <Route path="/CreateLinuxIso" element={<CreateLinuxIso />} />
        <Route path="/CreateLinuxSeg" element={<CreateLinuxSeg />} />
        <Route path="/CreateWindowsSeg" element={<CreateWindowsSeg />} />
      </Routes>
    </Router>
  );
}

export default App;
