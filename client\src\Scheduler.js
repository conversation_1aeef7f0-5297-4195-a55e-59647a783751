// Scheduler.js
import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import axios from 'axios';
import './scheduler.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheckCircle, faExclamationCircle } from '@fortawesome/free-solid-svg-icons';
import { IoIosClose } from "react-icons/io";
import Navbar from './Navbar'; // Ensure this path is correct

const schedules = [
  { name: 'US-Pacific', timezone: 'PST', time: '6AM - 10PM' ,filename :'us-pacific.xlsx'},
  { name: 'US-Mountain', timezone: 'MST', time: '6AM - 10PM' ,filename :'us-mountain.xlsx'},
  { name: 'US-Central', timezone: 'CST', time: '6AM - 10PM' ,filename :'us-central.xlsx'},
  { name: 'US-East', timezone: 'EST', time: '6AM - 10PM'  ,filename :'us-east.xlsx'},
  { name: 'EU-West', timezone: 'WET', time: '6AM - 10PM'  ,filename :'eu-west.xlsx'},
  { name: 'EU-Central', timezone: 'CET', time: '6AM - 10PM'  ,filename :'eu-central.xlsx'},
  { name: 'EU-East', timezone: 'EET', time: '6AM - 10PM'  ,filename :'eu-east.xlsx'},
  { name: 'AP-India', timezone: 'IST', time: '6AM - 10PM'  ,filename :'ap-india.xlsx'},
];
const weeklyschedules = [
  { name: 'US-Pacific-Weekly', timezone: 'PST', time: '6AM - 10PM' ,filename :'us-pacific-weekly.xlsx'},
  { name: 'US-Mountain-Weekly', timezone: 'MST', time: '6AM - 10PM' ,filename :'us-mountain-weekly.xlsx'},
  { name: 'US-Central-Weekly', timezone: 'CST', time: '6AM - 10PM' ,filename :'us-central-weekly.xlsx'},
  { name: 'US-East-Weekly', timezone: 'EST', time: '6AM - 10PM'  ,filename :'us-east-weekly.xlsx'},
  { name: 'EU-West-Weekly', timezone: 'WET', time: '6AM - 10PM'  ,filename :'eu-west-weekly.xlsx'},
  { name: 'EU-Central-Weekly', timezone: 'CET', time: '6AM - 10PM'  ,filename :'eu-central-weekly.xlsx'},
  { name: 'EU-East-Weekly', timezone: 'EET', time: '6AM - 10PM'  ,filename :'eu-east-weekly.xlsx'},
  { name: 'AP-India-Weekly', timezone: 'IST', time: '6AM - 10PM'  ,filename :'ap-india-weekly.xlsx'},
];

const Scheduler = () => {
  const [showForm, setShowForm] = useState(false);
  const [selectedSchedule, setSelectedSchedule] = useState(null);
  const [awsAccount, setAwsAccount] = useState('');
  const [region, setRegion] = useState('');
  const [instanceIds, setInstanceIds] = useState(['']);
  const [instanceIdValues, setInstanceIdValues] = useState([]);
  const [view, setView] = useState('allSchedules'); // State to toggle between views
  const [mySchedules, setMySchedules] = useState([]); 
  const [myScheduleList, setMyScheduleList] = useState([]);// State to store "My Schedules"
  const [user, setUser] = useState( {
    email: '<EMAIL>',
    displayName: 'Guest',
    firstName: 'Guest'
  });
  const [accounts, setAccounts] = useState([]);
  const [regions, setRegions] = useState([]);
  const [instances, setInstances] = useState([]);
  const [accountId, setAccountId] = useState([]);
  const [data, setData] = useState([]);
  const [Message, setMessage] = useState("");
  const [removeMessage, setremoveMessage] = useState("");
  const [removeError, setremoveError] = useState("");
  const [Error,setError] = useState("");
  const [scheduleType, setScheduleType] = useState('schedules');
  const [searchTerm, setSearchTerm] = useState(''); // State for search term
  const [filterRegion, setFilterRegion] = useState(''); 
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
      } catch (error) {
        // Set user to null in case of an error
      }
    }
    checkAuth();
  }, []);

  useEffect(() => {
    let x=[];
    axios.get('https://umanage.dev.hidglobal.com/api/user')
      .then(response => {
        const fetchedData = response.data;
        // console.log(user);
        // console.log(fetchedData); 
         //console.log('Fetched user data:', fetchedData);
       // console.log(user);
        const userEntry = fetchedData.find(entry => entry.user === user.email);
        //console.log('User entry:', userEntry);
  
        if (userEntry) {
          const accountIds = userEntry.accounts.split(',').map(account => account.trim());
          //console.log(accountIds);
          x=accountIds;
          // console.log('Parsed account IDs:', accountIds);
          setAccountId(accountIds);
        } else {
          setAccountId([]);
        }
      })
      .catch(error => {
        // console.error('Error fetching user accounts:', error);
      });
      axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
      .then((response) => {
        const uniqueAccounts = Array.from(new Set(response.data.map(item => item.CreateAccountId)))
           .map(accountId => response.data.find(item => item.CreateAccountId === accountId));
           let fetchedData = response.data;
          setData(fetchedData);
          fetchedData = uniqueAccounts.filter(item => x.includes(item.CreateAccountId));
             //console.log('Filtered S3 data:', fetchedData);
    
            //const uniqueAccounts = [...new Set(fetchedData.map(item => item.CreateAccountName))];
             //console.log('Unique account names:', uniqueAccounts);
        setAccounts(fetchedData); // Store the entire account object
        //console.log("account"+accounts);
      })
      .catch((error) => {
        // console.error('Error fetching accounts:', error);
      });
  }, [user]);
  useEffect(() => {
    if (awsAccount) {
      axios.get('https://umanage.dev.hidglobal.com/api/s3')
        .then(response => {
          let fetchedData = response.data;
          
          
          setData(fetchedData);
          
        })
        .catch(error => {
          console.error('Error fetching S3 data:', error);
        });
    }
  }, [awsAccount]);
  useEffect(() => {
    if (awsAccount) {
      const filteredData = data.filter(item => item.accountId === awsAccount);
      const uniqueRegions = [...new Set(filteredData.map(item => item.Region))];
      setRegions(uniqueRegions);
    }
  }, [awsAccount, data]);

  useEffect(() => {
    if (region && awsAccount) {
      const idata = data.filter(item => item.Region === region && item.accountId === awsAccount);
      
      setInstances(idata.map(item => ({
        value: item.InstanceId,
        label: `${item.InstanceName} - ${item.InstanceId}`
      })));
    }
  }, [region, awsAccount, data]);
  console.log ("InstanceIds",instanceIds);
  useEffect(() => {
    async function fetchSchedules() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/schedule/Schedule');
        const filteredSchedules = response.data.filter(schedule => schedule.engineer === user.email);
        setMySchedules(filteredSchedules);
      } catch (error) {
        console.error('Error fetching schedules:', error);
      }
    }
    fetchSchedules();
  }, [user.email]); 
  
  console.log("MySchedules",mySchedules);
  useEffect(() => {
    const transformSchedules = () => {
      const transformed = mySchedules.flatMap(schedule => 
        schedule.Instanceids.split(',').map(instanceId => ({
          ...schedule,
          Instanceids: instanceId.trim()
        }))
      );
      setMyScheduleList(transformed);
    };
  
    transformSchedules();
  }, [mySchedules]);
  console.log("ScheduleList",myScheduleList);
  const handleAddClick = (schedule) => {
    setSelectedSchedule(schedule);
    setShowForm(true);
  };
  const handleAccountChange = (selectedOption) => {
    setAwsAccount(selectedOption.value);
  };
console.log("AWS Account",awsAccount);
  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setMessage('');
    setError('');
  
    // Split instance IDs by comma and trim spaces
    
  
   
    try {
      const response = await axios.post('https://umanage.dev.hidglobal.com/api/schedule/AddSchedule', {
        accountId:awsAccount,
        instanceIds: instanceIdValues,
        region: region,
        email: user.email,
        filename: selectedSchedule.filename
      });
  
      setMessage("Instance added to the Schedule.");
  
      // Append details to the existing CSV file
      
     
  
    } catch (err) {
      setError('An error occurred while updating the Excel file.');
    }
  };
  console.log("Message",Message);
  console.log("Error",Error);
  const handleRemoveClick = (schedule) => {
    try {
     axios.post('https://umanage.dev.hidglobal.com/api/schedule/RemoveSchedule', {
        accountId: schedule.AccountID,
        instanceIds: [schedule.Instanceids],
        region: schedule.Region,
        filename: schedule.Zone,
        email: user.email
      });
      setMySchedules(mySchedules.filter(s => s !== schedule));
      setremoveMessage("Instance removed from the Schedule.");
    } catch (err) {
      setremoveError('An error occurred while removing the instance.');
    }
  };
  const handleInstanceChange = (selectedOptions) => {
    setInstanceIds(selectedOptions);
    setInstanceIdValues(selectedOptions.map(option => option.value));
  };
  console.log("InstanceIdsValues",instanceIdValues);
  const accountOptions = accounts.map(account => ({
    value: account.CreateAccountId,
    label: account.CreateAccountName,
  }));
  const selectedAccount =accountOptions.find(option => option.value === awsAccount);

  const handleToggleChange = (type) => {
    setScheduleType(type);
  };
  
  return (
    <div>
      <Navbar />
      <div className="scheduler-container">
        <aside className="sidebar">
          <h2>Scheduler</h2>
          <button onClick={() => setView('allSchedules')}>All Schedules</button>
          <button onClick={() => setView('mySchedules')}>My Schedules</button>
        </aside>

        <main className="main-content">
          {view === 'allSchedules' && (
            <>
              <h1>INSTANCE SCHEDULER</h1>
              <div className="toggle-container">
              <input
                type="radio"
                id="schedules"
                name="scheduleType"
                value="schedules"
                checked={scheduleType === 'schedules'}
                onChange={() => handleToggleChange('schedules')}
              />
              <label htmlFor="schedules">Schedules</label>
              <input
                type="radio"
                id="weeklyschedules"
                name="scheduleType"
                value="weeklyschedules"
                checked={scheduleType === 'weeklyschedules'}
                onChange={() => handleToggleChange('weeklyschedules')}
              />
              <label htmlFor="weeklyschedules">Weekly Schedules</label>
              <div
                className={` ${scheduleType === 'schedules' ? 'underline' : 'underline-weekly'}`}
              ></div>
            </div>
              <div className="schedule-grid">
                {(scheduleType === 'schedules' ? schedules : weeklyschedules).map((schedule, index) => (
                  <div key={index} className="schedule-box">
                    <h3>{schedule.name}</h3>
                    <p>{schedule.timezone} ({schedule.time})</p>
                    <button onClick={() => handleAddClick(schedule)}>Add</button>
                  </div>
                ))}
              </div>
              <div className="message-banner">
                {scheduleType === 'schedules' ? (
                  <p>Instances will be started at <strong>6:00 AM</strong> and stopped at <strong>10:00 PM</strong> daily.</p>
                ) : (
                  <p>Instances will be started at <strong>6:00 AM</strong> on <strong>Monday</strong> and stopped at <strong>10:00 PM</strong> on <strong>Friday</strong> weekly.</p>
                )}
              </div>
            </>
          )}

{view === 'mySchedules' && (
            <>
              <h1>MY SCHEDULES</h1>
              {removeMessage && (
                <p className="sch-message">
                  <FontAwesomeIcon icon={faCheckCircle} /> {removeMessage}
                  <button className="sch-close-btn" onClick={() => setremoveMessage('')}><IoIosClose /></button>
                </p>
              )}
              {removeError && (
                <p className="sch-alert">
                  <FontAwesomeIcon icon={faExclamationCircle} /> {removeError}
                  <button className="sch-close-btn" onClick={() => setremoveError('')}><IoIosClose /></button>
                </p>
              )}
              <table className="schedule-table">
                <thead>
                  <tr>
                    <th>Instance ID</th>
                    <th>Region</th>
                    <th>AWS Account</th>
                    <th>Schedule</th>
                    <th>Start Date</th>
                    <th>End Date</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {myScheduleList.map((schedule, index) => (
                    <tr key={index}>
                      <td>{schedule.Instanceids}</td>
                      <td>{schedule.Region}</td>
                      <td>{schedule.AccountID}</td>
                      <td>{schedule.Zone}</td>
                      <td>6:00 AM</td> {/* Start Date */}
                      <td>10:00 PM</td> {/* End Date */}
                      <td>
                        <button className="remove-button" onClick={() => handleRemoveClick(schedule)}>Remove</button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </>
          )}
        </main>

        {showForm && (
          <div className="schedule-form">
            <div className="schedule-form-content">
            <button className="sch-close-btn-top" onClick={() => { setShowForm(false); setMessage(''); setError(''); }}>X</button>
              <h2>Add Instance to {selectedSchedule.name}</h2>
              <form onSubmit={handleFormSubmit}>
                <label>AWS Account</label>
                {/* <select value={awsAccount} onChange={(e) => setAwsAccount(e.target.value)}>
                  <option value="">Select Account</option>
                  {accounts.map(account => (
                    <option key={account} value={account}>{account}</option>
                  ))}
                </select> */}
               <Select
                  className="re-select"
                  value={selectedAccount || null} // Display the selected account
                  onChange={handleAccountChange} // Handle account change
                  options={accountOptions}// Map accounts to options
                  isSearchable={true} // Enable search functionality
                  placeholder="Search or select an account"
                />
                

                <label>Region</label>
                <select className="form-control" 
                  value={region} 
                  onChange={(e) => setRegion(e.target.value)} disabled={!awsAccount}>
                  <option value="">Select Region</option>
                  {regions.map(region => (
                    <option key={region} value={region}>{region}</option>
                  ))}
                </select> 

                <label>Instance Name</label>
                {/* <select value={instance} onChange={(e) => setInstance(e.target.value)} disabled={!region}>
                  <option value="">Select Instance</option>
                  {instances.map(instance => (
                    <option key={instance.id} value={instance.id}>{instance.label}</option>
                  ))}
                </select> */}
                <Select
                  isMulti
                  className="re-select"
                  value={instanceIds}
                  onChange={handleInstanceChange}
                  options={instances}
                  isDisabled={!region}
                />
               {Message && <p className="sch-message"><FontAwesomeIcon icon={faCheckCircle} />   {Message}</p>}
               {Error && <p className="sch-alert"><FontAwesomeIcon icon={faExclamationCircle} />   {Error}</p>}
                <button  type="submit" disabled={Message}>Add Instance</button>
              </form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Scheduler;
