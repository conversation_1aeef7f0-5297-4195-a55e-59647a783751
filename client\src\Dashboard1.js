import React, { useRef, useState ,useEffect} from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './Dashboard.css';
import Navbar from './Navbar';
import Select from 'react-select';
import { QuickSightEmbedding } from 'amazon-quicksight-embedding-sdk';
import { createEmbeddingContext } from 'amazon-quicksight-embedding-sdk';
function Dashboard() {
  const containerRef = useRef(null);
  const [loading, setLoading] = useState(false);
   const navigate = useNavigate();
  const [error, setError] = useState('');
  const [user, setUser] = useState(
    {
      email: '<EMAIL>',
      displayName: 'test displayname',
      firstName: 'test firstname'
    });
  const [accountId, setAccountId] = useState([]);
  const [accountNames, setAccountNames] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedDashboard, setSelectedDashboard] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const regions = ['us-east-1', 'us-west-2', 'eu-west-1'];
   const[dashboardId, setDashboardId] = useState('');
  const [accounts, setAccounts] = useState([]); // State to store accounts fetched from the API
   
   const [approverRole,setApproverRole] =useState(''); // Example role value
   const dashboardIds = [
    { value: '23d4146c-4579-4caf-858b-07f254071aba', label: 'User U-manage Metrics' },
    { value: '8d0292a5-7626-48fe-9688-92570f7076eb', label: 'Users_EC2_Inventory' },
    { value: 'c2bc1140-b943-4b58-b740-8c5b02d3cb7a', label: 'Migration Dashboard' },
  ];
  
  // Add restricted dashboards if approverRole is 'Approver'
  if (approverRole === 'Approver') {
    dashboardIds.push(
      { value: 'f45c2bfe-edcf-4f0c-bd58-1a06d76d0163', label: 'U-Access Request Dashboard (Restricted)' },
      { value: '12b68754-816d-487d-ae1d-821e9b7f8962', label: 'CUDOS Dashboard (Restricted)' }
    );
  }
  
  console.log(dashboardIds); // Verify the dashboard IDs

console.log(dashboardIds);
const BusinessArea = [
  { account_id: '***********', business_area: 'SI' },
  { account_id: '***********', business_area: 'SI' },
  { account_id: '***********', business_area: 'IAMS' },
  { account_id: '***********', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'SI' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '***********', business_area: 'PACS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IDT' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'IDT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '***********', business_area: 'EAT' },
  { account_id: '***********', business_area: 'EAT' },
  { account_id: '***********', business_area: 'EIT' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'SI' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'IAMS' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'EIT' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'EAT' },
  { account_id: '************', business_area: 'PACS' },
  { account_id: '************', business_area: 'IAMS' },
];
const [businessAreaOptions, setBusinessAreaOptions] = useState([]);
const [selectedBA, setSelectedBA] = useState(''); // State to store selected business area
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
         // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);
  
  useEffect(() => {
    let x=[];
    axios.get('https://umanage.dev.hidglobal.com/api/user')
      .then(response => {
        const fetchedData = response.data;
        // console.log(user);
        // console.log(fetchedData); 
         //console.log('Fetched user data:', fetchedData);
       // console.log(user);
        const userEntry = fetchedData.find(entry => entry.user === user.email);
        //console.log('User entry:', userEntry);
  
        if (userEntry) {
          const accountIds = userEntry.accounts.split(',').map(account => account.trim());
          //console.log(accountIds);
          x=accountIds;
          // console.log('Parsed account IDs:', accountIds);
          setAccountId(accountIds);
        } else {
          setAccountId([]);
        }
      })
      .catch(error => {
        // console.error('Error fetching user accounts:', error);
      });
      axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
      .then((response) => {
        const uniqueAccounts = Array.from(new Set(response.data.map(item => item.CreateAccountId)))
           .map(accountId => response.data.find(item => item.CreateAccountId === accountId));
           let fetchedData = response.data;
          
          fetchedData = uniqueAccounts.filter(item => x.includes(item.CreateAccountId));
             //console.log('Filtered S3 data:', fetchedData);
    
            //const uniqueAccounts = [...new Set(fetchedData.map(item => item.CreateAccountName))];
             //console.log('Unique account names:', uniqueAccounts);
        setAccounts(fetchedData); // Store the entire account object
        //console.log("account"+accounts);
      })
      .catch((error) => {
        // console.error('Error fetching accounts:', error);
      });
  }, [user]);
  console.log('Account IDs:', accountId); // Log the account IDs for debugging
  useEffect(() => {
    if (accountId.length > 0) {
      // Filter business areas based on selected account IDs
      const filteredBusinessAreas = BusinessArea.filter((item) =>
        accountId.includes(item.account_id)
      ).map((item) => ({
        value: item.business_area,
        label: item.business_area,
      }));

      // Remove duplicates from businessAreaOptions
      const uniqueBusinessAreas = Array.from(
        new Set(filteredBusinessAreas.map((option) => option.value))
      ).map((value) => ({
        value,
        label: value,
      }));

      setBusinessAreaOptions(uniqueBusinessAreas);
    } else {
      setBusinessAreaOptions([]);
    }
  }, [accountId]);
  console.log('Business Area Options:', businessAreaOptions); // Log the business area options for debugging
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/trigger-ssm/approver')
      .then(response => {
        const fetchedData = response.data;
  
        // Filter fetchedData based on user.email
        const userSpecificData = fetchedData.filter(data => data.user === user.email);
        
        setApproverRole(userSpecificData[0].approver);
        // Set ApproverRole if user-specific data exists
      })
      .catch(error => {
        console.error('Error fetching approver data:', error);
      });
  }, [user.email]);
  console.log('Approver Role:', approverRole); 
  console.log("Selected Account:", selectedAccount);
  console.log("Selected Dashboard:", selectedDashboard);
  useEffect(() => {
    if (accountId.length > 0) {
      axios.get('https://umanage.dev.hidglobal.com/api/s3')
        .then(response => {
          let fetchedData = response.data;
           //console.log('Fetched S3 data:', fetchedData);
  
          fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
           //console.log('Filtered S3 data:', fetchedData);
  
          const uniqueAccounts = [...new Set(fetchedData.map(item => item.AccountName))];
           //console.log('Unique account names:', uniqueAccounts);
  
          
          setAccountNames(uniqueAccounts);
        })
        .catch(error => {
          // console.error('Error fetching S3 data:', error);
        });
    }
  }, [accountId]);
  const handleEmbed = async () => {
    setLoading(true);
    setError('');
  
    try {
      const response = await fetch('https://umanage.dev.hidglobal.com/api/trigger-ssm/quicksight', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ servicenownumber: '0000', businessArea: selectedBA, dashboardId: selectedDashboard }),
      });
  
      const data = await response.json();
  
      if (!data.EmbedUrl) {
        setError('Embed URL not found in response.');
        return;
      }
  
      console.log('in fetch');
      console.log(data.EmbedUrl);
  
      // Clear the container before embedding the new dashboard
      if (containerRef.current) {
        containerRef.current.innerHTML = ''; // Clear the container
      }
  
      const embeddingContext = await createEmbeddingContext(); // ✅ Correct usage
  
      embeddingContext.embedDashboard({
        url: data.EmbedUrl,
        container: containerRef.current,
        height: '800px',
        width: '100%',
        scrolling: 'no',
        footerPaddingEnabled: true,
        printEnabled: false,
        resizeHeightOnSizeChangedEvent: true,
      });
    } catch (err) {
      console.error('Error embedding dashboard:', err);
      setError('Failed to load dashboard.');
    } finally {
      setLoading(false);
    }
  };
  const handleBusinessAreaChange = (selectedOption) => {
    setSelectedBA(selectedOption.value); // Update selected account
    setSelectedDashboard(''); // Reset selected dashboard when account changes
  };
  const handleDashboardChange = (selectedOption) => {
    setSelectedDashboard(selectedOption.value); // Update selected dashboard
  }
  return (
    <div className="dashboard-wrapper">
      
      <h2 style={{ textAlign: 'center' }}>QuickSight Dashboard</h2>
      <div className="dropdown-container">
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Account Selection</h2>
      <p className="dropdown-description">Select the account to manage instances.</p>
      {/* <select className="form-control" value={selectedAccount} onChange={(e) => setSelectedAccount(e.target.value)}>
        <option value="">Select Account</option>
        {accountNames.map(account => (
          <option key={account} value={account}>{account}</option>
        ))}
      </select> */}
      <Select
        className="re-select"
        value={businessAreaOptions.find(option => option.value === selectedBA)} // Display the selected account
        onChange={handleBusinessAreaChange} // Handle account change
        options={businessAreaOptions}// Map accounts to options
        isSearchable={true} // Enable search functionality
        placeholder="Search or select an account"
      />
    </div>

    <div className="dropdown-section">
      <h2 className="dropdown-heading">Dashboard Selection</h2>
      <p className="dropdown-description">Choose the region for the selected account.</p>
      <Select
        className="re-select"
        value={dashboardIds.find(option => option.value === selectedDashboard)} // Display the selected account
        onChange={handleDashboardChange} // Handle account change
        options={dashboardIds}// Map accounts to options
        isSearchable={true} // Enable search functionality
        placeholder="Search or select a Dashboard"
      />
    </div>
  </div>
      <div style={{ textAlign: 'center', marginBottom: '20px' }}>
        <button onClick={handleEmbed} disabled={loading}>
          {loading ? 'Loading...' : 'Load Dashboard'}
        </button>
        {error && <p style={{ color: 'red' }}>{error}</p>}
      </div>
      <div
        id="dashboardContainer"
        ref={containerRef}
        style={{ width: '100%', height: '800px', border: '1px solid #ccc' }}
      />
    </div>
  );
}

export default Dashboard;
