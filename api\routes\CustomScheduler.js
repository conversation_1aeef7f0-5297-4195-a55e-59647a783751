const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');
const nodemailer = require('nodemailer');
const fs = require("fs");
const XLSX = require('xlsx');
const { error, time } = require('console');
const axios = require('axios');
const https = require('https');
const cronstrue = require('cronstrue');
// Export the function to set up the router with necessary utilities
module.exports = (generateTicketNumber, storeDataInS3) => {
  let originalCredentials = AWS.config.credentials;
 
  // Function to assume an IAM role and return temporary credentials
  const assumeRole = async (accountId,firstname) => {
    console.log("assumerole");
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    
    try {
      const data = await sts.assumeRole(params).promise();
      return data.Credentials;
    } catch (error) {
      AWS.config.update({ credentials: null });
        console.error('Error assuming role:', error.code, error.message);
        if (error.code === 'AccessDenied') {
          const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
            // Handle AccessDenied specifically, maybe log the accounts involved
       
     
    }else{
      console.error('Error assuming role:', error);
      throw error;
    }}
  };
 
  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };
 
  // Function to configure AWS services (SSM, S3, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      cloudFormation: new AWS.CloudFormation()
    };
  };
  
  const handleSSMRequest = async (req, res, documentName) => {
    console.log(req.body);

    //const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} =  req.body;
 console.log("i am in custom Scheduler");
    let firstname= req.body.ProvisioningEngineer;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');

    const sendUpdate = (message) => {
      res.write(`${message }\n\n`);
    };
// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
// const combinedString = req.body.securityGroupIds.join(',');

    const params = {
        DocumentName:'Custom_Instance_Schedule_Stack',
        Parameters: {
          InstanceID: [req.body.InstanceID],
          StackName: [req.body.StackName],
          StartCronJob: [req.body.StartCronJob],
          StopCronJob: [req.body.StopCronJob],
          Timezone: [req.body.Timezone],
          AssumeRole: [`arn:aws:iam::${req.body.accountId}:role/CrossAccountAccessRole`],
          cronJobType: [req.body.cronJobType],
          ContactingCustomer: [req.body.ProvisioningEngineer ],
          AffectingUser: [req.body.ProvisioningEngineer ],
         
          ShortDescription: [` Schedule Creation through U-manage`],
          Description: [` Schedule Creation through U-manage InsatnceID:${req.body.InstanceID} Created in AccountID :${req.body.accountId} Region:${req.body.Region}`],
          RequestedFor: [req.body.ProvisioningEngineer],

        }
      };
      console.log(params);
 
    try {
      // AWS.config.update({ credentials: originalCredentials });
      if(req.body.accountId!=************){
      const credentials = await assumeRole(req.body.accountId,y);
      await initializeAWS(credentials, req.body.Region);}else{AWS.config.update({
        region: req.body.Region
      });}
      const { ssm } = configureAWS();
      console.log("after initilize");
    //   const ticketNumber = '1';
    console.log("triggering ssm document");
      let executionID;
      const startTime = new Date();
      console.log("triggering 146ssm document");
      let data;
     try{

         data = await ssm.startAutomationExecution(params).promise();
     } catch (error) {
        console.error('Error fetching execution status:', error);
      
      }
      
      console.log("triggering 148ssm document");
      executionID = data.AutomationExecutionId;
      console.log(originalCredentials);
      AWS.config.update({ credentials: originalCredentials });
      const getAutomationssmExecutionStatus1 = async (ssm, executionId) => {
        try {
          const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
          console.log('Automation execution data:', data); // Log entire response
          const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
          return data;
        } catch (error) {
          console.error('Error fetching execution status:', error);
        
        }
      };
      let servicenownumber;
        // Poll status and fetch detailed result
        const pollAutomationssmExecutionStatus1 = async (ssm, executionId, interval = 1000) => {
          return new Promise((resolve, reject) => {
            const intervalId = setInterval(async () => {
              try {
                const data = await getAutomationssmExecutionStatus1(ssm, executionId);
                const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
                console.log('Current status:', status);
                
               
                const targetStep = data.AutomationExecution.StepExecutions.find(
                  step => step.StepName === 'Snow_Req_Creation'
                );
                if (targetStep && targetStep.Outputs) {
                    console.log("Step Outputs:", targetStep.Outputs);
                   //servicenownumber= targetStep.Outputs.RITM[0];
                   try{ 
                //    console.log(targetStep.Outputs.Output);
                //    console.log(targetStep.Outputs.Output[0]);
                } catch (error) {
                   console.log(error);
                  }
                  } else {
                    console.log("No outputs found for the specified step.");
                  }
                sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
                if (['Success', 'Failed', 'TimedOut', 'Cancelled','Pending'].includes(status)) {
                    if ([ 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                        sendUpdate(`Error: ${data.AutomationExecution.CurrentStepName} step is ${status}`);
                        throw new Error('Instance Creation failed');
                    }
                  clearInterval(intervalId);
                  resolve(status);
                }
              } catch (error) {
                clearInterval(intervalId);
                reject(error);
              }
            }, interval);
          });
        };
      // Poll status and fetch detailed result
      const status = await pollAutomationssmExecutionStatus1(ssm, executionID);
      const executionDetails = await getAutomationssmExecutionStatus1(ssm, executionID);
      // const data = await getAutomationssmExecutionStatus1(ssm, executionId);
      const status2= executionDetails.AutomationExecution ? executionDetails.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      console.log("status2");
      console.log(executionDetails.AutomationExecution.Outputs);
       console.log("output");
       const StackId0= executionDetails.AutomationExecution.Outputs['CUSTOM_SCHEDULE_STACK.StackId'];
       const instanceip0= executionDetails.AutomationExecution.Outputs['Set_Scheduler_EC2_Tags.IPAddress'];
       const instancename0= executionDetails.AutomationExecution.Outputs['Set_Scheduler_EC2_Tags.Name'];
       const accountname0= executionDetails.AutomationExecution.Outputs['Get_Account_Name.AccountName'];
       const ritm0= executionDetails.AutomationExecution.Outputs['RetrieveRITM.RITM'];
         const StackId1= executionDetails.AutomationExecution.Outputs['CUSTOM_SCHEDULE_STACK_1.StackId'];
       const instanceip1= executionDetails.AutomationExecution.Outputs['Set_Scheduler_EC2_Tags_1.IPAddress'];
       const instancename1= executionDetails.AutomationExecution.Outputs['Set_Scheduler_EC2_Tags_1.Name'];
       const accountname1= executionDetails.AutomationExecution.Outputs['Get_Account_Name_1.AccountName'];
        const ritm1= executionDetails.AutomationExecution.Outputs['RetrieveRITM_1.RITM'];
        //Set_Scheduler_EC2_Tags.AlreadyTaggedIPs
       
       let StackId;
       let instanceip;
let instancename; 
      let accountname;
      let ritm;
       if (StackId0[0] === 'No output available yet because the step is not successfully executed') {
   StackId=StackId1;
    instanceip=instanceip1;
    instancename=instancename1;
    accountname=accountname1;
    ritm=ritm1;
       } else {
    StackId=StackId0; 
    instanceip=instanceip0;
    instancename=instancename0;
    accountname=accountname0;
    ritm=ritm0;
       }

       console.log(StackId[0]);
       //Get_Account_Name.AccountName
     console.log(instanceip[0]);
      console.log(instancename[0]);
     
      
      const finishTime = new Date();
     const endTime =new Date();
    
    console.log("before logs");
console.log(executionDetails.AutomationExecution.Outputs['Set_Scheduler_EC2_Tags.AlreadyTaggedNames'][0]);
console.log(executionDetails.AutomationExecution.Outputs['Set_Scheduler_EC2_Tags.AlreadyTaggedNames'][0]);

 
if(    executionDetails.AutomationExecution.Outputs['Set_Scheduler_EC2_Tags.AlreadyTaggedNames'][0]==='false' || executionDetails.AutomationExecution.Outputs['Set_Scheduler_EC2_Tags.AlreadyTaggedNames'][0]==='No output available yet because the step is not successfully executed'){
  
try {
   const s3 = new AWS.S3();
    const getObjectParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/CustomSchedulebackup.xlsx',
    };

    const data = await s3.getObject(getObjectParams).promise();
    let workbook;

    if (data.Body && data.Body.length > 0) {
        workbook = XLSX.read(data.Body, { type: 'buffer' });
    } else {
        workbook = XLSX.utils.book_new();
    }

    const sheetName = 'Sheet1';
    let sheet;

    if (workbook.SheetNames.includes(sheetName)) {
        sheet = workbook.Sheets[sheetName];
    } else {
        sheet = XLSX.utils.aoa_to_sheet([[
            'InstanceId', 'StackName', 'StartCronJob', 'StopCronJob', 'Timezone',
            'StackId', 'accountId', 'Region', 'ProvisioningEngineer'
        ]]);
        XLSX.utils.book_append_sheet(workbook, sheet, sheetName);
    }

   const instanceIds = (req.body.InstanceID || '')
  .split(',')
  .map(id => id.replace(/"/g, '').trim()); // Remove quotes and trim whitespace

// const newRows = instanceIds.map(instanceId => [
//   instanceId,
//   req.body.StackName || '',
//   req.body.StartCronJob || '',
//   req.body.StopCronJob || '',
//   req.body.Timezone || '',
//   StackId[0] || '',
//   req.body.accountId || '',
//   req.body.Region || '',
//   req.body.ProvisioningEngineer || ''
// ]);

// newRows now contains multiple rows, one for each instanceId


    const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
  
  const instanceIps = (instanceip[0] || '')
        .split(',').map(ip => ip.replace(/"/g, '').trim());

    const instanceNames = (instancename[0]|| '')
        .split(',').map(name => name.replace(/"/g, '').trim());

        const parts = req.body.actualStartCronJob.trim().split(' ');
   
    // Convert AWS DOW to standard (subtract 1, wrap Sunday from 1 → 0)
    let dow = parts[4];
    if (dow !== '?' && dow !== '*') {
        dow = String((parseInt(dow, 10) + 6) % 7); // 1 → 0 (Sunday), 2 → 1 (Monday), etc.
        parts[4] = dow;
    }
    // Remove year (since cronstrue doesn't support it)
    let x= parts.slice(0, 5).join(' ');

    const parts2 = req.body.actualStopCronJob.trim().split(' ');
   
    // Convert AWS DOW to standard (subtract 1, wrap Sunday from 1 → 0)
    let dow2 = parts2[4];
    if (dow2 !== '?' && dow2 !== '*') {
        dow2 = String((parseInt(dow2, 10) + 6) % 7); // 1 → 0 (Sunday), 2 → 1 (Monday), etc.
        parts2[4] = dow2;
    }
    // Remove year (since cronstrue doesn't support it)
    let x2= parts2.slice(0, 5).join(' ');
    instanceIds.forEach((id, index) => {
        const ip = instanceIps[index] || '';
        const name = instanceNames[index] || '';


        jsonData.push([
            id,
            
            req.body.StackName || '',
            cronstrue.toString(x , { locale: "en" }) || '',
            cronstrue.toString(x2 , { locale: "en" }) || '',
            req.body.Timezone || '',
            StackId[0] || '',
            `AccountId:${req.body.accountId}` || '',
            req.body.Region || '',
            req.body.ProvisioningEngineer || '',
            ip,
            name,
            accountname[0] || '',
            req.body.cronJobType || '',
            ritm[0] || ''
        ]);
    });
    
   
console.log(jsonData);
    const updatedSheet = XLSX.utils.aoa_to_sheet(jsonData);

    workbook.Sheets[sheetName] = updatedSheet;

    const updatedFile = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    const uploadParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/CustomSchedulebackup.xlsx',
        Body: updatedFile,
        ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };

    await s3.putObject(uploadParams).promise();
    console.log('Excel file updated successfully!');
} catch (error) {
    console.error('Error updating Excel file:', error);
   // return res.status(500).send('Error writing to Excel');
}
try {
    const s3 = new AWS.S3();
    const getObjectParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/CustomSchedule.csv',
    };

    const data = await s3.getObject(getObjectParams).promise();
    let rows = [];

    if (data.Body && data.Body.length > 0) {
        // Parse CSV
        const csvText = data.Body.toString('utf-8');
        const workbook = XLSX.read(csvText, { type: 'string' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        rows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    } else {
        // No data: create header row
        rows = [[
            'InstanceId', 'InstanceIp', 'InstanceName',
            'StackName', 'StartCronJob', 'StopCronJob', 'Timezone',
            'StackId', 'accountId', 'Region', 'ProvisioningEngineer'
        ]];
    }

    const instanceIds = (req.body.InstanceID || '')
        .split(',').map(id => id.replace(/"/g, '').trim());

    const instanceIps = (instanceip[0] || '')
        .split(',').map(ip => ip.replace(/"/g, '').trim());

    const instanceNames = (instancename[0]|| '')
        .split(',').map(name => name.replace(/"/g, '').trim());

           const parts = req.body.actualStartCronJob.trim().split(' ');
   
    // Convert AWS DOW to standard (subtract 1, wrap Sunday from 1 → 0)
    let dow = parts[4];
    if (dow !== '?' && dow !== '*') {
        dow = String((parseInt(dow, 10) + 6) % 7); // 1 → 0 (Sunday), 2 → 1 (Monday), etc.
        parts[4] = dow;
    }
    // Remove year (since cronstrue doesn't support it)
    let x= parts.slice(0, 5).join(' ');

    const parts2 = req.body.actualStopCronJob.trim().split(' ');
   
    // Convert AWS DOW to standard (subtract 1, wrap Sunday from 1 → 0)
    let dow2 = parts2[4];
    if (dow2 !== '?' && dow2 !== '*') {
        dow2 = String((parseInt(dow2, 10) + 6) % 7); // 1 → 0 (Sunday), 2 → 1 (Monday), etc.
        parts2[4] = dow2;
    }
    // Remove year (since cronstrue doesn't support it)
    let x2= parts2.slice(0, 5).join(' ');
    instanceIds.forEach((id, index) => {
        const ip = instanceIps[index] || '';
        const name = instanceNames[index] || '';



// Convert to ISO string
const isoString = finishTime.toISOString(); // e.g., "2025-06-06T12:30:00.000Z"

// Replace 'T' with 'time'
const formatted = isoString.replace('T', '   ');

console.log(formatted); // Output: "2025-06-06time12:30:00.000Z"

// // Format hours and minutes as HH:MM (in local time)
// const hours = String(finishTime.getHours()).padStart(2, '0');
// const minutes = String(finishTime.getMinutes()).padStart(2, '0');
// const timePart = `${hours}:${minutes}`;

// // Combine both
// const formatted = `${datePart} ${timePart}`;

 // e.g., "2025-06-06 14:30"

        rows.push([
            id,
            
            req.body.StackName || '',
           cronstrue.toString(x , { locale: "en" }) || '',
            cronstrue.toString(x2 , { locale: "en" }) || '',
            req.body.Timezone || '',
       StackId[0] || '',
           `AccountId:${ req.body.accountId}` || '',
            req.body.Region || '',
            req.body.ProvisioningEngineer || '',
            ip,
            name,
              accountname[0] || '',
              req.body.cronJobType || '',
              ritm[0] || '',
              formatted, 
        ]);
    });

    // Convert rows to CSV
    const sheet = XLSX.utils.aoa_to_sheet(rows);
    const csvOutput = XLSX.utils.sheet_to_csv(sheet);

    // Upload updated CSV to S3
    const uploadParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/CustomSchedule.csv',
        Body: csvOutput,
        ContentType: 'text/csv',
    };

    await s3.putObject(uploadParams).promise();
    console.log('CSV file updated successfully!');
    
} catch (error) {
    console.error('Error updating CSV file:', error);
    return res.status(500).send('Error writing to CSV');
}
      
    // await storeDataInS3(ticketNumber, executionID,instancename,accountId, documentName, startTime,endTime,status, instanceId,businesscontact,email,accountname,servicenownumber);
      AWS.config.update({ credentials: originalCredentials });
      sendUpdate('Execution Successfull Sending Email');
      await new Promise(resolve => setTimeout(resolve, 20000));
     console.log(cronstrue.toString(req.body.StartCronJob));
      
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 try{      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `<EMAIL>`,
         cc: `<EMAIL> `,
        subject: `U-Manage - EC2 Instance Custom Scheduler`,
        html: `<p>Hi,</p>  

<p>    We are pleased to inform you that the Instance Custom Scheduler creation is
    <strong>${executionDetails.AutomationExecution.AutomationExecutionStatus}</strong>.
    Below are the details of the operation:</p>  

<ul>
  <li><strong>Instance ID:</strong> ${req.body.InstanceID}</li>
   <li><strong>Instance IP:</strong> ${instanceip[0]}</li>
    <li><strong>Instance Name:</strong> ${instancename[0]}</li>
  <li><strong>Start Time(Instance Start time):</strong> ${cronstrue.toString(req.body.actualStartCronJob)}</li>
  <li><strong>Stop Time(Instance Stop Time):</strong> ${cronstrue.toString(req.body.actualStopCronJob)}</li>
  <li><strong>Timezone:</strong> ${req.body.Timezone}</li>
</ul>

<p>Thanks,<br>Enterprise AWS Cloud Services</p>
<p> Note: This is a system generated e-mail. Please do not respond</p>
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</p>

    `
   
  ,
});
console.log('Execution Successfull Sending Email');
console.log(req.body);
}catch(err){
  console.log(err);
}
      sendUpdate('Successfull');
///// res.end('Successfull');

}else{    
throw new Error('Scheduler  Creation failed since instance is already part of another schedule');}
    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `<EMAIL>`,
         cc: `<EMAIL>`,
        subject: `Create Schedule Action Failed Through U-manage`,
        html: `<p>Hi,</p>  

<p>We are pleased to inform you that the custom scheduler operation has been  Failed. Below are the details of the operation:</p>  

<ul>
<li><strong>Status:</strong> Failed ${err}</li>
 
</ul>

<p>Thanks,<br>Enterprise AWS Cloud Services</p>
<p> Note: This is a system generated e-mail. Please do not respond</p>
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</p>

    `
   
  ,
});
      await new Promise(resolve => setTimeout(resolve, 10000));
      sendUpdate(`Error: ${err}`);
      await new Promise(resolve => setTimeout(resolve, 10000));
      res.end(`Error: ${err}`);
    }
  };
  
  const handledeleteSSMRequest = async (req, res, documentName) => {
    console.log(req.body);

    //const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} =  req.body;
 console.log("i am in custom Scheduler");
    let firstname= req.body.ProvisioningEngineer;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');

    const sendUpdate = (message) => {
      res.write(`${message }\n\n`);
    };
// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
// const combinedString = req.body.securityGroupIds.join(',');
let params;

     params = {
        DocumentName:'Update_Custom_Instance_Schedule_Stack',
        Parameters: {
          InstanceID: [req.body.InstanceID],
         StackID:[req.body.StackId],
          AssumeRole: [`arn:aws:iam::${req.body.accountId}:role/CrossAccountAccessRole`]
        }
      };
      console.log(params);
 
    try {
      // AWS.config.update({ credentials: originalCredentials });
      if(req.body.accountId!=************){
      const credentials = await assumeRole(req.body.accountId,y);
      await initializeAWS(credentials, req.body.Region);}else{AWS.config.update({
        region: req.body.Region
      });}
      const { ssm } = configureAWS();
      console.log("after initilize");
    //   const ticketNumber = '1';
    console.log("triggering ssm document");
      let executionID;
      const startTime = new Date();
      console.log("triggering 146ssm document");
      let data;
     try{

         data = await ssm.startAutomationExecution(params).promise();
     } catch (error) {
        console.error('Error fetching execution status:', error);
      
      }
      
      console.log("triggering 148ssm document");
      executionID = data.AutomationExecutionId;
      console.log(originalCredentials);
      AWS.config.update({ credentials: originalCredentials });
      const getAutomationssmExecutionStatus1 = async (ssm, executionId) => {
        try {
          const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
          console.log('Automation execution data:', data); // Log entire response
          const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
          return data;
        } catch (error) {
          console.error('Error fetching execution status:', error);
        
        }
      };
      let servicenownumber;
        // Poll status and fetch detailed result
        const pollAutomationssmExecutionStatus1 = async (ssm, executionId, interval = 1000) => {
          return new Promise((resolve, reject) => {
            const intervalId = setInterval(async () => {
              try {
                const data = await getAutomationssmExecutionStatus1(ssm, executionId);
                const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
                console.log('Current status:', status);
                
               
                const targetStep = data.AutomationExecution.StepExecutions.find(
                  step => step.StepName === 'Snow_Req_Creation'
                );
                if (targetStep && targetStep.Outputs) {
                    console.log("Step Outputs:", targetStep.Outputs);
                   //servicenownumber= targetStep.Outputs.RITM[0];
                   try{ 
                //    console.log(targetStep.Outputs.Output);
                //    console.log(targetStep.Outputs.Output[0]);
                } catch (error) {
                   console.log(error);
                  }
                  } else {
                    console.log("No outputs found for the specified step.");
                  }
                sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
                if (['Success', 'Failed', 'TimedOut', 'Cancelled','Pending'].includes(status)) {
                    if ([ 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                        sendUpdate(`Error: ${data.AutomationExecution.CurrentStepName} step is ${status}`);
                        throw new Error('Instance Creation failed');
                    }
                  clearInterval(intervalId);
                  resolve(status);
                }
              } catch (error) {
                clearInterval(intervalId);
                reject(error);
              }
            }, interval);
          });
        };
      // Poll status and fetch detailed result
      const status = await pollAutomationssmExecutionStatus1(ssm, executionID);
      const executionDetails = await getAutomationssmExecutionStatus1(ssm, executionID);
      // const data = await getAutomationssmExecutionStatus1(ssm, executionId);
      const status2= executionDetails.AutomationExecution ? executionDetails.AutomationExecution.AutomationExecutionStatus : 'Unknown';

     

      
      const finishTime = new Date();
     const endTime =new Date();
    
    console.log("before logs");

     

//try {
//    const s3 = new AWS.S3();
//     const getObjectParams = {
//         Bucket: 'server-provision-application',
//         Key: 'tickets/CustomSchedule.xlsx',
//     };

//     const data = await s3.getObject(getObjectParams).promise();
//     let workbook;

//     if (data.Body && data.Body.length > 0) {
//         workbook = XLSX.read(data.Body, { type: 'buffer' });
//     } else {
//         workbook = XLSX.utils.book_new();
//     }

//     const sheetName = 'Sheet1';
//     let sheet;

//     if (workbook.SheetNames.includes(sheetName)) {
//         sheet = workbook.Sheets[sheetName];
//     } else {
//         sheet = XLSX.utils.aoa_to_sheet([[
//             'InstanceId', 'StackName', 'StartCronJob', 'StopCronJob', 'Timezone',
//             'StackId', 'accountId', 'Region', 'ProvisioningEngineer'
//         ]]);
//         XLSX.utils.book_append_sheet(workbook, sheet, sheetName);
//     }

//     const newRow = [
//         req.body.InstanceID || '',
//         req.body.StackName || '',
//         req.body.StartCronJob || '',
//         req.body.StopCronJob || '',
//         req.body.Timezone || '',
//            req.body.StackId|| '',
//         req.body.accountId || '',
//         req.body.Region || '',
//         req.body.ProvisioningEngineer || ''
//     ];

//     const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });

//     // Replace row based on StackId (which is at column 6 / index 5)
//     let replaced = false;
//     for (let i = 1; i < jsonData.length; i++) {
//       if (jsonData[i][5] === req.body.StackId&&jsonData[i][0] === req.body.InstanceID) {
      
//           jsonData.splice(i, 1); // Remove the row
//           modified = true;
//           break;
        
//       }
//     }
     
// console.log(jsonData);
//     const updatedSheet = XLSX.utils.aoa_to_sheet(jsonData);
//     workbook.Sheets[sheetName] = updatedSheet;

//     const updatedFile = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

//     const uploadParams = {
//         Bucket: 'server-provision-application',
//         Key: 'tickets/CustomSchedule.xlsx',
//         Body: updatedFile,
//         ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
//     };

//     await s3.putObject(uploadParams).promise();
//     console.log('Excel file updated successfully!');
// } catch (error) {
//     console.error('Error updating Excel file:', error);
//     return res.status(500).send('Error writing to Excel');
// }
try {
    const s3 = new AWS.S3();
    const getObjectParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/CustomSchedule.csv',
    };

    const data = await s3.getObject(getObjectParams).promise();
    let rows = [];

    if (data.Body && data.Body.length > 0) {
        const csvText = data.Body.toString('utf-8');
        const workbook = XLSX.read(csvText, { type: 'string' });
        const sheet = workbook.Sheets[workbook.SheetNames[0]];
        rows = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    } else {
        // Add header if file is empty
        rows = [[
            'InstanceId', 'StackName', 'StartCronJob', 'StopCronJob', 'Timezone',
            'StackId', 'accountId', 'Region', 'ProvisioningEngineer'
        ]];
    }

    const newRow = [
        req.body.InstanceID || '',
        req.body.StackName || '',
        req.body.StartCronJob || '',
        req.body.StopCronJob || '',
        req.body.Timezone || '',
        req.body.StackId || '',
        req.body.accountId || '',
        req.body.Region || '',
        req.body.ProvisioningEngineer || ''
    ];

    // Replace row based on StackId (index 5) and InstanceID (index 0)
    let replaced = false;
    for (let i = 1; i < rows.length; i++) {
        if (
            rows[i][5] === req.body.StackId &&
            rows[i][0] === req.body.InstanceID
        ) {
            rows.splice(i, 1); // Remove the existing row
            replaced = true;
            break;
        }
    }

    // Add the new (or updated) row
    

    // Convert to CSV
    const newSheet = XLSX.utils.aoa_to_sheet(rows);
    const csvOutput = XLSX.utils.sheet_to_csv(newSheet);

    const uploadParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/CustomSchedule.csv',
        Body: csvOutput,
        ContentType: 'text/csv',
    };

    await s3.putObject(uploadParams).promise();
    console.log('CSV file updated successfully!');
   // return res.status(200).send('CSV updated');
} catch (error) {
    console.error('Error updating CSV file:', error);
    //return res.status(500).send('Error writing to CSV');
}

try {
   const s3 = new AWS.S3();
    const getObjectParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/CustomSchedulebackup.xlsx',
    };

    const data = await s3.getObject(getObjectParams).promise();
    let workbook;

    if (data.Body && data.Body.length > 0) {
        workbook = XLSX.read(data.Body, { type: 'buffer' });
    } else {
        workbook = XLSX.utils.book_new();
    }

    const sheetName = 'Sheet1';
    let sheet;

    if (workbook.SheetNames.includes(sheetName)) {
        sheet = workbook.Sheets[sheetName];
    } else {
        sheet = XLSX.utils.aoa_to_sheet([[
            'InstanceId', 'StackName', 'StartCronJob', 'StopCronJob', 'Timezone',
            'StackId', 'accountId', 'Region', 'ProvisioningEngineer'
        ]]);
        XLSX.utils.book_append_sheet(workbook, sheet, sheetName);
    }
 const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
  
  

const targetInstanceId = req.body.InstanceID;
const targetStackId = req.body.StackId;

// Find and update the row
for (let i = 1; i < jsonData.length; i++) {
  const row = jsonData[i];
  if (row[0] === targetInstanceId && row[5] === targetStackId) {
    // Update the desired cell, for example, column index 10
    row[16] = req.body.ProvisioningEngineer; // Replace with your actual value
    break;
  }
}


   
//console.log(jsonData);
    const updatedSheet = XLSX.utils.aoa_to_sheet(jsonData);
    workbook.Sheets[sheetName] = updatedSheet;

    const updatedFile = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

    const uploadParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/CustomSchedulebackup.xlsx',
        Body: updatedFile,
        ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };

    await s3.putObject(uploadParams).promise();
    console.log('Excel file updated successfully!');
} catch (error) {
    console.error('Error updating Excel file:', error);
    return res.status(500).send('Error writing to Excel');
}
      
    // await storeDataInS3(ticketNumber, executionID,instancename,accountId, documentName, startTime,endTime,status, instanceId,businesscontact,email,accountname,servicenownumber);
      AWS.config.update({ credentials: originalCredentials });
      sendUpdate('Execution Successfull Sending Email');
      await new Promise(resolve => setTimeout(resolve, 20000));
     
      
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 try{      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${req.body.ProvisioningEngineer}`,
         cc: `<EMAIL> `,
        subject: `U-Manage - EC2 Instance Custom Scheduler`,
        html: `<p>Hi,</p>  

<p>    We are pleased to inform you that the Instance Custom Scheduler has been Removed
    <strong>${executionDetails.AutomationExecution.AutomationExecutionStatus}</strong>.
    Below are the details of the operation:</p>  

<ul>
   <li><strong>Instance ID:</strong> ${req.body.InstanceID}</li>
  <li><strong>Start Time(Instance Start time):</strong> ${  req.body.StartCronJob }</li>
  <li><strong>Stop Time(Instance Stop Time):</strong> ${    req.body.StopCronJob}</li>
  <li><strong>Timezone:</strong> ${req.body.Timezone}</li>
</ul>

<p>Thanks,<br>Enterprise AWS Cloud Services</p>
<p> Note: This is a system generated e-mail. Please do not respond</p>
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</p>

    `
   
  ,
});
console.log('Execution Successfull Sending Email');
}catch(err){
  console.log(err);
}
      //sendUpdate('Successfull');
      res.end('Successfull');
     

    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${req.body.ProvisioningEngineer}`,
         cc: '<EMAIL>,',
        subject: `Create Action Failed Through U-manage`,
        html: `<p>Hi,</p>  

<p>We are pleased to inform you that the custom schedule remove operation has been  Failed. Below are the details of the operation:</p>  

<ul>
<li><strong>Status:</strong> Failed ${err}</li>
 
</ul>

<p>Thanks,<br>Enterprise AWS Cloud Services</p>
<p> Note: This is a system generated e-mail. Please do not respond</p>
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</p>

    `
   
  ,
});
      await new Promise(resolve => setTimeout(resolve, 10000));
      sendUpdate(`Error: ${err}`);
      await new Promise(resolve => setTimeout(resolve, 10000));
      res.end(`Error: ${err}`);
    }
  };
  
 
 router.get('/CustomscheduleLogs', async (req, res) => {
     const s3 = new AWS.S3();
     
    //      if (err) {
    //        return res.status(500).send(err.message);
    //      }
  
    //      const workbook = XLSX.read(data.Body, { type: 'buffer' });
    //          const sheetName = workbook.SheetNames[0]; // Read the first sheet
    //          const sheet = workbook.Sheets[sheetName];
 
    //          // Convert sheet data to JSON
    //          const results = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    //      console.log(results);
    //          // Map data to expected format (assuming columns are in order: AccountID, InstanceIDs, Region)
               
            
    //          const formattedResults = results.map(row => ({
    //              InstanceID: row[0] || '',
    //              StackName: row[1] || '',
    //              StartCronJob: row[2] || '',
    //                StopCronJob: row[3] || '',
    //              Timezone: row[4] || '',
    //              StackId: row[5] || '',
    //              accountId: row[6] || '',
    //                Region: row[7] || '',
    //              ProvisioningEngineer: row[8] || ''
    //          }));
 
    //          res.json(formattedResults);
    //    });
    //  } catch (err) {
    //   console.log(err);
    //    res.status(500).send(err.message);
    //  }
     try {
    const params = {
      Bucket: 'server-provision-application',
      Key: 'tickets/CustomSchedule.csv'
    };
  //  console.log(params);

    s3.getObject(params, (err, data) => {
      if (err) {
        return res.status(500).send(err.message);
      }

      const csvText = data.Body.toString('utf-8');
      const workbook = XLSX.read(csvText, { type: 'string' });
      const sheetName = workbook.SheetNames[0];
      const sheet = workbook.Sheets[sheetName];

      const results = XLSX.utils.sheet_to_json(sheet, { header: 1 });
     
      const formattedResults = results.map(row => ({
        InstanceID: row[0] || '',
        StackName: row[1] || '',
        StartCronJob: row[2] || '',
        StopCronJob: row[3] || '',
        Timezone: row[4] || '',
        StackId: row[5] || '',
        accountId:String(row[6] || '').split(':')[1] || '',
        Region: row[7] || '',
        ProvisioningEngineer: row[8] || '',
        IP: row[9] || '',
        Name: row[10] || '',
         State: row[15] || '',
        accountname: row[11] || '', 
        type: row[12],
        ritm: row[13],
        time: row[14]// Assuming State is the 12th column
      }));
 
            
          // console.log(formattedResults);

      res.json(formattedResults);
    });
  } catch (err) {
    console.log(err);
    res.status(500).send(err.message);
  }
   });
 
 router.post('/RemovecustomSchedule', async (req, res) => {
    // InstanceId	StackName	StartCronJob	StopCronJob	Timezone	StackId	accountId	Region	 Expecting accountId and instanceIdsToRemove in the request body
     console.log("remove");
     console.log(req.body);
   
 
     try {
       await handledeleteSSMRequest(req,res,'');
       //res.status(200).json({ message: 'Instance IDs removed successfully.' });
     } catch (err) {
       console.log(err);
       const transporter = nodemailer.createTransport({
         host: 'relay.assaabloy.net',
         port: 25,
         secure: false,
         auth: {
           user: '<EMAIL>',
           pass: '',
         },
       });
   
       const mail = await transporter.sendMail({
         from: '<EMAIL>',
         to: `${req.body.ProvisioningEngineer}`,
         // cc: `<EMAIL>,`,
         subject: `${instanceIds} Remove from Scheduler action went WRONG `,
         html: `
     <p>Hi ,</p>
     <p>This is to inform you that <b>${instanceIds}</b> is not properly removed from  Scheduler in  <b>${filename}</b>.</p>
     <p><NAME_EMAIL> for details</p>
     <p>Thanks,<br>GDIAS Team</p>
   `,   });
      
      // res.status(500).json({ error: err.message });
 
     }
   });
  router.post('/', (req, res) => handleSSMRequest(req, res, 'WIN_test'));
  
  return router;
};
 