import React from 'react';
import './AboutDomain.css'; // Ensure to create this CSS file for styling
import Domain from '../assets/about/Domain.png';
const AboutDomainChecker = () => (
  <div className="about-domain-checker-container">
    <h2 className="about-domain-checker-title">Domain Checker Action</h2>
    <p className="about-domain-checker-description">
      The Domain Checker action is used to verify the domain status of a Windows instance. This helps ensure that the instance is correctly associated with the intended domain and avoids potential connectivity or access issues.
    </p>
    <h3 className="about-domain-checker-subtitle">Steps to Check Domain Status</h3>
    <ol className="about-domain-checker-steps">
      <li className="about-domain-checker-step">
        <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
        <div className="about-domain-checker-image-container">
          <img src={Domain} alt="Select Account Sample" className="about-domain-checker-image" />
        </div>
      </li>
      <li className="about-domain-checker-step">
        <strong>Select the Region:</strong> Pick the region assigned to that account, as instance availability is region-specific.
      </li>
      <li className="about-domain-checker-step">
        <strong>Select the Instance:</strong> Locate the instance by either its ID or Name for easy identification.
      </li>
      <li className="about-domain-checker-step">
        <strong>Review Details:</strong> Confirm all instance details, such as configuration and status.
      </li>
      <li className="about-domain-checker-step">
        <strong>Acknowledge:</strong> Check the acknowledgment box to confirm understanding of the actions.
      </li>
      <li className="about-domain-checker-step">
        <strong>Click the "Check Domain" Button:</strong> Execute the domain check action. Ensure that the SSM agent is running on the instance.
      </li>
    </ol>
    <p className="about-domain-checker-note">
      Note: The Domain Checker works only for Windows instances, and the SSM agent must be running on the instance. Errors may occur if the agent is inactive.
    </p>
  </div>
);

export default AboutDomainChecker;
