const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');
const nodemailer = require('nodemailer');
const fs = require("fs");
const XLSX = require('xlsx');
const { error } = require('console');
const axios = require('axios');
const https = require('https');
// Export the function to set up the router with necessary utilities
module.exports = (generateTicketNumber, storeDataInS3) => {
  let originalCredentials = AWS.config.credentials;
 
  // Function to assume an IAM role and return temporary credentials
  const assumeRole = async (accountId,firstname) => {
    console.log("assumerole");
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    
    try {
      const data = await sts.assumeRole(params).promise();
      return data.Credentials;
    } catch (error) {
      AWS.config.update({ credentials: null });
        console.error('Error assuming role:', error.code, error.message);
        if (error.code === 'AccessDenied') {
          const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
            // Handle AccessDenied specifically, maybe log the accounts involved
       
     
    }else{
      console.error('Error assuming role:', error);
      throw error;
    }}
  };
 
  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };
 
  // Function to configure AWS services (SSM, S3, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      cloudFormation: new AWS.CloudFormation()
    };
  };
  const handleSSMRequest = async (req, res, documentName) => {
    console.log(req.body);

    //const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} =  req.body;
 console.log("i am in new ssm");
    let firstname= req.body.ProvisioningEngineer;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');

    const sendUpdate = (message) => {
      res.write(`${message }\n\n`);
    };
// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
const combinedString = req.body.securityGroupIds.join(',');

sendUpdate(`Adax Object is created ${adaxname}`);
    let params;
   
      console.log(req.body);  
      
      params= {
        DocumentName: req.body.platform=='windows'?'Isolated_Windows_Instance_Stack':'Isolated_Linux_Instance_Stack',
        Parameters: {
          InstanceName: [req.body.InstanceName],
         
          ContactingCustomer: [req.body.ProvisioningEngineer ],
          AffectingUser: [req.body.ProvisioningEngineer ],
         
          ShortDescription: [` Instance Creation through U-manage`],
          Description: [` Instance Creation through U-manage InsatnceName:${req.body.InstanceName} Created in AccountID :${req.body.accountId} Region:${req.body.Region}`],
          RequestedFor: [req.body.ProvisioningEngineer],
          InstanceType: [req.body.InstanceType ],
          AMI: [req.body.AMI ],
          VolumeSize: [req.body.volume1.ebsVolumeSize==''?'50' :req.body.volume1.ebsVolumeSize],
          VolumeSize1: [req.body.volume2!=null?req.body.volume2.ebsVolumeSize:'1'],
          VolumeSize2: [req.body.volume3!=null?req.body.volume3.ebsVolumeSize:'1'],
          VolumeSize3: [req.body.volume4!=null?req.body.volume4.ebsVolumeSize:'1'],
          VolumeSize4: [req.body.volume5!=null?req.body.volume5.ebsVolumeSize:'1'],
          VolumeType: [req.body.volume1.ebsVolumeType],
          VolumeType1: [req.body.volume2!=null?req.body.volume2.ebsVolumeType: 'gp3'],
          VolumeType2: [req.body.volume3!=null?req.body.volume3.ebsVolumeType: 'gp3'],
          VolumeType3: [req.body.volume4!=null?req.body.volume4.ebsVolumeType: 'gp3'],
          VolumeType4: [req.body.volume5!=null?req.body.volume5.ebsVolumeType: 'gp3'],
          DeviceName: [req.body.volume1.DeviceName],
          DeviceName1: [req.body.volume2!=null?req.body.volume2.DeviceName: 'xvdb'],
          DeviceName2: [req.body.volume3!=null?req.body.volume3.DeviceName: 'xvdd'],
          DeviceName3: [req.body.volume4!=null?req.body.volume4.DeviceName: 'xvdf'],
          DeviceName4: [req.body.volume5!=null?req.body.volume5.DeviceName: 'xvdg'],
          SecurityGroupIds: [combinedString]  ,
          SubnetId: [req.body.subnetId ],
          UseBlockDeviceMappings: [req.body.UseBlockDeviceMappings],
          CostCenter: [req.body.CostCenter ],
          CostCenterDescription: [req.body.CostCenterDescription],
          SupportTier: [req.body.SupportTier],
          SupportTierDescription: [req.body.SupportTierDescription],
          ProvisioningEntity: [req.body.ProvisioningEntity],
          BusinessArea: [req.body.BusinessArea],
          BusinessContact: [req.body.BusinessContact],
          BusinessContactEmail: [req.body.BusinessContactEmail],
          BusinessSegment: [req.body.BusinessSegment],
          BusinessSegmentDescription: [req.body.BusinessSegmentDescription],
          TechnicalContact: [req.body.TechnicalContact],
          TechnicalContactEmail: [req.body.TechnicalContactEmail],
          Environment: [req.body.Environment],
          NetworkLocation: [req.body.NetworkLocation],
          FunctionalArea: [req.body.FunctionalArea],
          ProvisioningEngineer: [req.body.ProvisioningEngineer],
          BackupPlan: [req.body.BackupPlan],
          Assumerole: [`arn:aws:iam::${req.body.accountId}:role/CrossAccountAccessRole`]
        }
      }; 
    
      console.log(params);
 
    try {
      // AWS.config.update({ credentials: originalCredentials });
      if(req.body.accountId!=************){
      const credentials = await assumeRole(req.body.accountId,y);
      await initializeAWS(credentials, req.body.Region);}else{AWS.config.update({
        region: req.body.Region
      });}
      const { ssm } = configureAWS();
      console.log("after initilize");
    //   const ticketNumber = '1';
    console.log("triggering ssm document");
      let executionID;
      const startTime = new Date();
      console.log("triggering 146ssm document");
      let data;
     try{

         data = await ssm.startAutomationExecution(params).promise();
     } catch (error) {
        console.error('Error fetching execution status:', error);
      
      }
      
      console.log("triggering 148ssm document");
      executionID = data.AutomationExecutionId;
      console.log(originalCredentials);
      AWS.config.update({ credentials: originalCredentials });
      const getAutomationssmExecutionStatus1 = async (ssm, executionId) => {
        try {
          const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
          console.log('Automation execution data:', data); // Log entire response
          const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
          return data;
        } catch (error) {
          console.error('Error fetching execution status:', error);
        
        }
      };
      let servicenownumber;
        // Poll status and fetch detailed result
        const pollAutomationssmExecutionStatus1 = async (ssm, executionId, interval = 1000) => {
          return new Promise((resolve, reject) => {
            const intervalId = setInterval(async () => {
              try {
                const data = await getAutomationssmExecutionStatus1(ssm, executionId);
                const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
                console.log('Current status:', status);
                
               
                const targetStep = data.AutomationExecution.StepExecutions.find(
                  step => step.StepName === 'Snow_Req_Creation'
                );
                if (targetStep && targetStep.Outputs) {
                    console.log("Step Outputs:", targetStep.Outputs);
                   //servicenownumber= targetStep.Outputs.RITM[0];
                   try{ 
                //    console.log(targetStep.Outputs.Output);
                //    console.log(targetStep.Outputs.Output[0]);
                } catch (error) {
                   console.log(error);
                  }
                  } else {
                    console.log("No outputs found for the specified step.");
                  }
                sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
                if (['Success', 'Failed', 'TimedOut', 'Cancelled','Pending'].includes(status)) {
                    if ([ 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                        sendUpdate(`Error: ${data.AutomationExecution.CurrentStepName} step is ${status}`);
                        throw new Error('Instance Creation failed');
                    }
                  clearInterval(intervalId);
                  resolve(status);
                }
              } catch (error) {
                clearInterval(intervalId);
                reject(error);
              }
            }, interval);
          });
        };
      // Poll status and fetch detailed result
      const status = await pollAutomationssmExecutionStatus1(ssm, executionID);
      const executionDetails = await getAutomationssmExecutionStatus1(ssm, executionID);
      // const data = await getAutomationssmExecutionStatus1(ssm, executionId);
      const status2= executionDetails.AutomationExecution ? executionDetails.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      console.log(executionDetails);
      console.log(executionDetails.AutomationExecution);
      const logData = `
Outputs: ${JSON.stringify(executionDetails.AutomationExecution?.Outputs, null, 2)}
`;

// Append log data to "execution_logs.txt"
fs.appendFileSync("execution_logs.txt", logData + "\n\n", "utf8");

      console.log(executionDetails.AutomationExecution.Outputs);
      const finishTime = new Date();
     // console.log(executionDetails.AutomationExecution.Outputs.Windows_Instance_Stack.OutputPayload);
      console.log(executionDetails.AutomationExecution.Outputs["Describe_Stack_Resource.InstanceID"][0]);
    // const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} = req.body;
      const endTime =new Date();
    
    
console.log('Execution Successfull  Email sent');
      sendUpdate('Successfull Executed Storing Logs');
      await new Promise(resolve => setTimeout(resolve, 10000));
     
      try {
        // Define S3 parameters to get the existing Excel file
        const getObjectParams = {
            Bucket: 'server-provision-application',
            Key: 'tickets/Create.xlsx', // Change this to the path where your Excel file is stored
        };
        const s3 = new AWS.S3();
        // Fetch the existing file from S3
        const data = await s3.getObject(getObjectParams).promise();
        let workbook;
     
        if (data.Body.length > 0) {
            // Read the existing file
            workbook = XLSX.read(data.Body, { type: 'buffer' });
        } else {
            // Create a new workbook if none exists
            workbook = XLSX.utils.book_new();
        }
     
         // Get or create the sheet
        const sheetName = 'Tickets';
        let sheet;
     
        if (workbook.SheetNames.includes(sheetName)) {
            sheet = workbook.Sheets[sheetName];
        } else {
            // Create a new sheet with headers if the sheet does not exist
            sheet = XLSX.utils.aoa_to_sheet([[
                'InstanceName', 'InstanceType', 'AMI', 'SecurityGroupIds', 'SubnetId',
                'IAMRoleName', 'CostCenter', 'CostCenterDescription', 'SupportTier',
                'SupportTierDescription', 'InstanceSource', 'ProvisioningEntity',
                'ProvisioningJustification', 'BusinessArea', 'BusinessContact',
                'BusinessContactEmail', 'BusinessSegment', 'BusinessSegmentDescription',
                'TechnicalContact', 'TechnicalContactEmail', 'Environment',
                'NetworkLocation', 'FunctionalArea', 'ProvisioningEngineer', 'BackupPlan','result'
            ]]);
        }
        let x="";
        let newRow;
        try{
         
          newRow = [
            adaxname,
           executionDetails.AutomationExecution.Outputs["DescribeInstances.PrivateIP"][0],
           executionDetails.AutomationExecution.Outputs["Describe_Stack_Resource.InstanceID"][0],
            req.body.InstanceType,
            req.body.AMI,
           combinedString,
            req.body.subnetId,
            'HID-EC2-SSM-role',
            `${req.body.CostCenter}`,
            req.body.CostCenterDescription,
            req.body.SupportTier,
            req.body.SupportTierDescription,
            'NEW',
            req.body.ProvisioningEntity,
         '',
            req.body.BusinessArea,
            req.body.BusinessContact,
            req.body.BusinessContactEmail,
            `${req.body.BusinessSegment}`,
            req.body.BusinessSegmentDescription,
            req.body.TechnicalContact,
            req.body.TechnicalContactEmail,
            req.body.Environment,
            req.body.NetworkLocation,
            req.body.FunctionalArea,
            req.body.ProvisioningEngineer,
            req.body.BackupPlan || '',
            status,
            startTime,
            finishTime,
            executionDetails.AutomationExecution.Outputs["DescribeInstances.VolumeID1"][0],
            executionDetails.AutomationExecution.Outputs["DescribeInstances.VolumeID2"][0],
            executionDetails.AutomationExecution.Outputs["DescribeInstances.VolumeID1"][0],
            executionDetails.AutomationExecution.Outputs["DescribeInstances.VolumeID1"][0],
          
    
      ];}catch{
          x=result;
           newRow = [
            adaxname,
            "",
            "",
            params.InstanceType,
            params.AMI,
            params.securityGroupIds,
            params.subnetId,
            'HID-EC2-SSM-role',
            `  ${params.CostCenter}`,
            params.CostCenterDescription,
            params.SupportTier,
            params.SupportTierDescription,
            'NEW',
            params.ProvisioningEntity,
            params.ProvisioningJustification,
            params.BusinessArea,
            params.BusinessContact,
            params.BusinessContactEmail,
            ` ${params.BusinessSegment}`,
            params.BusinessSegmentDescription,
            params.TechnicalContact,
            params.TechnicalContactEmail,
            params.Environment,
            params.NetworkLocation,
            params.FunctionalArea,
            params.ProvisioningEngineer,
            params.BackupPlan || '',
            x,
            startTime,
            finishTime,
           
            "",
            "",
            "",
            "",
    
        ];
        }
        // Convert sheet to JSON
        const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
     
        // Prepare a new row with parameters
        
        console.log(x);
        // Append the new row to jsonData
        jsonData.push(newRow);
     
        // Convert updated JSON back to sheet
        const updatedSheet = XLSX.utils.aoa_to_sheet(jsonData);
        workbook.Sheets[sheetName] = updatedSheet;
     
        // Convert workbook to buffer
        const updatedFile = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
     
        // Upload the updated file back to S3
        const uploadParams = {
            Bucket: 'server-provision-application',
            Key: 'tickets/Create.xlsx', // Ensure this path matches where you want to store the file
            Body: updatedFile,
            ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        };
     
        await s3.putObject(uploadParams).promise();
        console.log('Excel file updated successfully!');
    } catch (error) {
        console.error('Error updating Excel file:', error);
    }


      
      await storeDataInS3(ticketNumber, executionID,instancename,accountId, documentName, startTime,endTime,status, instanceId,businesscontact,email,accountname,servicenownumber);
      AWS.config.update({ credentials: originalCredentials });
      sendUpdate('Execution Successfull Sending Email');
      await new Promise(resolve => setTimeout(resolve, 20000));
     
      
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 try{      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${req.body.ProvisioningEngineer}`,
         cc: `${req.body.BusinessContact} `,
        subject: `Create Action Through U-manage`,
        html: `<p>Hi,</p>  

<p>We are pleased to inform you that the EC2 instance provision operation has been  ${executionDetails.AutomationExecution.AutomationExecutionStatus}. Below are the details of the operation:</p>  

<ul>
<li><strong>Status:</strong> ${executionDetails.AutomationExecution.AutomationExecutionStatus}</li>
  <li><strong>Instance Name:</strong> ${adaxname}</li>
  <li><strong>Instance Type:</strong> ${req.body.InstanceType}</li>
  <li><strong>Account ID:</strong> ${req.body.accountId}</li>
  <li><strong>Region:</strong> ${req.body.Region}</li>
  <li><strong>Instance ID:</strong> ${executionDetails.AutomationExecution.Outputs["Describe_Stack_Resource.InstanceID"][0]}</li>
  <li><strong>Instance IP:</strong> ${executionDetails.AutomationExecution.Outputs["DescribeInstances.PrivateIP"][0]}</li>
  <li><strong>Image ID:</strong> ${req.body.AMI}</li>
  <li><strong>Security Groups:</strong> ${combinedString}</li>
  <li><strong>Subnet IDs:</strong> ${req.body.subnetId}</li>
  <li><strong>Cost Center:</strong> ${req.body.CostCenter}</li>
  <li><strong>Cost Center Description:</strong> ${req.body.CostCenterDescription}</li>
  <li><strong>Support Tier:</strong> ${req.body.SupportTier}</li>
  <li><strong>Support Tier Description:</strong> ${req.body.SupportTierDescription}</li>
  <li><strong>Instance Source:</strong> NEW</li>
  <li><strong>Provisioning Entity:</strong> ${req.body.ProvisioningEntity}</li>
  <li><strong>Business Area:</strong> ${req.body.BusinessArea}</li>
  <li><strong>Business Contact:</strong> ${req.body.BusinessContact}</li>
  <li><strong>Business Contact Email:</strong> ${req.body.BusinessContactEmail}</li>
  <li><strong>Business Segment:</strong> ${req.body.BusinessSegment}</li>
  <li><strong>Business Segment Description:</strong> ${req.body.BusinessSegmentDescription}</li>
  <li><strong>Technical Contact:</strong> ${req.body.TechnicalContact}</li>
  <li><strong>Technical Contact Email:</strong> ${req.body.TechnicalContactEmail}</li>
  <li><strong>Environment:</strong> ${req.body.Environment}</li>
  <li><strong>Network Location:</strong> ${req.body.NetworkLocation}</li>
  <li><strong>Functional Area:</strong> ${req.body.FunctionalArea}</li>
  <li><strong>Provisioning Engineer:</strong> ${req.body.ProvisioningEngineer}</li>
  <li><strong>Backup Plan:</strong> ${req.body.BackupPlan || 'N/A'}</li>
  <li><strong>Start Time:</strong> ${startTime}</li>
  <li><strong>Finish Time:</strong> ${finishTime}</li>
</ul>

<p>Thanks,<br>GDIAS Team</p>

<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</p>

    `
   
  ,
});
console.log('Execution Successfull Sending Email');
}catch(err){
  console.log(err);
}
      sendUpdate('Successfull');
     // res.end('Successfull');
     

    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${req.body.ProvisioningEngineer}`,
         cc: `${req.body.BusinessContact} `,
        subject: `Create Action Failed Through U-manage`,
        html: `<p>Hi,</p>  

<p>We are pleased to inform you that the EC2 instance provision operation has been  Failed. Below are the details of the operation:</p>  

<ul>
<li><strong>Status:</strong> Failed ${err}</li>
  <li><strong>Instance Name:</strong> ${req.body.InstanceName}</li>
  <li><strong>Instance Type:</strong> ${req.body.InstanceType}</li>
  <li><strong>Account ID:</strong> ${req.body.accountId}</li>
  <li><strong>Region:</strong> ${req.body.Region}</li>
  <li><strong>Image ID:</strong> ${req.body.AMI}</li>
  <li><strong>Security Groups:</strong> ${combinedString}</li>
  <li><strong>Subnet IDs:</strong> ${req.body.subnetId}</li>
  <li><strong>Cost Center:</strong> ${req.body.CostCenter}</li>
  <li><strong>Cost Center Description:</strong> ${req.body.CostCenterDescription}</li>
  <li><strong>Support Tier:</strong> ${req.body.SupportTier}</li>
  <li><strong>Support Tier Description:</strong> ${req.body.SupportTierDescription}</li>
  <li><strong>Instance Source:</strong> NEW</li>
  <li><strong>Provisioning Entity:</strong> ${req.body.ProvisioningEntity}</li>
  <li><strong>Business Area:</strong> ${req.body.BusinessArea}</li>
  <li><strong>Business Contact:</strong> ${req.body.BusinessContact}</li>
  <li><strong>Business Contact Email:</strong> ${req.body.BusinessContactEmail}</li>
  <li><strong>Business Segment:</strong> ${req.body.BusinessSegment}</li>
  <li><strong>Business Segment Description:</strong> ${req.body.BusinessSegmentDescription}</li>
  <li><strong>Technical Contact:</strong> ${req.body.TechnicalContact}</li>
  <li><strong>Technical Contact Email:</strong> ${req.body.TechnicalContactEmail}</li>
  <li><strong>Environment:</strong> ${req.body.Environment}</li>
  <li><strong>Network Location:</strong> ${req.body.NetworkLocation}</li>
  <li><strong>Functional Area:</strong> ${req.body.FunctionalArea}</li>
  <li><strong>Provisioning Engineer:</strong> ${req.body.ProvisioningEngineer}</li>
  <li><strong>Backup Plan:</strong> ${req.body.BackupPlan || 'N/A'}</li>
</ul>

<p>Thanks,<br>GDIAS Team</p>

<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</p>

    `
   
  ,
});
      await new Promise(resolve => setTimeout(resolve, 10000));
      sendUpdate(`Error: ${err}`);
      await new Promise(resolve => setTimeout(resolve, 10000));
      res.end(`Error: ${err}`);
    }
  };
  
 
 
 
 
 
  
  router.post('/', (req, res) => handleSSMRequest(req, res, 'WIN_test'));
  
  return router;
};
 