const express = require('express');
const nodemailer = require('nodemailer');
const crypto = require('crypto');
const router = express.Router();
 
const ENCRYPTION_KEY = crypto.createHash('sha256').update('umanage').digest(); // 32 bytes for AES-256
const IV_LENGTH = 16; // AES block size

function encrypt(text) {
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipheriv('aes-256-cbc', ENCRYPTION_KEY, iv);
  let encrypted = cipher.update(text, 'utf8', 'base64');
  encrypted += cipher.final('base64');
  return iv.toString('base64') + ':' + encrypted;
}



module.exports = () => {
  router.post('/', async (req, res) => {
   try {
    // Get provisioner engineer email from request
    console.log(req.body);
    const email = req.body.email;
    if (!email) {
      return res.status(400).json({ error: 'Email is required' });
    }
    // Use current date (YYYYMMDD) and time (HHmmss) for traceability
    const now = new Date();
    const dateStr = now.toISOString().slice(0,10).replace(/-/g, ''); // YYYYMMDD
    const timeStr = now.toTimeString().slice(0,8).replace(/:/g, ''); // HHmmss
    const payload = `${email}|${dateStr}${timeStr}|umanage`;
    // Encrypt the payload using the reserved key
    const encryptedPayload = encrypt(payload);
    // Final token: just the encrypted payload
    const token = encryptedPayload;
    res.status(200).json({
      token,
      issuedAt: now.toISOString(),
      expiresIn: 3 * 60 * 60 * 1000 // 3 hours in ms
    });
  } catch (error) {
    console.error('Error in handleSSMRequest:', error);
    res.status(500).json({ error: error.message });
  }
  });
 
  return router;
};


