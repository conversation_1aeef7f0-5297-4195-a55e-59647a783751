
// import React, { useEffect, useState, useMemo } from "react";
// import { MaterialReactTable } from 'material-react-table';
// import { ThemeProvider, createTheme, Dialog, DialogTitle, DialogContent, DialogActions, Button, TextField,Grid2,Typography } from "@mui/material";
// import { Box } from "@mui/material";
// import Grid2 from '@mui/material/Unstable_Grid2';
// const DataTable = () => {
 
//   const [openModal, setOpenModal] = useState(false);
//   const [selectedRowData, setSelectedRowData] = useState(null);
//   const [updatedData, setUpdatedData] = useState({});

//   const [data, setData] = useState([]);  // Original data
//   const [filteredData, setFilteredData] = useState([]); // Data to be displayed
//   const [loading, setLoading] = useState(true);
//   const defaultMaterialTheme = createTheme();
//   const [filteredCount, setFilteredCount] = useState(0);
//   const [globalFilter, setGlobalFilter] = useState("");
//   const [selectedRows, setSelectedRows] = useState({});
//   const [openDialog, setOpenDialog] = useState(false);

//   // Fetch data from backend
//   useEffect(() => {
//     fetch("https://umanage.dev.hidglobal.com/api/migration/data")
//       .then((response) => response.json())
//       .then((result) => {
//         const dataWithIds = result.map((row, index) => ({
//           ...row,
//           uniqueId:  `row-${index}`, // Use IP or index if no unique ID
//         }));

//         setData(dataWithIds);
//         setFilteredData(dataWithIds); // Initialize filteredData with full data
//         setFilteredCount(dataWithIds.length);
//         setLoading(false);
//       })
//       .catch((error) => {
//         console.error("Error fetching data:", error);
//         setLoading(false);
//       });
//   }, []);

//   const columns = useMemo(
//     () => [
//       { accessorKey: "ServerName", header: "Server Name" },
//       { accessorKey: "IPAddress", header: "IP Address" },
//       { accessorKey: "newipaddress", header: "NEW IP Address of Migrated VM" },
//       { accessorKey: "Country", header: "Country" },
//       { accessorKey: "Location", header: "Location" },
//       { accessorKey: "DependencyGroup", header: "Dependency Group" },
//       { accessorKey: "MigrationDisposition", header: "Migration Disposition" },
//       { accessorKey: "UseCase", header: "Use Case" },
//       { accessorKey: "UseCaseDescription", header: "Use Case Description" },
//       { accessorKey: "TargetAWSAccount", header: "Target AWS Account" },
//       { accessorKey: "SiteCode", header: "Site Code" },
//       { accessorKey: "_CostCenter", header: "Cost Center" },
//       { accessorKey: "_CostCenterDescription", header: "Cost Center Description" },
//       { accessorKey: "_BusinessSegment", header: "Business Segment" },
//       { accessorKey: "_BusinessSegmentDescription", header: "Business Segment Description" },
//       { accessorKey: "DeprecatedOS", header: "Deprecated OS" },
//       { accessorKey: "DomainJoined", header: "Domain Joined" },
//       { accessorKey: "PatchedtoDate", header: "Patched to Date" },
//       { accessorKey: "map_migrated", header: "Map Migrated" },
//       { accessorKey: "_SupportTier", header: "Support Tier" },
//       { accessorKey: "_SupportTierDescription", header: "Support Tier Description" },
//       { accessorKey: "_BackupPlan", header: "Backup Plan" },
//       { accessorKey: "_BackupPlanDescription", header: "Backup Plan Description" },
//       { accessorKey: "_BusinessContact", header: "Business Contact" },
//       { accessorKey: "SecondBusinessContact", header: "Second Business Contact" },
//       { accessorKey: "_BusinessContactEmail", header: "Business Contact Email" },
//       { accessorKey: "_TechnicalContact", header: "Technical Contact" },
//       { accessorKey: "_TechnicalContactEmail", header: "Technical Contact Email" },
//       { accessorKey: "_ProvisioningJustification", header: "Provisioning Justification" },
//       { accessorKey: "_NetworkLocation", header: "Network Location" },
//       { accessorKey: "_BusinessArea", header: "Business Area" },
//       { accessorKey: "_FunctionalArea", header: "Functional Area" }
//     ],
//     []
//   );

//   // **Global Filter Logic**
//   useEffect(() => {
//     let newFilteredData = data;

//     if (globalFilter) {
//       newFilteredData = newFilteredData.filter(row =>
//         Object.values(row).some(value =>
//           value?.toString().toLowerCase().includes(globalFilter.toLowerCase())
//         )
//       );
//     }
//     const selectedRowData = Object.keys(selectedRows).map((rowId) =>
//       data.find((row) => row.uniqueId === rowId)
//     ).filter(Boolean); // Remove undefined values
//     const combinedData = [...selectedRowData, ...newFilteredData].filter(
//       (row, index, self) => index === self.findIndex((r) => r.uniqueId === row.uniqueId)
//     );

//     setFilteredData(globalFilter ? combinedData : data);
   
//     setFilteredCount(newFilteredData.length);
//   }, [globalFilter, data,selectedRows]);
//   // if (loading) {
//   //   return <LoadingPage />; // Show loading page while data is being fetched
//   // }
//   const handleShowPopup = () => {
//     const selectedRowIds = Object.keys(selectedRows);
//     if (selectedRowIds.length > 0) {
//       const firstSelectedRow = filteredData.find(row => row.uniqueId === selectedRowIds[0]);
//       setSelectedRowData(firstSelectedRow);
//       setUpdatedData(firstSelectedRow);
//       setOpenModal(true);
//     }
//   };

//   // Handle input change inside the modal
//   const handleInputChange = (e) => {
//     setUpdatedData({ ...updatedData, [e.target.name]: e.target.value });
//   };
//   const handleSubmit = async (e) => {
//     console.log(selectedRows);
//     console.log(data);
//     const selectedServerNames = Object.keys(selectedRows)
//   .map((rowId) => data.find((row) => row.uniqueId === rowId))
//   .filter(Boolean) // Remove undefined values
//   .map((row) => row.ServerName); // Extract serverName

// console.log(selectedServerNames); 
//     const updatedData1 = {
//       ...updatedData, // Assuming this contains all the updated data
//       array:selectedServerNames
//     };
  
//     console.log("Submitting data:", updatedData1);
//     e.preventDefault();
//     try {
//       const response = await fetch('https://umanage.dev.hidglobal.com/api/migration/submit', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify(updatedData1),
//       });
//       console.log(updatedData1);
//       if (response.status==200) {
//         const result = await response.json();
//         alert('Data submitted successfully!');
//         console.log('Server response:', result);
//       } else {
//         console.error('Failed to submit data:', response.status);
//         alert('Failed to submit data. Please try again.');
//       }
//     } catch (error) {
//       console.error('Error during submission:', error);
//       alert('An error occurred while submitting data.');
//     }
//   };
//   // Handle submitting the updated data
//   // const handleSubmit = () => {
//   //   // fetch("/submit", {
//   //   //   method: "POST",
//   //   //   headers: { "Content-Type": "application/json" },
//   //   //   body: JSON.stringify(updatedData),
//   //   // })
//   //   //   .then((response) => response.json())
//   //   //   .then((result) => {
//   //   //     console.log("Updated Data Submitted:", result);
//   //   //     setOpenModal(false); // Close the modal after submission
//   //   //   })
//   //   //   .catch((error) => console.error("Error submitting data:", error));
//   //   console.log(updatedData);
//   // };

//   return (
//     <ThemeProvider theme={defaultMaterialTheme}>
//       {/* <h3>Total Rows: {filteredCount}</h3> */}
//       <MaterialReactTable
//         columns={columns}
//         initialState={{
//           showGlobalFilter: true, // Show global filter by default
//           showColumnFilters: true,
//           density: "compact", 
//           pagination: { pageSize: 10 } // Set default page size
//         }}
//         data={filteredData} // Use filtered data
//         enableGlobalFilter
//         enableFilters
//         enablePagination
//         positionPagination="top" 
//         onGlobalFilterChange={setGlobalFilter}
//         enableRowSelection // ✅ Enable row selection
//         getRowId={(row) =>  row.uniqueId} // ✅ Unique identifier for selection
//         onRowSelectionChange={setSelectedRows} // ✅ Capture selected rows
//         state={{ rowSelection: selectedRows }} 
//         muiSelectAllCheckboxProps={{
//           title: "Select All",
//           sx: { "&::after": { content: '" Select All"', marginLeft: "4px" } },
//         }}
//         renderTopToolbarCustomActions={() => (
//           <Box sx={{ alignItems: "center", gap: 2 }}>
//             <Typography variant="h6">Migration Form</Typography>
//             <Button
//         variant="contained"
//         color="primary"
//         onClick={handleShowPopup}
//         disabled={Object.keys(selectedRows).length === 0} // Disable if no row is selected
//         style={{ marginTop: "10px" }}
//       >
//         Show Selected Instance
//       </Button>
//           </Box>
//         )}
//       />
         
//       <Dialog open={openModal} onClose={() => setOpenModal(false)} fullWidth maxWidth="sm">
//         <DialogTitle>Edit Selected Row</DialogTitle>
//         <DialogContent>
//           {selectedRowData && (
//             <>
//               <Grid2 container spacing={2} alignItems="center">
//   {/* Server Name */}
//   <Grid2 item xs={4}>
//     <Typography variant="subtitle1" fontWeight="bold">Server Name:</Typography>
//   </Grid2>
//   <Grid2 item xs={8}>
//     <TextField
//       name="ServerName"
//       value={updatedData.ServerName || ""}
//       onChange={handleInputChange}
//       fullWidth
//       margin="dense"
//       disabled
//     />
//   </Grid2>

//   {/* IP Address */}
//   <Grid2 item xs={4}>
//     <Typography variant="subtitle1" fontWeight="bold">IP Address:</Typography>
//   </Grid2>
//   <Grid2 item xs={8}>
//     <TextField
//       name="IPAddress"
//       value={updatedData.IPAddress || ""}
//       onChange={handleInputChange}
//       fullWidth
//       margin="dense"
//       disabled
//     />
//   </Grid2>

//   {/* Migration Disposition */}
//   <Grid2 item xs={4}>
//     <Typography variant="subtitle1" fontWeight="bold">Migration Disposition:</Typography>
//   </Grid2>
//   <Grid2 item xs={8}>
//     <TextField
//       select
//       name="MigrationDisposition"
//       value={updatedData.MigrationDisposition || ""}
//       onChange={handleInputChange}
//       fullWidth
//       margin="dense"
//       SelectProps={{ native: true }}
//     >
//       <option value="">Select an option</option>
//       <option value="Planned">Planned</option>
//       <option value="Terminated">Terminated</option>
//       <option value="Moved">Migrated</option>
//     </TextField>
//   </Grid2>

//   {/* Country */}
//   <Grid2 item xs={4}>
//     <Typography variant="subtitle1" fontWeight="bold">Country:</Typography>
//   </Grid2>
//   <Grid2 item xs={8}>
//     <TextField
//       name="Country"
//       value={updatedData.Country || ""}
//       onChange={handleInputChange}
//       fullWidth
//       margin="dense"
//       disabled
//     />
//   </Grid2>
// </Grid2>

//             </>
//           )}
//         </DialogContent>
//         <DialogActions>
//           <Button onClick={() => setOpenModal(false)} color="secondary">Cancel</Button>
//           <Button onClick={handleSubmit} color="primary" variant="contained">Submit</Button>
//         </DialogActions>
//       </Dialog>
//     </ThemeProvider>
//   );
// };

// export default DataTable;
