import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './Stop.css';
import Select from 'react-select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser} from '@fortawesome/free-solid-svg-icons';
import { MdInfo  } from "react-icons/md";
import Ulogo from './assets/Ulogo.png';
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import Loading from './assets/Rocket.gif';
import Navbar from './Navbar';
function Approver() {
  const [data, setData] = useState([]);
 
  const navigate = useNavigate();
  const [selectedCard, setSelectedCard] = useState(null); 
  const [message, setMessage] = useState('');
  const [alertMessage, setAlertMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
   const [user, setUser] = useState(
  {
    email: '<EMAIL>',
    displayName: 'test displayname',
    firstName: 'test firstname'
  });
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
        setUser(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);
  const handleCardClick = (item) => {
    setSelectedCard(item); // Set the clicked card as the selected card
  };

  const handleCloseModal = () => {
    setSelectedCard(null); // Clear the selected card to close the modal
  };

 
  useEffect(() => {
   
      axios.get('https://umanage.dev.hidglobal.com/api/access/approvers')
        .then(response => {
          let fetchedData = response.data;
          console.log(response.data);
          console.log(user);
          const filteredData = fetchedData.filter((item) => {
            
              const approverEmails = item.approvers.split(',').map((email) => email.trim());
              return approverEmails.includes(user.email);
            
          });
          console.log(filteredData);
          setData(filteredData);
          
        })
        .catch(error => {
          // console.error('Error fetching S3 data:', error);
        });
    
  }, [user]);
  // Ef

  
  
  const handleTriggerSSM = (actionType, item) => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    axios.post('https://umanage.dev.hidglobal.com/api/access/approver/trigger-ssm', {
     item:item,
     action:actionType,
     approver:user.firstName,
     approveremail:user.email
    })
    .then(response => {
       
      setMessage(`Request Approved Successfully !!!`);
      setMessagestatus(true);
      })
    .catch(error => {
      setMessage(`Error: ${error}`);
      setMessagestatus(false);
    });
  };
  
  const[messagestatus, setMessagestatus] = useState();
  
  return (
    <div className="Stop-App">
       <Navbar />
  <div className="full-page-content">
  {(message||alertMessage) &&<div  className="notification-container">
        {alertMessage && !message && (
      <div className="alert-card">
        <div className="alert-header">
          <div className="loading-icon">
            <img src={Loading} alt="Loading" className="loading-gif" />
          </div>
          <p className="alert-message">{alertMessage}</p>
          <button className="close-button" onClick={() => setAlertMessage(null)}><IoIosClose /></button>
        </div>
      </div>
    )}

      {/* Status Message Card */}
      {message && (
        <div className={`status-card ${messagestatus ? 'success' : 'error'}`}>
          <div className={`status-icon ${messagestatus ? 'pop-animation' : 'shake-animation'}`}>
            {messagestatus ? <FaCheckCircle size={24} /> : <FaTimesCircle size={24} />}
          </div>
         <p>{message}</p>
          <button className="close-button"onClick={() => {  setMessage(null); setAlertMessage(null);}}><IoIosClose /></button>
          
       
        </div>
      )}
      </div>}

  <p className="main-title label-with-icon">
    Pending Requests  </p>
    

  
  {/* Description below the heading */}
  

  <div className="dropdown-container">
  {data.length > 0 ? (
            data.map((item, index) => (
              <div key={index} className="dropdown-item" onClick={() => handleCardClick(item)}>
                <div className="home-card">
                  <p>
                    <strong>Email:</strong> {item.email}
                  </p>
                  <p>
                    <strong>Type:</strong> {item.type}
                  </p>
                  <div className="button-group">
                  
                </div>
                </div>
                
              </div>
            ))
          ) : (
            <p>No pending requests.</p>
          )}
  </div>
  {selectedCard && (
        <div className="modal-overlay" onClick={handleCloseModal}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>Request Details</h3>
              <button className="close-button" onClick={handleCloseModal}><IoIosClose /></button>
            </div>
            <div className="modal-body">
              <p><strong>Email:</strong> {selectedCard.email}</p>
              <p><strong>Username:</strong> {selectedCard.username}</p>
              <p><strong>Account:</strong> {selectedCard.account}</p>
              <p><strong>Justification:</strong> {selectedCard.justification}</p>
              <p><strong>Type:</strong> {selectedCard.type}</p>
             
            </div>
            <div className="modal-footer">
              <button className="accept-button"  disabled={isProcessing}  onClick={() => handleTriggerSSM("accept", selectedCard)}>Accept</button>
              <button className="reject-button"  disabled={isProcessing} onClick={() => handleTriggerSSM("reject", selectedCard)}>Reject</button>
            </div>
          </div>
        </div>
      )}
  
  
</div>

    </div>
  );
}

export default Approver;
