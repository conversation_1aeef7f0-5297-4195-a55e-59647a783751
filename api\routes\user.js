const express = require('express');
const csvParser = require('csv-parser');
const router = express.Router();
 
module.exports = (s3, Readable) => {
  
  router.get('/', async (req, res) => {
    try {
      const params = {
         Bucket: 'server-provision-application',
            Key: 'Data/user_account_info.csv'
      };
 
      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }
 
        const stream = Readable.from(data.Body);
        const results = [];
        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
                user: row[Object.keys(row)[0]],
                accounts: row[Object.keys(row)[1]],

            });
          })
          .on('end', () => {
            // console.log(results);
            res.json(results);
          })
          .on('error', (err) => {
            res.status(500).send(err.message);
          });
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });
  
  return router;
};