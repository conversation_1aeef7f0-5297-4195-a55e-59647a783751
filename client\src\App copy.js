import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Route, Routes,Navigate } from 'react-router-dom';
import axios from 'axios';
import Home from './Home'; // Assuming you have a Home component
import About from './About'; // Assuming you have an About component
import NotFound from './NotFound'; // Assuming you have a NotFound component
import Stop from './Stop';
import Start from './Start';
import Create from './Create';
import CreateLinux from './CreateLinux';
import CreateWithoutDomainJoin from './CreateWithoutDomainJoin';
import Login from './Login';
import Terminate from './Terminate';
import DomainChecker from './DomainChecker';
import SchedulerComponent from './SchedulerComponent';

function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
 
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.eit.hidglobal.com/api/profile');
        setUser(response.data.user);
        // console.log(user);
        // console.log(response.data.user);
      } catch (error) {
       
        setUser(null);
      }
      setLoading(false);
    }
    checkAuth();
  }, []);


  if (loading) {
    return <div>Loading...</div>;
  }
  return (
    <Router>
      <Routes>
        <Route path="/home" element={user ?<Home />: <Navigate to="/login" />}></Route>
        <Route path="/about" element={user ?<About /> : <Navigate to="/login" />} />
        <Route path="/stop" element={user ?<Stop />: <Navigate to="/login" />} />
        <Route path="/scheduler" element={user ?<SchedulerComponent />: <Navigate to="/login" />} />
        <Route path="/start" element={user ?<Start /> : <Navigate to="/login" />} />
        <Route path="/login" element={<Login />} />
        <Route path="/createwindows" element={user ?<Create />: <Navigate to="/login" />} />
        <Route path="/createlinux" element={user ?<CreateLinux />: <Navigate to="/login" />} />
        <Route path="/createwithoutdomjoin" element={user ?<CreateWithoutDomainJoin />: <Navigate to="/login" />} />
        <Route path="/terminate" element={user ?<Terminate />: <Navigate to="/login" />} />
        <Route path="/DomainChecker" element={user ?<DomainChecker />: <Navigate to="/login" />} />
        <Route path="*" element={<NotFound />} /> {/* For handling 404 - not found */}
      </Routes>
    </Router>
  );
}

export default App;
