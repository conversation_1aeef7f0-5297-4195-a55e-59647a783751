AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for an EC2 instance
 
Parameters:
  InstanceName:
    Type: String
    MaxLength: 15
    Description: "Enter a valid name with the format <P><R><L><D><AZ><E><OS><AC><SN>."
    AllowedPattern: "^[AMO][UFACEIXMK][CEWSONM][1-9][1-4ABCD](SBX|DEV|SUS|SUP|AQ1|AQ2|CIN|UAT|LIV)(MS|LX|UX)(BAS|EBA|EBD|DBS|SQL|UTIL|APP|\\d{4})(0[1-9]|[1-9][0-9])$"
    ConstraintDescription: "Must follow the specified format."
 
  InstanceType:
    Type: String
    Default: t2.micro
    AllowedValues: [
    "r6a.large", "r5a.xlarge", "c5a.large", "t3.large", "t3.medium",
    "r5.xlarge", "t3a.large", "m5a.xlarge", "m6a.large", "m5.2xlarge",
    "m7g.xlarge", "r6a.xlarge", "t3a.xlarge", "t3.xlarge", "t3.2xlarge",
    "c5.large", "t2.micro", "m6a.2xlarge", "g6.2xlarge", "t3a.medium",
    "c7a.2xlarge", "m5.xlarge", "r7a.medium", "c6a.xlarge", "t3.small",
    "m6i.xlarge", "c5.xlarge", "c5.4xlarge", "c5.2xlarge", "m7i-flex.xlarge",
    "m5.large", "c6a.2xlarge", "c6i.large", "m7i-flex.2xlarge", "c6i.2xlarge",
    "c6i.xlarge", "r6i.large", "m7i-flex.large", "inf1.6xlarge", "r6i.xlarge",
    "m6a.xlarge", "r5.large", "m6i.2xlarge", "m7a.medium", "c6a.large",
    "m5a.large", "m6a.4xlarge", "r6a.2xlarge", "t3.micro", "t2.small",
    "r7i.large", "t2.xlarge", "c5a.12xlarge", "c5a.4xlarge", "c5a.2xlarge",
    "m5ad.4xlarge", "r5.4xlarge", "t3a.small", "r5.2xlarge", "r6i.2xlarge",
    "c6a.8xlarge", "c4.4xlarge", "c6g.xlarge", "m1.small", "m6i.large",
    "m6i.4xlarge", "c7i.xlarge", "c7i.large"
]
    Description: EC2 instance type
 
  AMI:
    Type: AWS::EC2::Image::Id
    Default: ami-0f496107db66676ff
    Description: AMI ID
 
  VolumeSize:
    Type: Number
    Description: "The size of the EBS volume in GiB"
    Default: 50
 
  VolumeSize1:
    Type: Number
    Description: "[Optional - Set 1, to not create] The size of the EBS volume in GiB"
    Default: 1
 
  VolumeSize2:
    Type: Number
    Description: "[Optional - Set 1, to not create] The size of the EBS volume in GiB"
    Default: 1
 
  VolumeSize3:
    Type: Number
    Description: "[Optional - Set 1, to not create] The size of the EBS volume in GiB"
    Default: 1
 
  VolumeSize4:
    Type: Number
    Description: "[Optional - Set 1, to not create] The size of the EBS volume in GiB"
    Default: 1
 
  VolumeType:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  VolumeType1:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  VolumeType2:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  VolumeType3:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  VolumeType4:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  DeviceName:
    Type: String
    Default: /dev/sda1
    Description: "Device name for the volume."
 
  DeviceName1:
    Type: String
    Default: /dev/xvdcz
    Description: "Device name for the volume."
 
  DeviceName2:
    Type: String
    Default: /dev/sdc
    Description: "Device name for the volume."
 
  DeviceName3:
    Type: String
    Default: /dev/sdd
    Description: "Device name for the volume."
 
  DeviceName4:
    Type: String
    Default: /dev/sdd
    Description: "Device name for the volume."
 
  SecurityGroupIds:
    Type: List<String>
    Default: sg-0ea3a51fdf626d282
    Description: Security Group IDs
 
  SubnetId:
    Type: AWS::EC2::Subnet::Id
    Default: subnet-05a0c42d955cdbe93
    Description: Subnet ID for the EC2 instance

  CreateIAMRole:
    Type: String
    Default: "no"
    AllowedValues:
      - "yes"
      - "no"
    Description: "Specify 'Yes' to create the IAM Role, 'No' to skip."
 
  IAMRoleName:
    Type: String
    Description: Name of the IAM Role to attach to the EC2 instance
 
  UseBlockDeviceMappings:
    Type: String
    Default: "yes"
    AllowedValues:
      - "yes"
      - "no"
    Description: "Whether to use block device mappings."
 
  CostCenter:
    Type: String
    Default: "6420"
    Description: Cost center code
 
  CostCenterDescription:
    Type: String
    Default: "Infrastructure and NOC"
    Description: Description of the cost center
 
  SupportTier:
    Type: String
    Default: "TIER3"
    Description: Support tier
 
  SupportTierDescription:
    Type: String
    Default: "ON-DEMAND (automatic first backup)"
    Description: Description of the support tier
 
  InstanceSource:
    Type: String
    Default: "INEMB"
    Description: Source of the instance
 
  ProvisioningEntity:
    Type: String
    Default: "HID Engineer"
    Description: Provisioning entity
 
  ProvisioningJustification:
    Type: String
    Default: "RITM0000000"
    Description: Justification for provisioning
 
  BusinessArea:
    Type: String
    Default: "OtherBA"
    Description: Business area
 
  BusinessContact:
    Type: String
    Default: "Paramjeet Singh"
    Description: Business contact person
 
  BusinessContactEmail:
    Type: String
    Default: "<EMAIL>"
    Description: Business contact email
 
  BusinessSegment:
    Type: String
    Default: "9000"
    Description: Business segment
 
  BusinessSegmentDescription:
    Type: String
    Default: "HID Global"
    Description: Description of the business segment
 
  TechnicalContact:
    Type: String
    Default: "Steve Hayter"
    Description: Technical contact person
 
  TechnicalContactEmail:
    Type: String
    Default: "<EMAIL>"
    Description: Technical contact email
 
  Environment:
    Type: String
    Default: "SANDBOX"
    Description: Environment type
 
  NetworkLocation:
    Type: String
    Default: "INTERNAL"
    Description: Network location
 
  FunctionalArea:
    Type: String
    Default: "IT"
    Description: Functional area
 
  ProvisioningEngineer:
    Type: String
    Default: "Prasana Srinivasan"
    Description: Provisioning engineer
 
  BackupPlan:
    Type: String
    Default: "BRONZE"
    Description: Backup plan
 
Conditions:
  CreateRoleCondition: !Equals [ !Ref CreateIAMRole, "yes" ]
  UseBlockDeviceMappingsCondition: !Equals [ !Ref UseBlockDeviceMappings, "yes" ]
  CreateVolume1: !Not [ !Equals [ !Ref VolumeSize1, 1 ] ]
  CreateVolume2: !Not [ !Equals [ !Ref VolumeSize2, 1 ] ]
  CreateVolume3: !Not [ !Equals [ !Ref VolumeSize3, 1 ] ]
  CreateVolume4: !Not [ !Equals [ !Ref VolumeSize4, 1 ] ]
 
Resources:
  MyIAMRole:
      Type: AWS::IAM::Role
      Condition: CreateRoleCondition
      Properties:
        RoleName: !If
          - CreateRoleCondition
          - !Ref IAMRoleName
          - !Sub "Role-${AWS::StackName}" # Default name if not provided
        AssumeRolePolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Effect: "Allow"
              Principal:
                Service: "ec2.amazonaws.com"
              Action: "sts:AssumeRole"

  MyInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Condition: CreateRoleCondition
    Properties:
      Roles:
        - !Ref MyIAMRole

  MyEC2Instance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref InstanceType
      ImageId: !Ref AMI
      SecurityGroupIds: !Ref SecurityGroupIds
      SubnetId: !Ref SubnetId
      PropagateTagsToVolumeOnCreation: true
      IamInstanceProfile: !If
        - CreateRoleCondition
        - !Ref MyInstanceProfile
        - !Ref "AWS::NoValue"
 
      UserData:
        Fn::Base64: !Sub |
          <powershell>
           $UsernameParameter = Get-SSMParameter -Name 'domainuser' -WithDecryption $true;
           $PasswordParameter = Get-SSMParameter -Name 'domainpassword' -WithDecryption $true;
           $Password = ConvertTo-SecureString $PasswordParameter.Parameter.Value -AsPlainText -Force;
           $Credential = New-Object System.Management.Automation.PSCredential ($UsernameParameter.Parameter.Value, $Password);
           $OUPath = 'OU=USAWS,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global';
           Add-Computer -DomainName 'ad.global' -Credential $Credential -OUPath $OUPath -Restart;
          </powershell>
 
      BlockDeviceMappings:
        - DeviceName: !Ref DeviceName
          Ebs:
            VolumeSize: !Ref VolumeSize
            VolumeType: !Ref VolumeType
            Encrypted: true
            DeleteOnTermination: true
 
        - !If
          - CreateVolume1
          - DeviceName: !Ref DeviceName1
            Ebs:
              VolumeSize: !Ref VolumeSize1
              VolumeType: !Ref VolumeType1
              Encrypted: true
              DeleteOnTermination: true
          - !Ref "AWS::NoValue"
 
        - !If
          - CreateVolume2
          - DeviceName: !Ref DeviceName2
            Ebs:
              VolumeSize: !Ref VolumeSize2
              VolumeType: !Ref VolumeType2
              Encrypted: true
              DeleteOnTermination: true
          - !Ref "AWS::NoValue"
 
        - !If
          - CreateVolume3
          - DeviceName: !Ref DeviceName3
            Ebs:
              VolumeSize: !Ref VolumeSize3
              VolumeType: !Ref VolumeType3
              Encrypted: true
              DeleteOnTermination: true
          - !Ref "AWS::NoValue"
 
        - !If
          - CreateVolume4
          - DeviceName: !Ref DeviceName4
            Ebs:
              VolumeSize: !Ref VolumeSize4
              VolumeType: !Ref VolumeType4
              Encrypted: true
              DeleteOnTermination: true
          - !Ref "AWS::NoValue"
 
      Tags:
        - Key: Name
          Value: !Ref InstanceName
        - Key: _CostCenter
          Value: !Ref CostCenter
        - Key: _CostCenterDescription
          Value: !Ref CostCenterDescription
        - Key: _SupportTier
          Value: !Ref SupportTier
        - Key: _SupportTierDescription
          Value: !Ref SupportTierDescription
        - Key: _InstanceSource
          Value: !Ref InstanceSource
        - Key: _ProvisioningEntity
          Value: !Ref ProvisioningEntity
        - Key: _ProvisioningJustification
          Value: !Ref ProvisioningJustification
        - Key: _BusinessArea
          Value: !Ref BusinessArea
        - Key: _BusinessContact
          Value: !Ref BusinessContact
        - Key: _BusinessContactEmail
          Value: !Ref BusinessContactEmail
        - Key: _BusinessSegment
          Value: !Ref BusinessSegment
        - Key: _BusinessSegmentDescription
          Value: !Ref BusinessSegmentDescription
        - Key: _TechnicalContact
          Value: !Ref TechnicalContact
        - Key: _TechnicalContactEmail
          Value: !Ref TechnicalContactEmail
        - Key: _Environment
          Value: !Ref Environment
        - Key: _NetworkLocation
          Value: !Ref NetworkLocation
        - Key: _FunctionalArea
          Value: !Ref FunctionalArea
        - Key: _ProvisioningEngineer
          Value: !Ref ProvisioningEngineer
        - Key: _BackupPlan
          Value: !Ref BackupPlan
 
Outputs:
  InstanceId:
    Description: The instance ID of the newly created EC2 instance
    Value: !Ref MyEC2Instance
 
  PrivateIp:
    Description: The private IP address of the EC2 instance
    Value: !GetAtt MyEC2Instance.PrivateIp
 
  CostCenter:
    Description: Cost center code
    Value: !Ref CostCenter
 
  CostCenterDescription:
    Description: Description of the cost center
    Value: !Ref CostCenterDescription
 
  SupportTier:
    Description: Support tier
    Value: !Ref SupportTier
 
  SupportTierDescription:
    Description: Description of the support tier
    Value: !Ref SupportTierDescription
 
  InstanceSource:
    Description: Source of the instance
    Value: !Ref InstanceSource
 
  ProvisioningEntity:
    Description: Provisioning entity
    Value: !Ref ProvisioningEntity
 
  ProvisioningJustification:
    Description: Justification for provisioning
    Value: !Ref ProvisioningJustification
 
  BusinessArea:
    Description: Business area
    Value: !Ref BusinessArea
 
  BusinessContact:
    Description: Business contact person
    Value: !Ref BusinessContact
 
  BusinessContactEmail:
    Description: Business contact email
    Value: !Ref BusinessContactEmail
 
  BusinessSegment:
    Description: Business segment
    Value: !Ref BusinessSegment
 
  BusinessSegmentDescription:
    Description: Description of the business segment
    Value: !Ref BusinessSegmentDescription
 
  TechnicalContact:
    Description: Technical contact person
    Value: !Ref TechnicalContact
 
  TechnicalContactEmail:
    Description: Technical contact email
    Value: !Ref TechnicalContactEmail
 
  Environment:
    Description: Environment type
    Value: !Ref Environment
 
  NetworkLocation:
    Description: Network location
    Value: !Ref NetworkLocation
 
  FunctionalArea:
    Description: Functional area
    Value: !Ref FunctionalArea
 
  ProvisioningEngineer:
    Description: Provisioning engineer
    Value: !Ref ProvisioningEngineer
 
  BackupPlan:
    Description: Backup plan
    Value: !Ref BackupPlan
 