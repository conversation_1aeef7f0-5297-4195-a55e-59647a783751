@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');

.Stop-App {
  font-family: "Lato", sans-serif;
  background-color: #ffffff;
  color: #333;
  min-height: 100vh;
 
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
 
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
/* 
.modal-content {
 background-color: #062445 !important;
  padding: 20px;
 
  border-radius: 8px;
  width: 50%;
  max-width: 600px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
} */

.modal-content {
  background-color: #062445 !important;
  padding: 20px;
  border-radius: 12px!important; /* Rounded edges */
  width: 50%;
  max-width: 600px;
  max-height: 80vh!important; /* Restrict height to make space for scrolling */
  overflow-y: auto !important; /* Add vertical scrolling if content overflows */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  color: white!important; /* Text color set to white */
}
.modal-content::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-thumb {
  background-color: #ffffff; /* Scrollbar thumb color */
  border-radius: 12px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background-color: #cccccc; /* Thumb color on hover */
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.modal-body {
  margin-bottom: 15px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.modal-footer .accept-button,
.modal-footer .reject-button {
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.modal-footer .accept-button {
  background-color: #a4e1a6;
  color: white;
}

.modal-footer .reject-button {
  background-color: #f9b4af;
  color: white;
}

/* Navbar styling remains unchanged */
.stop-navbar {
  background-color: #ffffff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 10px 20px;
}

.home-card {
  background-color: #17192b;
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 25px;
  text-align: center;
  width: 300px;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.home-card::before {
  content: '';
  position: absolute;
  top: 0px;
  left: -50px;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, #09539d, #8fbeec, #a4a7b5, #95b4df);
  transition: 0.5s;
  z-index: -1;
  transform: rotate(0deg);
  border-radius: 50%;
  animation: home-rotateStroke 5s linear infinite;
}
.stop-navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stop-navbar-logo {
  height: 50px;
}

.stop-navbar-links {
  display: flex;
  gap: 15px;
}

.stop-navbar-link {
  color: #02569b;
  text-decoration: none;
  font-weight: 900;
  transition: color 0.3s;
}

.stop-navbar-link:hover {
  color: #fff;
  background-color: #02569b;
}

.stop-logout-btn {
  background-color: #ffffff;
  color: #02569b;
  border: none;
  font-size: medium;
  font-weight: 800;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.stop-logout-btn:hover {
  background-color: #00549B;
  color: #fff;
}

.full-page-content {
  padding: 20px;
  max-width: 100%;
  margin: 0 auto;
}

.info-icon-container {
 /* Spacing between the text and info icon */
  cursor: pointer;

}


/* Container for dropdowns */
.dropdown-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.dropdown-section {
  flex: 1;
}

.dropdown-heading {
  font-size: 18px;
  font-weight: 700;
  color: #02569b;
  margin-bottom: 5px;
}

.dropdown-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f8f9fa;
  font-size: 14px;
}

/* Instance details styling */
.instance-details {
  margin-top: 20px;
  padding: 15px;
  background-color: #f1f3f6;
  border-left: 4px solid #007bff;
  border-radius: 5px;
}

.instance-details p {
  margin: 5px 0;
  color: #555;
}

/* Trigger SSM button */
.TriggerSSM {
  display: block;
  width: calc(100% - 20px);
  padding: 10px;
  margin: 20px 10px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.TriggerSSM:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.TriggerSSM:hover:enabled {
  background-color: #0056b3;
}

/* Checkbox for acknowledgment */
.checkbox-container {
  display: flex;
  align-items: center;
  margin: 15px 10px;
}

.checkbox-label {
  margin-left: 10px;
  font-size: 14px;
  color: #333;
}

/* Success and error messages */
.alert-message {
  margin-top: 15px;
  background-color: #ffcc00;
  color: #333;
  padding: 10px;
  border-radius: 5px;
  font-weight: bold;
  text-align: center;
}

.message {
  margin-top: 10px;
  padding: 10px;
  background-color: #007bff;
  color: white;
  border-radius: 5px;
  text-align: center;
}



.info-icon {
  cursor: pointer;
}

.tooltip-card {
  position: absolute;
  top: 25px;
  right: 0;
  background-color: white;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 5px;
  width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.tooltip-overlay {
  position: relative;
  display: inline-block;
}

.main-description {
  margin-bottom: 20px;
  font-size: 1rem;
  color: #666;
}
.main-title {
  font-size: 45px; /* Extra large size */
  font-weight: 800; /* Maximum boldness */
  color: #00549B; /* Ensure it matches the color scheme */
  margin-bottom: 10px; /* Add space below */
  line-height: 1.1; /* Tighten line height */
  text-align: left; /* Force align to the left */
  text-transform: uppercase;/* Force all uppercase letters */
  letter-spacing: 0.8px; /* Slight spacing between letters */
}
.big-input-simulated {
  height: auto;
  min-height: 100px; /* Enough for four lines */
  max-height: 150px; /* Limit height */
  overflow-y: auto;
  padding: 10px;
  font-size: 16px;
  line-height: 1.5;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  background-color: #fff;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
  width: 100%;
  word-wrap: break-word;
  white-space: pre-wrap; /* Allow text wrapping */
}

.big-input-simulated:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}