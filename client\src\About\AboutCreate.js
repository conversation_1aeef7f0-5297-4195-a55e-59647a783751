import React from 'react';
import step1Image from '../assets/about/Account.png'; // Ensure correct image paths
import step2Image from '../assets/about/Configure.png';
import step3Image from '../assets/about/Storage.png';
import step4Image from '../assets/about/Networking.png';
import './AboutCreate.css';
const Create = () => {
  return (
    <div className="provision-instances">
      <h1 className="header-title">Provision Instances Overview</h1>
      <p className="overview-text">
        The process for provisioning Windows, Linux, and instances without domain join in AWS follows these standardized six steps:
      </p>

      {/* Step 1 */}
      <div className="step-section">
        <h2 className="step-title">Step 1: Select the Account</h2>
        <p className="step-description">Choose the appropriate AWS account where the instance needs to be provisioned.</p>
        <img src={step1Image} alt="Step 1" className="step-image" />
      </div>

      {/* Step 2 */}
      <div className="step-section">
        <h2 className="step-title">Step 2: Configuration</h2>
        <div className="step-details">
          <h3 className="step-subtitle">Step 2a: Instance Name</h3>
          <p className="step-description">Ensure the instance name is exactly 15 characters long following UManage naming conventions.</p>
          
          <h3 className="step-subtitle">Step 2b: Select the Region</h3>
          <p className="step-description">Choose the designated region as per the account's requirements.</p>
          
          <h3 className="step-subtitle">Step 2c: Select the AMI</h3>
          <p className="step-description">Pick the operating system version for your instance.</p>
          
          <h3 className="step-subtitle">Step 2d: Select the Instance Type</h3>
          <p className="step-description">Choose the instance type. High-end versions are available with prior approval despite restrictions.</p>
        </div>
        <img src={step2Image} alt="Step 2" className="step-image" />
      </div>

      {/* Step 3 */}
      <div className="step-section">
        <h2 className="step-title">Step 3: Storage Configuration</h2>
        <p className="step-description">
          By default, each instance comes with a 50 GB root volume. Up to four additional volumes can be added with user-defined specifications.
        </p>
        <h3 className="step-subtitle">Step 3a: EBS Volume Size</h3>
        <p className="step-description">Specify the volume size in GB; the default is 50 GB.</p>

        <h3 className="step-subtitle">Step 3b: EBS Volume Type</h3>
        <p className="step-description">Choose the volume type; the recommended type is gp3 for balanced performance and cost-effectiveness.</p>

        <h3 className="step-subtitle">Step 3c: Device Name</h3>
        <p className="step-description">The device name is pre-configured but can be overwritten if needed to meet specific requirements.</p>
        <img src={step3Image} alt="Step 3" className="step-image" />
      </div>

      {/* Step 4 */}
      <div className="step-section">
        <h2 className="step-title">Step 4: Network and Security Configuration</h2>
        <h3 className="step-subtitle">Step 4a: Select the Appropriate VPC</h3>
        <p className="step-description">We have six VPC types; choose one based on the following descriptions. You can identify the type of VPC by its name.</p>
        <ul className="vpc-list">
          <li><strong>Segmented VPC</strong> – Firewall blocks access except for IS approved SDP exceptions.</li>
          <li><strong>Isolated (with Restricted WAN) VPC</strong> – Internet access via NLB or ALB in the public subnet.</li>
          <li><strong>Proxied Internet-Only VPC</strong> – Internet access only through a proxy server.</li>
          <li><strong>No Internet VPC</strong> – Complete internet blockage.</li>
          <li><strong>Isolated (with Internet) VPC</strong> – Only allows internet egress traffic.</li>
          <li><strong>Completely Isolated VPC</strong> – Blocks all traffic.</li>
        </ul>

        <h3 className="step-subtitle">Step 4b: Select the Subnet</h3>
        <p className="step-description">Choose the subnet appropriate for your application, considering availability zones.</p>

        <h3 className="step-subtitle">Step 4c: Select Security Groups</h3>
        <p className="step-description">Use the management and application security groups first; otherwise, select the default security group.</p>
        <img src={step4Image} alt="Step 4" className="step-image" />
      </div>

      {/* Step 5: Tags Configurations */}
      <div className="step-section">
        <h2 className="step-title">Step 5: Tags Configurations</h2>
        <p className="step-description">
          These tags help in tracking, billing, and organizing resources.
        </p>
        
        <h3 className="step-subtitle">Business Area</h3>
        <p className="step-description"><strong>Purpose:</strong> Specifies the organizational area responsible for the instance.<br/><strong>Example:</strong> OtherBA</p>

        <h3 className="step-subtitle">Business Segment</h3>
        <p className="step-description"><strong>Purpose:</strong> Defines the financial or business segment associated with the resource.<br/><strong>Example:</strong> 9000</p>

        <h3 className="step-subtitle">Business Segment Description</h3>
        <p className="step-description"><strong>Purpose:</strong> Provides additional context about the business segment.<br/><strong>Example:</strong> HID Global</p>

        <h3 className="step-subtitle">Cost Center</h3>
        <p className="step-description"><strong>Purpose:</strong> Allocates instance expenses to a specific cost center.<br/><strong>Example:</strong> 6420</p>

        <h3 className="step-subtitle">Cost Center Description</h3>
        <p className="step-description"><strong>Purpose:</strong> Describes the cost center for easier identification.<br/><strong>Example:</strong> Global Infrastructure and NOC</p>

        <h3 className="step-subtitle">Functional Area</h3>
        <p className="step-description"><strong>Purpose:</strong> Specifies the functional department managing the resource.<br/><strong>Example:</strong> IT</p>

        <h3 className="step-subtitle">Environment</h3>
        <p className="step-description"><strong>Purpose:</strong> Identifies the AWS account where the instance will be provisioned.<br/><strong>Example:</strong> EIT Sandbox</p>

        <h3 className="step-subtitle">Backup Plan</h3>
        <p className="step-description"><strong>Purpose:</strong> Designates the backup strategy applied to the instance.<br/><strong>Example:</strong> Bronze</p>

        <h3 className="step-subtitle">Network Location</h3>
        <p className="step-description"><strong>Purpose:</strong> Defines network access restrictions or location (internal/external).<br/><strong>Example:</strong> Internal</p>

        <h3 className="step-subtitle">Support Tier</h3>
        <p className="step-description"><strong>Purpose:</strong> Indicates the support level required for the instance.<br/><strong>Example:</strong> Tier 3</p>

        <h3 className="step-subtitle">Support Tier Description (Optional)</h3>
        <p className="step-description"><strong>Purpose:</strong> Provides details about the support level, including SLA.<br/><strong>Example:</strong> SLA: P3</p>

        <h3 className="step-subtitle">Map-Migrated (Optional)</h3>
        <p className="step-description"><strong>Purpose:</strong> Marks migrated instances with an identifier for tracking.<br/><strong>Example:</strong> Mig47460</p>

        <h3 className="step-subtitle">Business Contact</h3>
        <p className="step-description"><strong>Purpose:</strong> Lists the business owner or contact person for the instance.<br/><strong>Example:</strong> Firstname Lastname</p>

        <h3 className="step-subtitle">Business Contact Mail</h3>
        <p className="step-description"><strong>Purpose:</strong> Provides the email address of the business contact.<br/><strong>Example:</strong> <EMAIL></p>

        <h3 className="step-subtitle">Technical Contact</h3>
        <p className="step-description"><strong>Purpose:</strong> Identifies the technical owner or support person for the instance.<br/><strong>Example:</strong> Firstname Lastname</p>

        <h3 className="step-subtitle">Technical Contact Mail</h3>
        <p className="step-description"><strong>Purpose:</strong> Lists the email of the technical contact.<br/><strong>Example:</strong> <EMAIL></p>

        <h3 className="step-subtitle">Provision Justification</h3>
        <p className="step-description"><strong>Purpose:</strong> Justifies the instance creation, often linked to a ServiceNow ticket.<br/><strong>Example:</strong> ServiceNow Ticket ID: INC123456</p>
      </div>
    </div>
  );
};

export default Create;
