const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');

// Export the function to set up the router with necessary utilities
module.exports = (generateTicketNumber, storeDataInS3) => {
  let originalCredentials = AWS.config.credentials;

  // Function to assume an IAM role and return temporary credentials
  const assumeRole = async (accountId,firstname) => {
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::${accountId}:role/EC2CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    console.log(firstname);
    console.log(params);
    try {
      const data = await sts.assumeRole(params).promise();
      return data.Credentials;
    } catch (error) {
      console.error('Error assuming role:', error);
      throw error;
    }
  };

  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };

  // Function to configure AWS services (SSM, S3, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      cloudFormation: new AWS.CloudFormation()
    };
  };

  // Function to get the status of an SSM Automation execution
  const getAutomationExecutionStatus = async (ssm, executionId) => {
    try {
      const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      console.log('Automation execution data:', data); // Log entire response
      const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      return status;
    } catch (error) {
      console.error('Error fetching execution status:', error);
      throw error;
    }
  };

  // Function to fetch detailed results of an SSM Automation execution
  const getAutomationExecutionDetails = async (ssm, executionId) => {
    try {
      const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      return data.AutomationExecution;
    } catch (error) {
      console.error('Error fetching execution details:', error);
      throw error;
    }
  };

  // Function to poll the status of an SSM Automation execution
  const pollAutomationExecutionStatus = async (ssm, executionId, interval = 5000) => {
    return new Promise((resolve, reject) => {
      const intervalId = setInterval(async () => {
        try {
          const status = await getAutomationExecutionStatus(ssm, executionId);
          console.log('Current status:', status);

          if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
            clearInterval(intervalId);
            resolve(status);
          }
        } catch (error) {
          clearInterval(intervalId);
          reject(error);
        }
      }, interval);
    });
  };

  // Function to handle SSM requests (start/stop EC2 instances)
  const handleSSMRequest = async (req, res, documentName) => {
    const { instanceId, region, businesscontact, email, accountId,accountname, instancename } = req.body;

    let firstname=req.body.firstname;
    let x=firstname.replace(/\s+/g,'');
    let y=x.substring(0,8);
console.log(firstname);
    if (!instanceId || !region || !accountId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    const params = {
      DocumentName: documentName,
      Parameters: {
        InstanceId: [instanceId],
        // Add other parameters required by your SSM document here
      }
    };

    try {
      // AWS.config.update({ credentials: originalCredentials });
      const credentials = await assumeRole(accountId,y);
      await initializeAWS(credentials, region);
      const { ssm } = configureAWS();

      const ticketNumber = generateTicketNumber();
      let executionID;

      const data = await ssm.startAutomationExecution(params).promise();
      executionID = data.AutomationExecutionId;
      AWS.config.update({ credentials: originalCredentials });
      // Poll status and fetch detailed result
      const status = await pollAutomationExecutionStatus(ssm, executionID);
      const executionDetails = await getAutomationExecutionDetails(ssm, executionID);

      console.log('Execution details:', executionDetails);

      await storeDataInS3(ticketNumber, executionID, executionDetails. DocumentName, executionDetails. ExecutionStartTime, executionDetails.  ExecutionEndTime, executionDetails. AutomationExecutionStatus, executionDetails. Parameters. InstanceId,businesscontact,email,accountname, instancename);
      AWS.config.update({ credentials: originalCredentials });

      res.status(200).json({
        message: 'SSM Automation Document triggered successfully!',
        ticketNumber,
        executionId: executionID,
        status,
        executionDetails // Include details in the response
      });
    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
      res.status(500).json({ error: err.message });
    }
  };

  // Route to stop an EC2 instance
  router.post('/stop', (req, res) => handleSSMRequest(req, res, 'AWS-StopEC2Instance'));

  // Route to start an EC2 instance
  router.post('/start', (req, res) => handleSSMRequest(req, res, 'AWS-StartEC2Instance'));

  return router;
};
