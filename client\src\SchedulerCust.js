import React, { useState } from 'react';
import Navbar from './Navbar'; // Ensure this path is correct
import CustSched from './CustSched'; // Import CustSched.js
import CustSchedLogs from './CustSchedLogs'; // Import CustSchedLogs.js

const Scheduler = () => {
  const [view, setView] = useState('allSchedules'); // State to toggle between views

  return (
    <div>
      <Navbar />
      <div className="scheduler-container">
        <aside className="sidebar">
          <h2>Scheduler</h2>
          <button onClick={() => setView('allSchedules')}>All Schedules</button>
          <button onClick={() => setView('mySchedules')}>My Schedules</button>
        </aside>

        <main className="main-content">
          {view === 'allSchedules' && (
            <CustSched /> // Render CustSched.js for "All Schedules"
          )}

          {view === 'mySchedules' && (
            <CustSchedLogs /> // Render CustSchedLogs.js for "My Schedules"
          )}
        </main>
      </div>
    </div>
  );
};

export default Scheduler;