{"name": "api", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-cloudformation": "^3.650.0", "@aws-sdk/client-ec2": "^3.741.0", "@aws-sdk/client-s3": "^3.637.0", "@aws-sdk/client-ssm": "^3.637.0", "@aws-sdk/client-sts": "^3.650.0", "@aws-sdk/util-waiter": "^3.370.0", "@mui/icons-material": "^7.1.2", "@react-three/drei": "^9.120.4", "@react-three/fiber": "^8.17.10", "aws-sdk": "^2.1686.0", "axios": "^1.8.4", "body-parser": "^1.20.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "cronstrue": "^2.61.0", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.4.5", "errorhandler": "^1.5.1", "exceljs": "^4.4.0", "express": "^4.19.2", "express-session": "^1.18.0", "fs": "^0.0.1-security", "https": "^1.0.0", "imap-simple": "^5.1.0", "json2csv": "^6.0.0-alpha.2", "mailparser": "^3.7.3", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "nodemailer": "^6.9.16", "passport": "^0.5.3", "passport-saml": "^3.2.4", "path": "^0.12.7", "three": "^0.171.0", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}}