##
##  Authentication
##

# User Input for Authentication
$username = "<EMAIL>"
$password = "x5EMe7a4W3Qfb6FDo82N" #TODO: Set your password

# Logging setup
$logFile = "C:\Logs\API_Response.log"
if (!(Test-Path $logFile)) { New-Item -ItemType File -Path $logFile -Force }

Function Write-Log {
    param([string]$message)
    $timeStamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    "$timeStamp - $message" | Out-File -FilePath $logFile -Append
}

# API Base URL
$baseUrl = "https://adx-api.assaabloy.net/restApi"

# Session request
$requestUrl = "$baseUrl/api/authSessions/create"
$requestBody = ConvertTo-Json @{"username" = $username; "password" = $password} -Compress

try {
    $session = Invoke-RestMethod -Method POST -Uri $requestUrl -Body $requestBody -ContentType "application/json"
    Write-Host "✅ Session created successfully." -ForegroundColor Green
    Write-Log "Session created successfully."
} catch {
    Write-Host "❌ Error creating session: $_" -ForegroundColor Red
    Write-Log "Error creating session: $_"
    exit
}

# Get session ID
$sessionId = $session.sessionId
Write-Host "Session ID: $sessionId"
Write-Log "Session ID: $sessionId"

# Request authentication token
$requestUrl = "$baseUrl/api/auth"
$requestBody = ConvertTo-Json @{"sessionId" = $sessionId; "type" = 0} -Compress

try {
    $authTicketInfo = Invoke-RestMethod -Method POST -Uri $requestUrl -Body $requestBody -ContentType "application/json"
    Write-Host "✅ Authentication token obtained." -ForegroundColor Green
    Write-Log "Authentication token obtained."
} catch {
    Write-Host "❌ Error obtaining authentication token: $_" -ForegroundColor Red
    Write-Log "Error obtaining authentication token: $_"
    exit
}

$token = $authTicketInfo.token
Write-Host "Token: $token"
Write-Log "Token: $token"

##
## Execute custom command - HID - Create Server (Cloud standard)
##

# Parameters
$provider = "A"
$region = "F" 
$locale = "C"
$siteDesignator = "1"
$zone = "1"
$environment = "SBX"
$os = "MS"
$application = "TES02"
$patchGroup = "DEVTEST"
$description = "This is a test"
$ouDN = "OU=CNSUH,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global"
$ownerDN = "CN=Srinivasan\, Prasana,OU=INCHE,OU=UsersInternal,OU=Users,OU=HID,OU=SSC,DC=ad,DC=global"

# API request setup
$endpoint = "/api/directoryObjects/executeCustomCommand"
$requestUrl = "$baseUrl$endpoint"
$requestHeaders = @{"Adm-Authorization" = $token}
$requestBody = ConvertTo-Json -Depth 4 @{
    "directoryObject" = "DC=ad,DC=global";
    "customCommandId" = "c39819b3-4a52-4476-9950-3e2e6c8bfdd5";
    "parameters" = @(
        @{ "type" = "List"; "name" = "param-Provider"; "value" = $provider },
        @{ "type" = "List"; "name" = "param-Region"; "value" = $region },
        @{ "type" = "List"; "name" = "param-Locale"; "value" = $locale },
        @{ "type" = "List"; "name" = "param-SiteDesignator"; "value" = $siteDesignator },
        @{ "type" = "List"; "name" = "param-Zone"; "value" = $zone },
        @{ "type" = "List"; "name" = "param-Environment"; "value" = $environment },
        @{ "type" = "List"; "name" = "param-OS"; "value" = $os },
        @{ "type" = "List"; "name" = "param-Application"; "value" = $application },
        @{ "type" = "List"; "name" = "param-PatchingGroup"; "value" = $patchGroup },
        @{ "type" = "Text"; "name" = "param-Description"; "value" = $description },
        @{ "type" = "ADObject"; "name" = "param-SiteOU"; "value" = @( @{ "referenceType" = 0; "key" = $ouDN }) },
        @{ "type" = "ADObject"; "name" = "param-ManagedBy"; "value" = @( @{ "referenceType" = 0; "key" = $ownerDN }) }
    )
}

# Make API request
try {
    $result = Invoke-RestMethod -Method POST -Headers $requestHeaders -Uri $requestUrl -Body $requestBody -ContentType "application/json"
    Write-Host "✅ Request executed successfully." -ForegroundColor Green
    Write-Log "Request executed successfully."
} catch {
    Write-Host "❌ Error executing request: $_" -ForegroundColor Red
    Write-Log "Error executing request: $_"
    exit
}

# Check if result exists
if ($result) {
    Write-Host "✅ Response received successfully!" -ForegroundColor Green
    Write-Log "Response received successfully."
    
    # Save response to log file
    $result | Out-File -FilePath $logFile -Append

    # Show result
    Write-Host "Full Response Object:" -ForegroundColor Cyan
    $result | Format-List *

    # Debug: Check object structure
    Write-Host "Object Properties:" -ForegroundColor Blue
    $result | Get-Member

    # Check for specific property
    if ($result.PSObject.Properties.Name -contains "status") {
        Write-Host "Status: $($result.status)" -ForegroundColor Yellow
        Write-Log "Status: $($result.status)"
    } else {
        Write-Host "⚠️ Warning: Response does not contain 'status'." -ForegroundColor Magenta
        Write-Log "Warning: Response does not contain 'status'."
    }
} else {
    Write-Host "❌ No output received. Something went wrong!" -ForegroundColor Red
    Write-Log "No output received. Possible API failure."
}
