/* About Page Layout */
.about-page {
  display: flex;
  min-height: 100vh;
  font-family: 'Lato', sans-serif;
  background-color: #f3f4f6; /* Light background color */
}

/* Sidebar Styling */
.about-sidebar {
  position: fixed; /* Fix the sidebar position */
  top: 0; /* Align to the top of the viewport */
  left: 0; /* Align to the left */
  width: 250px; /* Width of the sidebar */
  height: 100vh; /* Full height of the viewport */
  padding: 20px;
  background-color: #1e3a8a; /* Deep blue background */
  color: #ffffff;
  border-right: 1px solid #ffffff;
  overflow-y: auto; /* Enable scrolling for sidebar content if necessary */
}

.about-sidebar h3 {
  font-size: 1.5rem;
  color: #ffffff;
  margin-bottom: 15px;
  border-bottom: 1px solid #3b82f6; /* Light blue line for emphasis */
  padding-bottom: 5px;
}

.about-sidebar ul {
  list-style-type: none;
  padding: 0;
}

.about-sidebar li {
  cursor: pointer;
  padding: 10px 15px;
  color: #d1d5db; /* Light gray */
  border-radius: 4px;
  transition: background-color 0.3s, color 0.3s;
}

.about-sidebar li:hover,
.about-sidebar li.active {
  background-color: #3b82f6; /* Light blue on hover/active */
  color: #ffffff;
}

.about-sidebar li div {
  display: flex;
  align-items: center;
}

.about-sidebar li span {
  margin-right: 8px;
}

/* Content Area */
.about-content {
  flex: 1;
  margin-left: 250px;
  background-color: #ffffff;
  border-radius: 8px;
  
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.about-content h2 {
  font-size: 1.8rem;
  color: #1e3a8a; /* Deep blue */
  margin-bottom: 15px;
}

.about-content p {
  color: #4b5563; /* Dark gray */
  line-height: 1.6;
  font-size: large;
}

.about-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 20px 0;
}

.about-content th,
.about-content td {
  padding: 10px;
  border-bottom: 1px solid #e5e7eb; /* Light gray */
}

.about-content th {
  background-color: #e0f2fe; /* Light blue */
  color: #1e3a8a;
  font-weight: bold;
}

.about-content td {
  color: #374151; /* Gray */
}

.faq-item {
  margin-bottom: 20px;
}

.faq-item h4 {
  font-size: 1.2rem;
  color: #1e3a8a;
  margin-bottom: 8px;
}

.faq-item p {
  color: #6b7280; /* Lighter gray */
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .about-page {
    flex-direction: column;
  }

  .about-sidebar {
    width: 100%;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .about-content {
    margin: 10px;
    padding: 20px;
  }
}
.version {
  position: absolute;
  bottom: 1px; /* Distance from the bottom */
  right: 10px; /* Distance from the right */
  font-size: 15px; /* Adjust font size as needed */
  color: #d4d5d7; /* Subtle gray color */
}