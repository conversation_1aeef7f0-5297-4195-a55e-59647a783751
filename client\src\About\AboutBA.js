import React, { useState, useEffect } from 'react';
import <PERSON> from 'papaparse';
import csvData from '../assets/csv/Business.csv'; // Adjust the path to your CSV file
import './AboutBA.css';
const BusinessCenters = () => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [businessAreaFilter, setBusinessAreaFilter] = useState('');

  useEffect(() => {
    // Load and parse CSV data
    Papa.parse(csvData, {
      header: true,
      download: true,
      complete: (result) => {
        setData(result.data);
        setFilteredData(result.data);
      },
    });
  }, []);

  // Filter table data based on selected Business Area
  const handleFilterChange = (event) => {
    const selectedArea = event.target.value;
    setBusinessAreaFilter(selectedArea);
    if (selectedArea) {
      setFilteredData(data.filter((row) => row['Business Area'] === selectedArea));
    } else {
      setFilteredData(data);
    }
  };

  // Extract unique Business Areas for dropdown options
  const businessAreaOptions = [...new Set(data.map((row) => row['Business Area']))];

  return (
    <div className="business-centers">
      <h2 className="business-centers-title">Business Segment</h2>
      
      <div className="business-centers-filter">
        <label className="filter-label">Filter by Business Area:</label>
        <select
          className="filter-dropdown"
          value={businessAreaFilter}
          onChange={handleFilterChange}
        >
          <option value="">All</option>
          {businessAreaOptions.map((area, index) => (
            <option key={index} value={area}>{area}</option>
          ))}
        </select>
      </div>

      <table className="business-centers-table">
        <thead>
          <tr>
            <th className="table-header">Business Segment</th>
            <th className="table-header">Description</th>
            <th className="table-header">Business Area</th>
          </tr>
        </thead>
        <tbody>
          {filteredData.map((row, index) => (
            <tr key={index}>
              <td className="table-data">{row['Business Segment']}</td>
              <td className="table-data">{row['Description']}</td>
              <td className="table-data">{row['Business Area']}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default BusinessCenters;
