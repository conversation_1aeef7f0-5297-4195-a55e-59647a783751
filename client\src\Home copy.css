/* General page styling */
.home-page {
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  font-family: "Lato", sans-serif;
  background-color: rgb(0, 0, 0); /* White background for the entire page */
}

/* Navbar styling */
.home-navbar {
  background-color: #000000;
  height: 70px;
  display: flex;
  font-family: "Lato", sans-serif;
  align-items: center;
  margin-top: 10px;
  margin-bottom: -10px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.home-navbar-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20px;
  width: 100%;
}

/* Centered links in the navbar */
.home-navbar-links-center {
  display: flex;
  justify-content: center;
  gap: 2%;
  flex-grow: 1;
}

.home-navbar-logo {
  height: 60px;
  position: absolute;
  left: 20px;
}

.home-navbar-link {
  position: relative;
  color: #bfd8ed;
  text-decoration: none;
  font-weight: 800;
  font-size: 16px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s, transform 0.3s;
  cursor: pointer;
}

/* Updated Dropdown Menu */
.home-dropdown-menu {
  position: absolute;
  top: 100%;
  left: -20%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0);
  
  border-radius: 4px;
  padding: 15px;
  display: none;
  flex-direction: column;
  z-index: 1000;
  min-width: 220px; /* Wider dropdown */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.34);
  transition: all 0.3s ease-in-out;
}

.home-navbar-link:hover .home-dropdown-menu {
  display: flex;
}

.home-dropdown-menu a {
  padding: 10px 15px;
  text-decoration: none;
  color: #bfd8ed;
  font-weight: bold;
  transition: all 0.3s ease;
}

.home-dropdown-menu a:hover {
  background-color: #02569b;
  color: white;
  border-radius: 4px;
}

/* User dropdown styling */
.home-navbar-user {
  position: relative;
  cursor: pointer;
  margin-left: auto;
  padding: 0 20px;
}

.home-user-icon {
  color: #02569b;
  font-size: 24px;
  cursor: pointer;
}.home-user-name{
  color: #ffe7e7;
  font-size:17px;
  font-weight: bolder;
}
.home-user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: #5d4f4f8b;
  color: #ffffff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  display: none;
  z-index: 1000;
  min-width: 180px; /* Set minimum width for user dropdown */
  transition: all 0.3s ease-in-out;
}

.home-navbar-user:hover .home-user-menu {
  display: block;
}

.home-user-menu p {
  margin: 0;
  font-size: 16px;
  color: #f3f4f9;
}

.home-logout-btn {
  background-color: #153570;
  border: none;
  color: white;
  cursor: pointer;
  padding: 10px 15px;
  border-radius: 3px;
  margin-top: 10px;
  width: 100%;
  text-align: center;
  transition: background-color 0.3s ease;
}

.home-logout-btn:hover {
  background-color: #cc3c3c;
}

/* Add Hover Effects */
.home-navbar-link:hover,
.home-user-icon:hover {
  color: #02569b;
  transform: scale(1.05);
}

/* Transitions for smoother dropdown */
.home-dropdown-menu,
.home-user-menu {
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.home-navbar-link:hover .home-dropdown-menu,
.home-navbar-user:hover .home-user-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Top Section */
#bg-video {
  position: absolute;
  top: 0;
  left: 150px;
  width: 100%;
  height: 100%;
  /* Ensures the video covers the background */
  z-index: -1; /* Send the video behind the content */
   /* Slight transparency for a better text overlay */
}

/* Top Section */
.home-top-section {
  position: relative;
  display: flex;
  justify-content: space-between;
  padding: 40px;
  margin: 20px;
  border-radius: 80px;
  background-color: #140403;
  overflow: hidden;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 130px); 
  height: auto;
  z-index: 1; /* Ensures the section is on top of the video */
}

/* Left Section (Text on top of the video) */
.home-left-section {
  position: relative;
  z-index: 2; /* Ensure content appears above video */
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: #f0f8ff;
  text-align: left;
  width: 50%;
  padding: 20px;
}

.home-left-section h1 {
  font-size: 64px; /* Big H1 */
  font-weight: bold;
  margin-bottom: 20px;
  color: #c5e5fb; /* Light blue for prominent header */
}

.home-left-section p {
  font-size: 18px;
  line-height: 1.6;
  max-width: 80%; /* Optional to avoid spreading too wide */
  color: #b0c4de; /* Soft light color for description */
  margin-bottom: 40px; /* Spacing between description and button */
}

.home-goto-btn {
  background-color: #04223fec; /* Blue button */
  color: #cbdafcf7;
  border: none;
  
  border-radius: 50px;
  padding: 10px;
  font-size: 28px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.home-goto-btn:hover {
  background-color: #0d47a1; /* Darker blue on hover */
}

/* Statistics section below the button */
.home-stats {
  display: flex;
  gap: 40px;
  margin-top: 50px; /* Spacing between button and stats */
}

.home-stat h2 {
  font-size: 72px; /* Large numbers */
  color: #fff; /* Bright white for numbers */
  margin: 0;
}

.home-stat p {
  font-size: 24px; /* Label below numbers */
  color: #b0c4de; /* Lighter color for text */
}


.home-right-section {
  flex: 1;
  text-align: right;
  color: white;
}

.home-secondary-logo {
  height: 80px;
}

@keyframes home-gradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Service Section (Radio + Cards) */
.home-services-section {
  display: flex;
  flex-direction: column;
  
  align-items: center;
  height: 100vh; /* Full viewport height */
  background-color: #000000; /* Neutral background for bottom section */
}
#services-section {
  padding: 50px 0;
}
.home-toggle-container {
  position: relative;
  display: flex;
  height: 50px;
  width: 100%;
  margin: 30px 0;
  border-radius: 15px;
  overflow: hidden;
  background-color: transparent; 
}

.home-toggle-label {
  flex: 1;
  height: 100%;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
  line-height: 50px;
  cursor: pointer;
  transition: color 0.4s ease, transform 0.2s ease;
  z-index: 1;
  color: #fff; 
}

.home-toggle-label.old {
  color: #fff;
}

.home-toggle-label.new {
  color: #fff;
}

.home-slider-tab {
  position: absolute;
  height: 100%;
  width: 50%;
  left: 0;
  background: linear-gradient(to right, #003366, #0f2d4b, #062a4f, #05192e);
  border-radius: 0; 
  transition: left 0.3s ease, background 0.3s ease; 
  z-index: 0;
}

input[type="radio"] {
  display: none; 
}

#new:checked ~ .home-slider-tab {
  left: 50%;
  background: linear-gradient(to right, #051d360d, #03172c, #0c2843, #003366);
}

#new:checked ~ .home-toggle-label.old {
  color: #fff;
}

#new:checked ~ .home-toggle-label.new {
  color: #fff;
}

#old:checked ~ .home-toggle-label.old {
  color: #fff;
}

#old:checked ~ .home-toggle-label.new {
  color: #fff;
}

.home-toggle-label:hover {
  transform: scale(1.05);
  color: #e0e0e0;
}

.home-info-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* Ensures cards are displayed in 3 columns */
  gap: 30px; /* Adjust the gap between the cards */
  justify-content: center; /* Centers the grid */
  padding-bottom: 50px;
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;
}
 
/* Show the cards when toggled */
.home-info-cards.show {
  opacity: 1;
  transform: translateY(0);
}
 
/* For Provision Action, force all cards in one row (full-width) */
.home-info-cards.provision-action {
  grid-template-columns: repeat(3, 1fr); /* Only 3 cards per row  Responsive layout for all cards in one row */
}
 
/* For Service Action, only show three cards per row */
.home-info-cards.service-action {
  grid-template-columns: repeat(3, 1fr); /* Only 3 cards per row */
}
.home-card {
  background-color: #17192b;
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  padding: 25px;
  text-align: center;
  width: 300px;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border 0.3s ease;
  position: relative;
  cursor: pointer;
  overflow: hidden;
}

.home-card::before {
  content: '';
  position: absolute;
  top: 0px;
  left: -50px;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, #09539d, #8fbeec, #a4a7b5, #95b4df);
  transition: 0.5s;
  z-index: -1;
  transform: rotate(0deg);
  border-radius: 50%;
  animation: home-rotateStroke 5s linear infinite;
}

@keyframes home-rotateStroke {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.home-card:hover::before {
  transform: rotate(45deg);
}

.home-card:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  border: 1px solid #007bff;
}

.home-card h3 {
  font-size: 20px;
  margin: 0 0 10px;
  color: #78b3e9;
}

.home-card p {
  font-size: 14px;
  color: #c8dcf1;
}

.home-card:hover h3 {
  color: #feffff;
}

.home-card:hover p {
  color: #ffffff;
}

.home-card-btn {
  margin-top: 15px;
  padding: 10px 15px;
  border: none;
  background-color: #08549c;
  color: #fff;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s, transform 0.3s, box-shadow 0.3s;
}

.home-card-btn:hover {
  background-color: #ffffff;
  color: #08549c;
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}
.home-navbar-user {
  position: relative;
  display: flex;
  cursor: pointer;
  align-items: right;
  gap: 8px;
 
  padding: 0 20px;
  flex-shrink: 0;

}
