.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(30, 41, 80, 0.451); /* Semi-transparent white background */
  backdrop-filter: blur(5px); /* Blur effect */
  z-index: 9999; /* Ensure it's above everything */
}

.notification-container1 {
  width: 75%; /* 75% of the screen width */
  max-width: 800px; /* Optional: Limit maximum width */
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 10000; /* Ensure it's above the overlay */
}

.alert-card1 {
  width: 100%; /* Take full width of the container */
  height: auto; /* Adjust height automatically */
  padding: 20px;
  background-color: rgb(255, 255, 255);
  border-radius: 12px;
  border: 3px solid #ffffff00;

  position: relative;
  transition: all 0.3s ease-in-out;
}

.status-card1 {
  width: 100%; /* Take full width of the container */
  padding: 20px;
  background-color: #ffffff;
  border-radius: 12px;
  
  
  position: relative;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center; /* Aligns items vertically */
  gap: 8px;
}

.alert-header1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.loading-icon1 {
  width: 60px;
  height: 60px;
}

.loading-gif {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.alert-message1 {
  margin-left: 10px;
  flex-grow: 1;
  background-color: #f8f9fa00;
  font-size: 16px; /* Slightly larger font */
  font-weight: bolder;
  line-height: 1.2;
  text-align: left;
}

.close-button1 {
  position: absolute;
  top: 10px;
  right: 10px;
  background: none;
  border: none;
  font-size: 24px; /* Slightly larger close button */
  cursor: pointer;
  color: #344d0d;
  padding: 0;
}

.close-button1:hover {
  color: #333;
}

.status-card1.success {
  background-color: #28a745; /* Green for success */
}

.status-card1.done {
  background-color: #0a1646; /* Dark blue for done */
}

.status-card1.error {
  background-color: #dc3545; /* Red for error */
}

.status-icon1 {
  flex-shrink: 0;
  
}

.pop-animation1 {
  animation: pop 0.5s ease-out forwards;
}

.shake-animation1 {
  animation: shake 0.5s ease-out forwards;
}

@keyframes pop {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0% {
    transform: translateX(-10px);
  }
  50% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0);
  }
}

.status-card1 p {
  margin: 0;
  font-size: 16px; /* Slightly larger font */
}