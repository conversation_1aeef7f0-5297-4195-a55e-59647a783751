import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './Terminate.css';
import HIDlogo from './assets/hidLogo.png';
import Ulogo from './assets/Ulogo.png';
import Select from 'react-select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser} from '@fortawesome/free-solid-svg-icons';
import { MdInfo  } from "react-icons/md";
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import Loading from './assets/Rocket.gif';
import Navbar from './Navbar';
function Terminate() {
  const [data, setData] = useState([]);
  const [accountNames, setAccountNames] = useState([]);
  const navigate = useNavigate();
  const [regions, setRegions] = useState([]);
  const [instances, setInstances] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedAccountID, setSelectedAccountID] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedInstance, setSelectedInstance] = useState('');
  const [accountId, setAccountId] = useState([]);
  const [selectedBusinessContact, setSelectedBusinessContact] = useState('');
  const [message, setMessage] = useState('');
  // const [user, setUser] = useState('<EMAIL>');
  const [isAcknowledged, setIsAcknowledged] = useState(false);
  const [instanceDetails, setInstanceDetails] = useState({});
  const[ticket,setTicket] = useState('');
  
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
  const [showPortfolioDropdown, setShowPortfolioDropdown] = useState(false);
 const [Isticket, setIsticket] = useState(false)
  const [alertMessage, setAlertMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [serviceNow, setserviceNow] = useState('yes'); // Tracks whether the user has a ticket
const [generatedTicket, setGeneratedTicket] = useState(''); // Holds the generated ticket
const [isGeneratingTicket, setIsGeneratingTicket] = useState(false); // Tracks ticket generation state

   const [user, setUser] = useState(
 {
    email: '<EMAIL>',
    displayName: 'Guest',
    firstName: 'Guest'
  });
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/user')
      .then(response => {
        const fetchedData = response.data;
         //console.log('Fetched user data:', fetchedData);
       // console.log(user);
        const userEntry = fetchedData.find(entry => entry.user === user.email);
         //console.log('User entry:', userEntry);
  
        if (userEntry) {
          const accountIds = userEntry.accounts.split(',').map(account => account.trim());
           //console.log('Parsed account IDs:', accountIds);
          setAccountId(accountIds);
        } else {
          setAccountId([]);
        }
      })
      .catch(error => {
        // console.error('Error fetching user accounts:', error);
      });
  }, [user]);
  useEffect(() => {
    // Fetch all data on component mount
    if (accountId.length > 0) {
    axios.get('https://umanage.dev.hidglobal.com/api/s3')
      .then(response => {
        let fetchedData = response.data;
        //console.log(fetchedData);
        
        fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
        //console.log('Filtered S3 data:', fetchedData);

        const uniqueAccounts = [...new Set(fetchedData.map(item => item.AccountName))];
        //console.log('Unique account names:', uniqueAccounts);

        setData(fetchedData);
        setAccountNames(uniqueAccounts);
        setData(fetchedData);

        // Extract unique account names
       
      })
      .catch(error => {
        // console.error('Error fetching data:', error);
      });}

  }, [accountId]);

  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
        setUser(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);

  useEffect(() => {
    if (selectedAccount) {
      // Filter regions based on selected account
      const filteredData = data.filter(item => item.AccountName === selectedAccount);
      const uniqueRegions = [...new Set(filteredData.map(item => item.Region))];
      setRegions(uniqueRegions);
    }
  }, [selectedAccount, data]);

  useEffect(() => {
    if (selectedRegion && selectedAccount) {
      // Filter instances based on selected region and account
      const filteredData = data.filter(item => item.Region === selectedRegion && item.AccountName === selectedAccount);
      // Update state to include both id and InstanceName
      setInstances(filteredData.map(item => ({
        id: item.InstanceId,
        label: `${item.InstanceId} - ${item.InstanceName}`,
        name: item.InstanceName // Adjust based on the exact property name
      })));
    }
  }, [selectedRegion, selectedAccount, data]);

  const handleInstanceChange = (selectedOption) => {
    setSelectedInstance(selectedOption.id);

    // Find the selected instance using the selected instance ID
    const instance = data.find((inst) => inst.InstanceId === selectedOption.id);
    if (instance) {
      setInstanceDetails({
        InstanceType: instance.InstanceType,
        InstanceName: instance.InstanceName,
        BusinessArea: instance.BusinessArea,
        CostCenter: instance.CostCenter,
        BusinessSegment: instance.BusinessSegment,
        BusinessContactEmail: instance.BusinessContactEmail,
        Environment: instance.Environment,
        SupportTier: instance.SupportTier,
        ProvisioningEngineer: instance.ProvisioningEngineer,
        TechnicalContact: instance.TechnicalContact,
        FunctionalArea: instance.FunctionalArea,
        BackupPlan: instance.BackupPlan,
      });
      setSelectedAccountID(instance.accountId);
    }

    // Set the business contact based on the instance ID
    const businessContact = data.find((item) => item.InstanceId === selectedOption.id)?.BusinessContact;
    setSelectedBusinessContact(businessContact || '');

    // Smooth scroll to the instance details section
    setTimeout(() => {
      document.querySelector('.instance-details').scrollIntoView({ behavior: 'smooth' });
    }, 200);
  };
  const [showTicketTooltip, setshowTicketTooltip] = useState(false); 
  const[messagestatus, setMessagestatus] = useState();
  const handleTriggerSSM = () => {
    // if (!ticket.startsWith("RITM")&&!ticket.startsWith("REQ")&&!ticket.startsWith("INC")) {
    //   setMessage("Error: Enter the valid Service Now Ticket ID.");
    //   setMessagestatus(false);
    //   return; // Exit the function early if the ticket is invalid
    // }
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    axios.post('https://umanage.dev.hidglobal.com/api/ec2-termination', {
      instanceId: selectedInstance,
      instancename:instanceDetails.InstanceName,
      accountId: selectedAccountID,
      region: selectedRegion,
      businesscontact: instanceDetails.BusinessContactEmail,
      email: user.email,
      ticket: ticket,
      servicenow:serviceNow
    })
      .then(response => {
        setMessage(`Instance terminated successfully. Ticket ID: ${response.data.ticket}`);
        setMessagestatus(true);
      })
      .catch(error => {
        setMessage(`Error: ${error.response ? error.response.data.error : error.message}`);
        setMessagestatus(false);
      });
  };
  async function logout() {
    try {
      // Send a POST request to the backend logout endpoint
      const response = await fetch('/api/logout', {
        method: 'POST',
        credentials: 'include' // Include cookies with the request
      });
      // Check if the response is successful
      if (response.ok) {
        const result = await response.json();
        // console.log(result.message); // Log the success message or handle it as needed
   
        // Optionally, redirect the user to a different page or update the UI
        window.location.href = '/login'; // Redirect to the login page or homepage
      } else {
        // console.error('Logout failed');
      }
    } catch (error) {
      // console.error('Error during logout:', error);
    }
  };
  const instanceOptions = instances.map(instance => ({
    value: instance.id,
    label: `${instance.id} - ${instance.name}`
  }));

  const handleGenerateTicket = async () => {
    setIsGeneratingTicket(true);
    try {
      // Simulate API call to generate a ticket
      const response = await new Promise((resolve) =>
        setTimeout(() => resolve({ data: 'RITM123456' }), 2000)
      );
  
      setGeneratedTicket(response.data); // Set the generated ticket
      setIsticket(true); // Enable the termination button
    } catch (error) {
      console.error('Error generating ticket:', error);
    } finally {
      setIsGeneratingTicket(false);
    }
  };
  
  return (
    <div className="Stop-App">
      <Navbar />

      <div className="full-page-content">
      {(message||alertMessage) &&<div  className="notification-container">
        {alertMessage && !message && (
      <div className="alert-card">
        <div className="alert-header">
          <div className="loading-icon">
            <img src={Loading} alt="Loading" className="loading-gif" />
          </div>
          <p className="alert-message">{alertMessage}</p>
          <button className="close-button" onClick={() => setAlertMessage(null)}><IoIosClose /></button>
        </div>
      </div>
    )}

      {/* Status Message Card */}
      {message && (
        <div className={`status-card ${messagestatus ? 'success' : 'error'}`}>
          <div className={`status-icon ${messagestatus ? 'pop-animation' : 'shake-animation'}`}>
            {messagestatus ? <FaCheckCircle size={24} /> : <FaTimesCircle size={24} />}
          </div>
         <p>{message}</p>
          <button className="close-button"onClick={() => {  setMessage(null); setAlertMessage(null);}}><IoIosClose /></button>
        </div>
      )}
      </div>}

        <h1 className="main-title">Terminate Instance</h1>

        <div className="dropdown-container">
          <div className="dropdown-section">
            <h2 className="dropdown-heading">Account Selection</h2>
            <p className="dropdown-description">Select the account to manage instances.</p>
            <select value={selectedAccount} onChange={(e) => setSelectedAccount(e.target.value)}>
              <option value="">Select Account</option>
              {accountNames.map(account => (
                <option key={account} value={account}>{account}</option>
              ))}
            </select>
          </div>

          <div className="dropdown-section">
            <h2 className="dropdown-heading">Region Selection</h2>
            <p className="dropdown-description">Choose the region for the selected account.</p>
            <select value={selectedRegion} onChange={(e) => setSelectedRegion(e.target.value)} disabled={!selectedAccount}>
              <option value="">Select Region</option>
              {regions.map(region => (
                <option key={region} value={region}>{region}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="dropdown-container">
          <div className="dropdown-section">
            <h2 className="dropdown-heading">Instance Selection</h2>
            <p className="dropdown-description">Pick an instance from the list.</p>
            <Select
              value={instances.find(instance => instance.id === selectedInstance)} // Find the selected instance
              onChange={handleInstanceChange} // Handle instance change
              options={instances} // Options for the dropdown
              isSearchable={true} // Enable search
              placeholder="Search or select an instance"
            />

          </div>
         
</div>

        {selectedInstance && (
          <div className="instance-details">
          <p><strong>Instance Type <span className="colon">:</span></strong> {instanceDetails.InstanceType}</p>
          <p><strong>Instance Name <span className="colon">:</span></strong> {instanceDetails.InstanceName}</p>
          <p><strong>Business Area <span className="colon">:</span></strong> {instanceDetails.BusinessArea}</p>
          <p><strong>Cost Center<span className="colon">:</span></strong> {instanceDetails.CostCenter}</p>
          <p><strong>Business Segment<span className="colon">:</span></strong> {instanceDetails.BusinessSegment}</p>
          <p><strong>Business Contact Email<span className="colon">:</span></strong> {instanceDetails.BusinessContactEmail}</p>
          <p><strong>Environment<span className="colon">:</span></strong> {instanceDetails.Environment}</p>
          <p><strong>Support Tier<span className="colon">:</span></strong> {instanceDetails.SupportTier}</p>
          <p><strong>Provisioning Engineer<span className="colon">:</span></strong> {instanceDetails.ProvisioningEngineer}</p>
          <p><strong>Technical Contact<span className="colon">:</span></strong> {instanceDetails.TechnicalContact}</p>
          <p><strong>Functional Area<span className="colon">:</span></strong> {instanceDetails.FunctionalArea}</p>
          <p><strong>Backup Plan<span className="colon">:</span></strong> {instanceDetails.BackupPlan}</p>
          </div>
        )}

        {selectedInstance && (
          <div className="checkbox-container">
            <input 
              type="checkbox" 
              id="acknowledge" 
              checked={isAcknowledged} 
              onChange={() => setIsAcknowledged(!isAcknowledged)} 
            />
            <label htmlFor="acknowledge" className="checkbox-label">
              I acknowledge the risks associated with terminating this instance.
            </label>
          </div>
        )}

        <button 
          className="TriggerSSM" 
          disabled={!isAcknowledged || !selectedInstance || isProcessing } 
          onClick={handleTriggerSSM}
        >
          Terminate Instance
        </button>
       
      </div>
    </div>
  );
}

export default Terminate;
