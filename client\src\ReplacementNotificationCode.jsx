{(message || alertMessage) && (
  <div className="notification-overlay">
    <div className="notification-container1">
      
      {/* Enhanced Loading State */}
      {alertMessage && !message && (
        <div className="alert-card1">
          <div className="alert-header1">
            <div className="loading-icon1">
              <img src={Loading} alt="Processing..." className="loading-gif" />
            </div>
            <div className="alert-content">
              <p className="alert-message1">{alertMessage}</p>
              <div className="progress-bar">
                <div className="progress-fill"></div>
              </div>
            </div>
            <button 
              className="close-button1" 
              onClick={() => setAlertMessage(null)}
              aria-label="Close notification"
            >
              <IoIosClose />
            </button>
          </div>
        </div>
      )}

      {/* Enhanced Success/Error Status */}
      {message && (
        <div className={`status-card1 ${messagestatus ? 'success' : 'error'}`}>
          <div className={`status-icon1 ${messagestatus ? 'pop-animation1' : 'shake-animation1'}`}>
            {messagestatus ? <FaCheckCircle size={30} /> : <FaTimesCircle size={30} />}
          </div>
          <div className="status-content">
            <p className="status-message">{message}</p>
            <span className="status-label">
              {messagestatus ? 'Success' : 'Error'}
            </span>
          </div>
          <button
            className="close-button1"
            onClick={() => {
              setMessage(null);
              setAlertMessage(null);
            }}
            aria-label="Close notification"
          >
            <IoIosClose />
          </button>
        </div>
      )}
    </div>

    {/* Enhanced Email Notice */}
    <div className="additional-notification">
      <div className="email-icon">📧</div>
      <div className="email-content">
        <p className="email-title">Notification Sent</p>
        <p className="email-subtitle">
          You can close this page. A detailed email will be sent to you shortly.
        </p>
      </div>
    </div>
  </div>
)}
