.cost-centers {
    padding: 20px;
  }
  
  .cost-centers-title {
    text-align: center;
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: bold;
  }
  
  .cost-centers-filter {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
  }
  
  .filter-label {
    font-weight: bold;
  }
  
  .filter-input {
    padding: 5px;
    font-size: 16px;
    width: 200px;
  }
  
  .reset-button {
    padding: 5px 10px;
    font-size: 16px;
    background-color: #007bff;
    color: white;
    border: none;
    cursor: pointer;
  }
  
  .reset-button:hover {
    background-color: #0056b3;
  }
  
  .cost-centers-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .table-header {
    background-color: #f2f2f2;
    font-weight: bold;
    padding: 8px;
    border: 1px solid #ddd;
    text-align: left;
  }
  
  .table-data {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: left;
  }
  