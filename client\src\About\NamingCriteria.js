// Server Naming Dictionary.js
import React from 'react';
import './NamingCriteria.css'; // Ensure this path is correct

const NamingCriteria = () => {
  return (
    <div className="naming-criteria-container">
      <h2 className="naming-criteria-header">Server Naming Dictionary</h2>
      <table className="criteria-table">
        <thead>
          <tr className="criteria-table-row">
            <th className="criteria-table-header">Key</th>
            <th className="criteria-table-header">Definition</th>
            <th className="criteria-table-header">Description</th>
            <th className="criteria-table-header">Acceptable Values</th>
          </tr>
        </thead>
        <tbody>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">P</td>
            <td className="criteria-table-cell">Cloud Provider</td>
            <td className="criteria-table-cell">1-character representing vendor</td>
            <td className="criteria-table-cell">
              A - Amazon AWS <br />
              M - Microsoft Azure <br />
              O - Oracle Cloud Infrastructure
            </td>
          </tr>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">R</td>
            <td className="criteria-table-cell">Global Region</td>
            <td className="criteria-table-cell">1-character representing geographical region of cloud data center</td>
            <td className="criteria-table-cell">
              U - United States (us) <br />
              F - Africa (af) <br />
              A - Asia Pacific (ap) <br />
              C - Canada (ca) <br />
              E - Europe (eu) <br />
              I - Israel (il) <br />
              X - Mexico (mx) <br />
              M - Middle East (me) <br />
              S - South America (sa) <br />
              K - United Kingdom (uk)
            </td>
          </tr>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">L</td>
            <td className="criteria-table-cell">Regional Locale</td>
            <td className="criteria-table-cell">1-character representing regional location</td>
            <td className="criteria-table-cell">
              C - Central <br />
              E - East <br />
              W - West <br />
              S - South <br />
              O - Southeast <br />
              N - Northeast <br />
              M - Melbourne
            </td>
          </tr>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">D</td>
            <td className="criteria-table-cell">Regional Site Designator</td>
            <td className="criteria-table-cell">1-digit representing the physical data center in a given region</td>
            <td className="criteria-table-cell">1, 2, 3, 4, 5, 6, 7, 8, 9</td>
          </tr>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">Z</td>
            <td className="criteria-table-cell">Availability Zone</td>
            <td className="criteria-table-cell">1-alphanumeric character representing the AZ</td>
            <td className="criteria-table-cell">1, 2, 3, 4, A, B, C, D</td>
          </tr>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">E</td>
            <td className="criteria-table-cell">Environment</td>
            <td className="criteria-table-cell">3-character environment abbreviation</td>
            <td className="criteria-table-cell">
              SBX - Sandbox <br />
              DEV - Development <br />
              SUS - Sustaining <br />
              SUP - Support <br />
              AQ1 - Acquisitions 1 <br />
              AQ2 - Acquisitions 2 <br />
              CIN - Customer Integration <br />
              UAT - User Acceptance Testing <br />
              LIV - Live (production)
            </td>
          </tr>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">O</td>
            <td className="criteria-table-cell">Operating System</td>
            <td className="criteria-table-cell">2-character abbreviation</td>
            <td className="criteria-table-cell">
              MS - Microsoft Windows <br />
              LX - Linux <br />
              UX - Unix
            </td>
          </tr>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">A</td>
            <td className="criteria-table-cell">Application Code</td>
            <td className="criteria-table-cell">3-character abbreviation for installed application</td>
            <td className="criteria-table-cell">
              BAS - Bastion Host <br />
              EBA - EBS Application Server <br />
              EBD - EBS Database Server <br />
              DBS - Database Server (other) <br />
              SQL - MSSQL Server <br />
              UTIL - Utility Server <br />
              SMTP - SMTP Relay
            </td>
          </tr>
          <tr className="criteria-table-row">
            <td className="criteria-table-cell">#</td>
            <td className="criteria-table-cell">Server Number</td>
            <td className="criteria-table-cell">
              Two-digit node identifier, prefaced with 0 for values less than 10.
              If application code is overloaded, max values are 1-9.
            </td>
            <td className="criteria-table-cell">01, 02, ..., 98, 99</td>
          </tr>
        </tbody>
      </table>

      <h3 className="naming-criteria-subheader">Examples of Server Name:</h3>
      <table className="example-table">
        <thead>
          <tr className="example-table-row">
            <th className="example-table-header">Example</th>
            <th className="example-table-header">Meaning</th>
          </tr>
        </thead>
        <tbody>
          <tr className="example-table-row">
            <td className="example-table-cell">AUE2AAQ1MSSQL01</td>
            <td className="example-table-cell">
              cloud provider: Amazon <strong>A</strong>WS <br />
              global region: <strong>U</strong>nited States <br />
              regional locale: <strong>E</strong>ast <br />
              regional site designator: <strong>2</strong> <br />
              availability zone: <strong>A</strong> <br />
              environment: <strong>AQ1</strong> (Acquisitions 1) <br />
              operating system: <strong>MS</strong> (Microsoft Windows) <br />
              application code: <strong>SQL</strong> Server <br />
              server number: <strong>01</strong>
            </td>
          </tr>
          <tr className="example-table-row">
            <td className="example-table-cell">OAM13SUPUXIIS02</td>
            <td className="example-table-cell">
              cloud provider: <strong>O</strong>racle Cloud Infrastructure <br />
              global region: <strong>A</strong>sia Pacific <br />
              regional locale: <strong>M</strong>elbourne <br />
              regional site designator: <strong>1</strong> <br />
              availability zone: <strong>3</strong> <br />
              environment: <strong>SUP</strong> (Support) <br />
              operating system: <strong>UX</strong> (Unix) <br />
              application code: <strong>IIS</strong> Server <br />
              server number: <strong>02</strong>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default NamingCriteria;
