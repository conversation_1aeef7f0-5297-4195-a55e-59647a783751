import React from 'react';
import './AboutStart.css'; // Ensure to create this CSS file for styling
import Start from '../assets/about/Start.png';
const AboutStart = () => (
  <div className="about-start-container">
    <h2 className="about-start-title">Start Action</h2>
    <p className="about-start-description">
      The Start action is used to bring a previously  <strong>stopped instance </strong> back online. This action is useful when you need 
      to resume operations on an existing instance without launching a new one, which helps in saving costs while the 
      instance is inactive.
    </p>
    <h3 className="about-start-subtitle">Steps to Start an Instance</h3>
    <ol className="about-start-steps">
      <li className="about-start-step">
        <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
        <div className="about-start-image-container">
          <img src={Start} alt="Select Account Sample" className="about-start-image" />
        </div>
      </li>
      <li className="about-start-step">
        <strong>Select the Region:</strong> Pick the region associated with that account, as instance availability is region-specific.
      </li>
      <li className="about-start-step">
        <strong>Select the Instance:</strong> Locate the instance by its ID or Name for easy identification.
        
      </li>
      <li className="about-start-step">
        <strong>Review Details:</strong> Confirm the instance details, such as configuration and status, before starting.
      </li>
      <li className="about-start-step">
        <strong>Acknowledge:</strong> Check the acknowledgment box to confirm your understanding of the action.
      </li>
      <li className="about-start-step">
        <strong>Click the "Start" Button:</strong> Execute the start action. You can either wait in the portal for a 
        status update or log out, as a confirmation email will be sent indicating the result of the action.
      </li>
    </ol>
    
  </div>
);

export default AboutStart;
