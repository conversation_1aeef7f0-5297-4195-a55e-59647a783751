import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import './Navbar.css'; // Ensure this CSS file exists
import Ulogo from './assets/Ulogo.png'; // Ensure this logo file exists
import HIDlogo from './assets/hidLogo.png';
const Navbar = () => {
    const navigate = useNavigate();
    const [showUserMenu, setShowUserMenu] = useState(false);
    const [showServiceDropdown, setShowServiceDropdown] = useState(false);
    const [showPostDropdown, setShowPostDropdown] = useState(false);
    const [showPortfolioDropdown, setShowPortfolioDropdown] = useState(false);
    const [user, setUser] = useState(
        {
          email: '<EMAIL>',
          displayName: 'test displayname',
          firstName: 'test firstname'
        });
        useEffect(() => {
            async function checkAuth() {
              try {
                const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
                setUser(response.data.user);
                
              } catch (error) {
              
                 // Set user to null in case of an error
              }
              
            }
            checkAuth();
          },[navigate]);
          async function logout() {
            try {
              // Send a POST request to the backend logout endpoint
              const response = await fetch('/api/logout', {
                method: 'POST',
                credentials: 'include' // Include cookies with the request
              });
              // Check if the response is successful
              if (response.ok) {
                const result = await response.json();
                // console.log(result.message); // Log the success message or handle it as needed
           
                // Optionally, redirect the user to a different page or update the UI
                window.location.href = '/login'; // Redirect to the login page or createpage
              } else {
                // console.error('Logout failed');
              }
            } catch (error) {
              // console.error('Error during logout:', error);
            }
          }
    return (
    <div>
    <nav className="navbar">
    <div className="navbar-container">
      <img src={HIDlogo} alt="HID Logo" className="navbar-logo" />

      <div className="navbar-links-center">
      <a href="/" className="navbar-link">Home</a>
        <div 
          className="navbar-link"
          onMouseEnter={() => setShowServiceDropdown(true)}
          onMouseLeave={() => setShowServiceDropdown(false)}
        >
          Service Action
          {showServiceDropdown && (
            <div className="dropdown-menu">
              <a href="/start">Start Instance</a>
              <a href="/stop">Stop Instance</a>
              <a href="/terminate">Terminate Instance</a>
              <a href="/DomainChecker">Domain Checker</a>
            </div>
          )}
        </div>

        <div 
          className="navbar-link"
          onMouseEnter={() => setShowPortfolioDropdown(true)}
          onMouseLeave={() => setShowPortfolioDropdown(false)}
        >
          Provision Action
          {showPortfolioDropdown && (
            <div className="dropdown-menu">
               <a href="/createwindows">Create Windows Instance</a>
              <a href="/createlinux">Create Linux Instance</a>
              
              
            </div>
          )}
        </div>
        <div 
          className="navbar-link"
          onMouseEnter={() => setShowPostDropdown(true)}
          onMouseLeave={() => setShowPostDropdown(false)}
        >
          Custom Scheduler
          {showPostDropdown && (
            <div className="dropdown-menu">
               
             <a href="/custsched">Custom Scheduler</a>
              <a href="/custschedlogs">Custom Scheduler Logs</a>
              
              
            </div>
          )}
        </div>
        <a href="/help" className="navbar-link">Help</a>
        <a href="/approver" className="navbar-link">Approver Zone</a>
        
      </div>

      
      <div className="navbar-user"
       onMouseEnter={() => setShowUserMenu(true)} 
       onMouseLeave={() => setShowUserMenu(false)}>
      <span className="user-name">{user.firstName}</span> {/* Display user's name */}
     
      <img src={Ulogo} alt="HID Logo" className="user-icon" />
      {/* Dropdown remains for additional actions */}
      {showUserMenu && (
        <div className="user-menu">
          <span className="user-email">{user.email}</span>
          <button className="logout-btn" onClick={logout}>Logout</button>
        </div>
      )}
    </div>
    </div>
  </nav>
  </div>
  );
};

export default Navbar;
