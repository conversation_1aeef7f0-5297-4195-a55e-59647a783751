 
const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const passport = require('passport');
const errorhandler = require('errorhandler');
const AWS = require('aws-sdk');
const { Readable } = require('stream');
const csvParser = require('csv-parser');
const path = require('path');
const fs = require('fs');
const https = require('https');
const http = require('http');
const cron = require('node-cron');
const app = express();
const httpport = 80;
const httpsport = 443;


app.use(cors({
  origin: 'https://umanage.dev.hidglobal.com',
  credentials: true
}));
app.use(bodyParser.json());
app.use(cookieParser());
app.use(bodyParser.urlencoded({ extended: false }));
app.use(session({
  name: 'HID',
  secret:"Hidglobal",
  resave:false,
  saveUninitialized: false,
  cookie: { maxAge: 18000000, secure: true } // Cookie expiration time set to 2 minutes
}));
app.use(passport.initialize());
app.use(passport.session());
 
const s3 = new AWS.S3();
const ssm = new AWS.SSM();
const hostname = 'umanage.dev.hidglobal.com';
 
require('dotenv').config();

// Load configuration
var env = process.env.NODE_ENV || 'development';
const config = require('./config/config')[env];
console.log('Using configuration', config);
// AWS Configuration
const XLSX = require('xlsx');
const scheduledJob = async (docname,region) => {
  try {
    console.log("in schedule");
      const bucketName = 'server-provision-application';
      const fileName = 'Data/aws-Timer.xlsx';
 
      // Get the Excel file from S3
      const params = {
          Bucket: bucketName,
          Key: fileName,
      };
     
      const data = await s3.getObject(params).promise();
     
      const workbook = XLSX.read(data.Body, { type: 'buffer' });
     
      const sheetName = workbook.SheetNames[0]; // Assuming you want the first sheet
      const worksheet = workbook.Sheets[sheetName]; console.log(worksheet);
 
      // Convert sheet to JSON
      const rows = XLSX.utils.sheet_to_json(worksheet,{ header: 1 });
      console.log(rows);
      // Loop through each row and call SSM document
      for (const row of rows) {
       
 
          await callSSMDocument(row,docname,region);
      }
 
      console.log('All tasks completed.');
  } catch (error) {
      console.error('Error in scheduled job:', error);
  }
};
 
const assumeRole = async (accountId,firstname) => {
  const sts = new AWS.STS();
  const params = {
    RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
    RoleSessionName: firstname,
  };
  console.log(firstname);
  console.log(params);
  try {
    const data = await sts.assumeRole(params).promise();
    return data.Credentials;
  } catch (error) {
    console.error('Error assuming role:', error);
    throw error;
  }
}; const initializeAWS = async (credentials, region) => {
  AWS.config.update({
    credentials: new AWS.Credentials(
      credentials.AccessKeyId,
      credentials.SecretAccessKey,
      credentials.SessionToken
    ),
    region: region
  });
};
const configureAWS = () => {
  return {
    ssm: new AWS.SSM(),
    s3: new AWS.S3(),
    cloudFormation: new AWS.CloudFormation()
  };
};let originalCredentials = AWS.config.credentials;
// Function to call SSM document
const callSSMDocument = async (row,docname,region) => {
  console.log(row);
  const instanceIds = row[1].split(','); // Assuming Column2 contains comma-separated instance IDs
 
 
  try {
   
    if(String(row[2])===String(region)){
      if(String(row[0])!=String(324651370228)){
     console.log(row[2]);
      const credentials = await assumeRole(row[0],'qwerrt');
      await initializeAWS(credentials, region);}else{AWS.config.update({
        region: region
      });}
      const { ssm } = configureAWS();
      const instanceIds = row[1].split(',').map(id => id.trim()).filter(id => id); // Trim and filter empty IDs
 
        const params = {
          DocumentName: docname,
          Parameters: {
              // Pass the parameters needed for your SSM document
              InstanceId: instanceIds, // Example: Adjust based on your Excel structure
             
          },
      };
      console.log(params);
      console.log(params.Parameters);
      const respons1 = await ssm.startAutomationExecution(params).promise(); AWS.config.update({ credentials: originalCredentials });
      console.log(`SSM Document executed for row: ${JSON.stringify(row)}`);}
  } catch (error) {
      console.error(`Error executing SSM Document for row: ${JSON.stringify(row)}`, error);
  }
};
 

const storeDataInS3 = async (ticketNumber, executionID, instancename,accountId, DocumentName,  ExecutionStartTime,   ExecutionEndTime,  AutomationExecutionStatus,   InstanceId,businesscontact,email,accountname,servicenownumber) => {
  try {
    // Define S3 parameters to get the existing Excel file
    const getObjectParams = {
      Bucket: 'server-provision-application',
      Key: 'tickets/start and stop logs.xlsx', // Change this to the path where your Excel file is stored
    };
 
    // Fetch the existing file from S3
    const data = await s3.getObject(getObjectParams).promise();
    let workbook;
 
    if (data.Body.length > 0) {
      // Read the existing file
      workbook = XLSX.read(data.Body, { type: 'buffer' });
    } else {
      // Create a new workbook if none exists
      workbook = XLSX.utils.book_new();
    }
 
    // Get or create the sheet
    const sheetName = 'Tickets';
    let sheet;
 
    if (workbook.SheetNames.includes(sheetName)) {
      sheet = workbook.Sheets[sheetName];
    } else {
      // Create a new sheet with headers if the sheet does not exist
      sheet = XLSX.utils.aoa_to_sheet([
        ['Ticket Number', 'Execution ID', 'Execution Details', 'Business Contact', 'Email'],
      ]);
    }
 
    // Convert sheet to JSON and append new data
    const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    jsonData.push([ticketNumber, executionID, instancename,accountId, DocumentName,  ExecutionStartTime,   ExecutionEndTime,  AutomationExecutionStatus,   InstanceId,businesscontact,email,accountname]);
 
    // Convert updated JSON back to sheet
    const updatedSheet = XLSX.utils.aoa_to_sheet(jsonData);
    workbook.Sheets[sheetName] = updatedSheet;
 
    // Convert workbook to buffer
    const updatedFile = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
   
    // Upload the updated file back to S3
    const uploadParams = {
      Bucket: 'server-provision-application',
      Key: 'tickets/start and stop logs.xlsx', // Ensure this path matches where you want to store the file
      Body: updatedFile,
      ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
 
    await s3.putObject(uploadParams).promise();
    console.log('Excel file updated successfully!');
  } catch (error) {
    console.error('Error updating Excel file:', error);
  }
};

const storeEBSLogsInS3 = async (ticketNumber, executionID, instancename,accountId, DocumentName,  ExecutionStartTime,   ExecutionEndTime,  AutomationExecutionStatus,   InstanceId,businesscontact,email,volumeID,region,accountname,servicenownumber) => {
  try {
    // Define S3 parameters to get the existing Excel file
    const getObjectParams = {
      Bucket: 'server-provision-application',
      Key: 'tickets/EBS-Logs.xlsx', // Change this to the path where your Excel file is stored
    };
 
    // Fetch the existing file from S3
    const data = await s3.getObject(getObjectParams).promise();
    let workbook;
 
    if (data.Body.length > 0) {
      // Read the existing file
      workbook = XLSX.read(data.Body, { type: 'buffer' });
    } else {
      // Create a new workbook if none exists
      workbook = XLSX.utils.book_new();
    }
 
    // Get or create the sheet
    const sheetName = 'Sheet1';
    let sheet;
 
    if (workbook.SheetNames.includes(sheetName)) {
      sheet = workbook.Sheets[sheetName];
    } else {
      // Create a new sheet with headers if the sheet does not exist
      sheet = XLSX.utils.aoa_to_sheet([
        ['Ticket Number', 'Execution ID', 'Execution Details', 'Business Contact', 'Email'],
      ]);
    }
 
    // Convert sheet to JSON and append new data
    const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    jsonData.push([ticketNumber, executionID, instancename,accountId, DocumentName,  ExecutionStartTime,   ExecutionEndTime,  AutomationExecutionStatus,   InstanceId,businesscontact,email,volumeID,region,accountname]);
 
    // Convert updated JSON back to sheet
    const updatedSheet = XLSX.utils.aoa_to_sheet(jsonData);
    workbook.Sheets[sheetName] = updatedSheet;
 
    // Convert workbook to buffer
    const updatedFile = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
   
    // Upload the updated file back to S3
    const uploadParams = {
      Bucket: 'server-provision-application',
      Key: 'tickets/EBS-Logs.xlsx', // Ensure this path matches where you want to store the file
      Body: updatedFile,
      ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
 
    await s3.putObject(uploadParams).promise();
    console.log('Excel file updated successfully!');
  } catch (error) {
    console.error('Error updating Excel file:', error);
  }
};
const httpsOptions = {
  cert: fs.readFileSync('./certificate/pc-01.crt'),
  key:fs.readFileSync('./certificate/pc-01.key')
}
const httpserver = http.createServer(app);
const httpsserver = https.createServer(httpsOptions, app);
 
app.use((req, res, next) => {
  if(req.protocol === 'http') {
    res.redirect(301, `https://${req.headers.host}${req.url}`);
  }
  next();
});
 
// Configure CORS with credentials
 
require('./config/passport')(passport, config);
app.post("/", passport.authenticate(config.passport.strategy, {
 
  failureRedirect: '/auth/failure', // A path to handle failures
  failureFlash: true
}), (req, res) => { console.log("in th post");
  // Send a success response
  res.redirect('https://umanage.dev.hidglobal.com/home');
});
app.get("/", passport.authenticate(config.passport.strategy, {
  failureRedirect: '/auth/failure', // A path to handle failures
  failureFlash: true
}), (req, res) => { console.log("in th post");
  // Send a success response
  res.redirect('https://umanage.dev.hidglobal.com/home');
});
 
// Utility Functions
let currentTicketNumber = 0;
 
const generateTicketNumber = () => {
  currentTicketNumber += 1;
  return `TICKET-${currentTicketNumber.toString().padStart(6, '0')}`;
};
 
const storeDataInS3ForTermination = async (ticketNumber, executionID, accountId, InstanceName,  ExecutionStartTime,   ExecutionEndTime,  AutomationExecutionStatus,   InstanceId,businesscontact,email,accountname, instancename,region,servicenownumber) => {
 console.log("in server.js terminatuon");
  try {
    // Define S3 parameters to get the existing Excel file
    const getObjectParams = {
      Bucket: 'server-provision-application',
      Key: 'tickets/terminate_instance.xlsx', // Change this to the path where your Excel file is stored
    };
 
    // Fetch the existing file from S3
    const data = await s3.getObject(getObjectParams).promise();
    let workbook;
 
    if (data.Body.length > 0) {
      // Read the existing file
      workbook = XLSX.read(data.Body, { type: 'buffer' });
    } else {
      // Create a new workbook if none exists
      workbook = XLSX.utils.book_new();
    }
 
    // Get or create the sheet
    const sheetName = 'Sheet1';
    let sheet;
 
    if (workbook.SheetNames.includes(sheetName)) {
      sheet = workbook.Sheets[sheetName];
    } else {
      // Create a new sheet with headers if the sheet does not exist
      sheet = XLSX.utils.aoa_to_sheet([
        ['Ticket Number', 'Execution ID', 'Execution Details', 'Business Contact', 'Email'],
      ]);
    }
 
    // Convert sheet to JSON and append new data
    const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
    jsonData.push([ticketNumber, executionID, InstanceName,  ExecutionStartTime,   ExecutionEndTime,  AutomationExecutionStatus,   InstanceId, businesscontact, email,accountname, instancename,region,servicenownumber]);
 
    // Convert updated JSON back to sheet
    const updatedSheet = XLSX.utils.aoa_to_sheet(jsonData);
    workbook.Sheets[sheetName] = updatedSheet;
 
    // Convert workbook to buffer
    const updatedFile = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
   
    // Upload the updated file back to S3
    const uploadParams = {
      Bucket: 'server-provision-application',
      Key: 'tickets/terminate_instance.xlsx', // Ensure this path matches where you want to store the file
      Body: updatedFile,
      ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
 
    await s3.putObject(uploadParams).promise();
    console.log('Excel file updated successfully!');
  } catch (error) {
    console.error('Error updating Excel file:', error);
  }
};
// Function to fetch, update, and upload Excel file to S3
const storeDataInS3forCreate = async (params ,startTime,finishTime, result,logoutput) => {
  console.log(logoutput);
  try {
    // Define S3 parameters to get the existing Excel file
    const getObjectParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/Create.xlsx', // Change this to the path where your Excel file is stored
    };
    let ticket;
    if(params.servicenow=='NO'){

      const servicenowResponse = await fetch(
        'https://integration.nonprod.hidglobal.com/uat/ws/rest/ServiceNow/Incident/',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/xml',
            Authorization: 'Basic ' + btoa('servicenowuser-uat:servicenowuser-uat@hidglobal$yd786'),
            'x-api-key': 'd9a45eed-eeb2-4e76-840b-e6c98c97a9d6',
          },
          body: `
            <Incident>
                <user>${email}</user>
                <short_description>${instancename} Termination</short_description>
                <detailed_description>${ticket}</detailed_description>
            </Incident>
          `,
        }
      );
    
      const servicenowResponseText = await servicenowResponse.text();
    
      if (!servicenowResponse.ok) {
        throw new Error(`ServiceNow Error: ${servicenowResponse.status} ${servicenowResponseText}`);
      }
      params.ProvisioningJustification=servicenowResponseText;
      // res.status(200).json({
      //   message: 'SSM Automation Document triggered successfully!',
      //   executionID: executionID,
      //   ticketNumber: ticketNumber,
      //   servicenowResponse: servicenowResponseText,
      // });
    } 
    console.log("in store s3");
    console.log(result);
    // Fetch the existing file from S3
    const data = await s3.getObject(getObjectParams).promise();
    let workbook;
 
    if (data.Body.length > 0) {
        // Read the existing file
        workbook = XLSX.read(data.Body, { type: 'buffer' });
    } else {
        // Create a new workbook if none exists
        workbook = XLSX.utils.book_new();
    }
 
    // Get or create the sheet
    const sheetName = 'Tickets';
    let sheet;
 
    if (workbook.SheetNames.includes(sheetName)) {
        sheet = workbook.Sheets[sheetName];
    } else {
        // Create a new sheet with headers if the sheet does not exist
        sheet = XLSX.utils.aoa_to_sheet([[
            'InstanceName', 'InstanceType', 'AMI', 'SecurityGroupIds', 'SubnetId',
            'IAMRoleName', 'CostCenter', 'CostCenterDescription', 'SupportTier',
            'SupportTierDescription', 'InstanceSource', 'ProvisioningEntity',
            'ProvisioningJustification', 'BusinessArea', 'BusinessContact',
            'BusinessContactEmail', 'BusinessSegment', 'BusinessSegmentDescription',
            'TechnicalContact', 'TechnicalContactEmail', 'Environment',
            'NetworkLocation', 'FunctionalArea', 'ProvisioningEngineer', 'BackupPlan','result'
        ]]);
    }
    let x="";
    let newRow;
    try{
     x=result.$metadata.httpStatusCode==200?"Success":result;
      newRow = [
      params.InstanceName,
      logoutput[8].OutputValue,
      logoutput[16].OutputValue,
      params.InstanceType,
      params.AMI,
      params.securityGroupIds,
      params.subnetId,
      'HID-EC2-SSM-role',
     `${ params.CostCenter}`,
      params.CostCenterDescription,
      params.SupportTier,
      params.SupportTierDescription,
      'NEW',
      params.ProvisioningEntity,
      params.ProvisioningJustification,
      params.BusinessArea,
      params.BusinessContact,
      params.BusinessContactEmail,
     `${params.BusinessSegment}` ,
      params.BusinessSegmentDescription,
      params.TechnicalContact,
      params.TechnicalContactEmail,
      params.Environment,
      params.NetworkLocation,
      params.FunctionalArea,
      params.ProvisioningEngineer,
      params.BackupPlan || '',
      x,
      startTime,
      finishTime,
     
      logoutput[25].OutputValue,
      logoutput[26].OutputValue,
      logoutput[27].OutputValue,
      logoutput[28].OutputValue,

  ];}catch{
      x=result;
       newRow = [
        params.InstanceName,
        "",
        "",
        params.InstanceType,
        params.AMI,
        params.securityGroupIds,
        params.subnetId,
        'HID-EC2-SSM-role',
        `  ${params.CostCenter}`,
        params.CostCenterDescription,
        params.SupportTier,
        params.SupportTierDescription,
        'NEW',
        params.ProvisioningEntity,
        params.ProvisioningJustification,
        params.BusinessArea,
        params.BusinessContact,
        params.BusinessContactEmail,
        ` ${params.BusinessSegment}`,
        params.BusinessSegmentDescription,
        params.TechnicalContact,
        params.TechnicalContactEmail,
        params.Environment,
        params.NetworkLocation,
        params.FunctionalArea,
        params.ProvisioningEngineer,
        params.BackupPlan || '',
        x,
        startTime,
        finishTime,
       
        "",
        "",
        "",
        "",

    ];
    }
    // Convert sheet to JSON
    const jsonData = XLSX.utils.sheet_to_json(sheet, { header: 1 });
 
    // Prepare a new row with parameters
    
    console.log(x);
    // Append the new row to jsonData
    jsonData.push(newRow);
 
    // Convert updated JSON back to sheet
    const updatedSheet = XLSX.utils.aoa_to_sheet(jsonData);
    workbook.Sheets[sheetName] = updatedSheet;
 
    // Convert workbook to buffer
    const updatedFile = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
 
    // Upload the updated file back to S3
    const uploadParams = {
        Bucket: 'server-provision-application',
        Key: 'tickets/Create.xlsx', // Ensure this path matches where you want to store the file
        Body: updatedFile,
        ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
 
    await s3.putObject(uploadParams).promise();
    console.log('Excel file updated successfully!');
} catch (error) {
    console.error('Error updating Excel file:', error);
}
};
 let ticketfortermination=0;
 
const generateTicketNumbertermination = () => {
  ticketfortermination += 1;
  return `TICKET-${ticketfortermination.toString().padStart(6, '0')}`;
};
app.use('/api/s3', require('./routes/s3')(s3, Readable, csvParser));
app.use('/api/ebs', require('./routes/ebs')(s3, Readable, csvParser));
app.use('/api/access', require('./routes/access')(s3, Readable, csvParser));
app.use('/api/tags', require('./routes/tags')(s3, Readable, csvParser));
app.use('/api/get-from-s3', require('./routes/get-from-s3')(s3, Readable, csvParser));
app.use('/api/ec2-price', require('./routes/ec2-price')(s3, Readable, csvParser));
app.use('/api/ec2-termination', require('./routes/termination-ec2')(storeDataInS3ForTermination, generateTicketNumbertermination));
app.use('/api/vpc-subnet', require('./routes/vpc-subnet')(s3, Readable, csvParser));
app.use('/api/user', require('./routes/user')(s3, Readable, csvParser));
 app.use('/api/trigger-ssm', require('./routes/trigger-ssm')( generateTicketNumber, storeDataInS3));
 app.use('/api/ebs', require('./routes/ebs-action')( generateTicketNumber, storeEBSLogsInS3));
 app.use('/api/Account-Migration', require('./routes/crossMigration')( generateTicketNumber, storeDataInS3));
 app.use('/api/schedule', require('./routes/scheduler')( Readable, storeDataInS3));
//app.use('/api/deploy-stack', require('./routes/service-catalog')( generateTicketNumber, storeDataInS3));
app.use('/api/deploy-stack', require('./routes/deploy-stack')(storeDataInS3forCreate));
app.use('/api/logs', require('./routes/user-logs')(s3, Readable, csvParser));
app.use('/api/migration', require('./routes/migration')(s3, Readable, csvParser));
app.use('/api/feedback', require('./routes/feedback')(s3, Readable, csvParser));
app.use('/api/email', require('./routes/s3')(s3, Readable, csvParser));
app.use('/api/adax', require('./routes/adx'));
app.use('/api/tag-ramediator', require('./routes/fixer'));
app.use('/api/quicksight', require('./routes/quicksight'));
app.use('/api/umanage/sendingemail', require('./routes/email'));
app.use('/api/create-pipeline', require('./routes/Create-pipeline')( generateTicketNumber, storeDataInS3));
app.use('/api/clipboard-pipeline', require('./routes/clipboard')( generateTicketNumber, storeDataInS3));

app.use('/api/accountform', require('./routes/accountform')( generateTicketNumber, storeDataInS3));
app.use('/api/customcreate', require('./routes/CustomCreate')( generateTicketNumber, storeDataInS3));
app.use('/api/customschedule', require('./routes/CustomScheduler')( generateTicketNumber, storeDataInS3));
app.use('/api/create-pipeline-foraccountMigration', require('./routes/crossMigrationCreate')( generateTicketNumber, storeDataInS3));
// Profile Route
app.get('/api/profile', (req, res) => {
  if (req.isAuthenticated()) {
    res.json({ user: req.user });
   // console.log(req.user);
  } else {
    res.status(401).json({ error: 'Unauthorized' });
  }
});
// Logout Route
app.post('/api/logout', (req, res) => {
  req.logout((err)=>{
    if(err){
      return next(err);
    }
  });
  req.session.destroy((err)=>{
    if(err){
      return next(err);
    }
  })
  res.clearCookie('HID');
 
  res.json({ message: 'Logged out successfully' });
});
 
// Error Handling Middleware
if (env === 'development') {
  app.use(errorhandler());
}
 
app.use(express.static(path.join(__dirname, 'build')));
 
// Handle any other routes and serve the React frontend
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'build', 'index.html'));
});
 
httpserver.listen(httpport, hostname);
httpsserver.listen(httpsport, hostname);
//fetchAndUpdateWeeklyReport();