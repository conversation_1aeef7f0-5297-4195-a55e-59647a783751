const axios = require('axios');
const https = require('https');

// ------------------------ 
// ⚙️ Configuration
// ------------------------
const username = '<EMAIL>';
const password = 'x5EMe7a4W3Qfb6FDo82N';
const BASE_URL = 'https://adx-api.assaabloy.net/restApi';

// Disable SSL verification (Remove this in production)
const axiosInstance = axios.create({
  httpsAgent: new https.Agent({ rejectUnauthorized: false }),
});

// ------------------------
// 🟢 Step 1: Create Session
// ------------------------
async function createSession() {
  try {
    const url = `${BASE_URL}/api/authSessions/create`;
    const payload = { username, password };

    const response = await axiosInstance.post(url, payload, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.status === 200) {
      const sessionId = response.data.sessionId;
      console.log(`✅ Session created successfully. Session ID: ${sessionId}`);
      return sessionId;
    } else {
      console.error(`❌ Error creating session: ${response.status} - ${response.data}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Error creating session: ${error.message}`);
    process.exit(1);
  }
}

// ------------------------
// 🔐 Step 2: Get Auth Token
// ------------------------
async function getAuthToken(sessionId) {
  try {
    const url = `${BASE_URL}/api/auth`;
    const payload = {
      sessionId,
      type: 0,
    };

    const response = await axiosInstance.post(url, payload, {
      headers: { 'Content-Type': 'application/json' },
    });

    if (response.status === 200) {
      const token = response.data.token;
      console.log(`✅ Authentication token obtained.`);
      console.log(token);
      return token;
    } else {
      console.error(`❌ Error obtaining authentication token: ${response.status} - ${response.data}`);
      process.exit(1);
    }
  } catch (error) {
    console.error(`❌ Error obtaining authentication token: ${error.message}`);
    process.exit(1);
  }
}

//------------------------
// 🖥️ Step 3: Create Server
// ------------------------
async function createServer1(token) {
    try {
      const url = `${BASE_URL}/api/directoryObjects/executeCustomCommand`;
      const headers = {
        'Adm-Authorization': token,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      };
  //
  let template1 = '';
const region='A';
switch (region) {
  case 'U':
    template1 = 'OU=USAWS,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global';
    break;
  case 'A':
    template1 = 'OU=INAWS,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global';
    break;
  case 'E':
    template1 = 'OU=DEAWS,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global';
    break;
  default:
    template1 = ''; // Default if no match
}

console.log(template1);
      const payload = {
        directoryObject: 'DC=ad,DC=global',
        customCommandId: 'c39819b3-4a52-4476-9950-3e2e6c8bfdd5', // Verify this ID
        parameters: [
          { type: 'Text', name: 'param-Provider', value: 'A' },
          { type: 'Text', name: 'param-Region', value: 'F' },
          { type: 'Text', name: 'param-Locale', value: 'C' },
          { type: 'Text', name: 'param-SiteDesignator', value: '1' },
          { type: 'Text', name: 'param-Zone', value: '1' },
          { type: 'Text', name: 'param-Environment', value: 'SBX' },
          { type: 'Text', name: 'param-OS', value: 'MS' },
          { type: 'Text', name: 'param-Application', value: 'TES' },
          { type: 'Text', name: 'param-PatchingGroup', value: 'DEVTEST' },
          { type: 'Text', name: 'param-Description', value: 'This is a test' },
          {
            type: 'ADObject',
            name: 'param-SiteOU',
            value: [
              {
                referenceType: 1,
                "template": 'OU=CNSUH,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global', // Verify this DN
              },
            ],
          },
          {
            type: 'ADObject',
            name: 'param-ManagedBy',
            value: [
              {
                referenceType: 1,
                "template": 'CN=S-APP-ADX-API-HID-Automation,OU=API Access,OU=ServiceAccounts,OU=ADX,OU=GSSC,OU=Centrally Secured Applications,DC=ad,DC=global', // Verify this DN
              },
            ],
          },
        ],
      };
  
      // Debugging - Print Headers & Payload
      console.log('Headers:', JSON.stringify(headers, null, 2));
      console.log('Request Payload:', JSON.stringify(payload, null, 2));
  
      // Send POST request to execute custom command
      const response = await axiosInstance.post(url, payload, { headers });
  
      console.log(`Response Status Code: ${response.status}`);
      console.log(`Response Data: ${JSON.stringify(response.data, null, 2)}`);
  
      if (response.status === 200) {
        // Check for innerMessages or potential errors in the response
        const messages =
        response.data?.innerMessages?.[0]?.innerMessages?.[0]?.innerMessages || [];
      
      
      const generatedName = messages
        .filter((msg) => msg.text.includes('Generated name = '))
        .map((msg) => msg.text.split('Generated name = ')[1]);
      
  console.log(generatedName);
  console.log(template1);
        if (generatedName.length > 0) {
          console.log(`✅ Server created successfully. Generated Name: ${generatedName[0]}`);
        } else {
          console.log('✅ Server created, but no name generated.');
        }
      } else {
        console.error(`❌ Error executing custom command: ${response.status} - ${response.data}`);
      }
    } catch (error) {
      if (error.response) {
        console.error(
          `❌ Error: ${error.response.status} - ${JSON.stringify(error.response.data, null, 2)}`
        );
      } else {
        console.error(`❌ Error executing custom command: ${error.message}`);
      }
    }
  }
  
// ------------------------
// 🚀 Main Function
// ------------------------
async function createServer(token) {
    const url = `${BASE_URL}/api/customCommands/c39819b3-4a52-4476-9950-3e2e6c8bfdd5`;
  
    try {
      const response = await axios.get(url, {
        headers: {
          'Adm-Authorization': token,
          
          'Accept': 'application/json',
        },
      });
  
      console.log('✅ Custom Command Details:');
      console.log(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error(`❌ Error fetching custom command details: ${error.message}`);
    }
}
async function main() {
  try {
    const sessionId = await createSession();
    const token = await getAuthToken(sessionId);
    console.log(token);
    await createServer1(token);
  } catch (error) {
    console.error(`❌ Workflow failed: ${error.message}`);
  }
}


main();


// const { exec } = require('child_process');
// const path = require('path');

// // Path to your Python script
// const pythonScriptPath = path.join(__dirname, 'create-server.py');

// // Run Python script
// exec(`py "${pythonScriptPath}"`, (error, stdout, stderr) => {
//   if (error) {
//     console.error(`❌ Error executing Python script: ${error.message}`);
//     return;
//   }
//   if (stderr) {
//     console.error(`⚠️ Python Error: ${stderr}`);
//     return;
//   }

//   // Print Python output
//   console.log(`✅ Python Output: ${stdout}`);
// });

async function getCustomCommandDetails(token, commandId) {
    const url = `${BASE_URL}/api/customCommands/c39819b3-4a52-4476-9950-3e2e6c8bfdd5`;
  
    try {
      const response = await axios.get(url, {
        headers: {
          'Adm-Authorization': token,
          'Accept': 'application/json',
        },
      });
  
      console.log('✅ Custom Command Details:');
      console.log(JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.error(`❌ Error fetching custom command details: ${error.message}`);
    }
  }
  
  // Call with valid commandId
  //getCustomCommandDetails("Abug0X1xSItGttW7fI0K+ewHGCH1p+vS9r4THsVsqKRG/j9W5XtacTZuYmV9k/regnO26/kb7lxKPkawm5WV+l/AFbbD/AQImzlEmPY8yVMHnlueTW2ITba+ytQssQ/iGHSCE9Q5TnJihUk+KnhGLoRftyW98eeKFXkWJ7Ea1/dmSXyE5SjeDvZ9e2UsFe2SgLnha9KhnASlCzPd9O50wrNrp8UI8XKBMaNiphuLNtX3+mjwFrUhBk/nABBUE+EdgdpVeRQPaebPQdzlfNZodX9rwWHHlNioi8B/oDq7wKpHuhvwe3usWonxzMN0/lO/sTTWFTBitM6VZl4DM3uqpYWvMvyR7jYp3xCG7GcgNMxq1N2uC+r6MGbFHeSDZ5XD/0GruGHL4zUNsZyPqs9DKQcDmXJVxP+26e4/Q+x2abRN5w==", 'lY2OIJfkZAYnj4egbALd32MFVouXQFvFM_KJ1DSZ');
  