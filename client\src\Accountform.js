import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import 'bootstrap/dist/css/bootstrap.min.css';
import { faCog, faHdd, faNetworkWired, faTags, faCheckCircle ,faUser} from '@fortawesome/free-solid-svg-icons';
import './Create.css';
import Select from 'react-select';
import HIDlogo from './assets/hidLogo.png';
import Ulogo from './assets/Ulogo.png';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import { MdInfo  } from "react-icons/md";
import Loading from './assets/Rocket.gif'
import Navbar from './Navbar';
const Accountform = () => {
  const [step, setStep] = useState(1);
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    InstanceName: '',
    InstanceType: '',
    AMI: '',
    securityGroupIds: [],
    subnetId: '',
    UseBlockDeviceMappings: '',
    CreateIAMRole:'no',
    IAMRoleName: '' ,
   
    CostCenter: '',
    CostCenterDescription: '',
    SupportTier: '',
    SupportTierDescription: '',
    InstanceSource: 'NEW',
    ProvisioningEntity: '',
    ProvisioningJustification: '',
    BusinessArea: '',
    BusinessContact: '',
    BusinessContactEmail: '',
    BusinessSegment: '',
    BusinessSegmentDescription: '',
    TechnicalContact: '',
    TechnicalContactEmail: '',
    Environment: '',
    NetworkLocation: 'INTERNAL',
    FunctionalArea: '',
    ProvisioningEngineer: '',
    BackupPlan: '',
    accountId:"",
    DeviceName : '/dev/xvda',
    useIAMRole: '' ,
    createOptionalVolume: 'no',
    volume1: { ebsVolumeSize: '',  ebsVolumeType: 'gp3', DeviceName: '/dev/sda1' },
    volume2: null,
    volume3: null,
    volume4: null,
    volume5: null ,
    volumes: {}, // Initialize as an empty object
    selectedVolume: 'volume1',
    volumeCount: 1,
     UseCase: '',
  UseCaseDescription: ''
  });
 
  const [message, setMessage] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [accounts, setAccounts] = useState([]);
  const [vpcs, setVpcs] = useState([]);
  const [subnets, setSubnets] = useState([]);
  const [regions, setRegions] = useState([]);
  const [securityGroups, setSecurityGroups] = useState([]);
  const[messagestatus, setMessagestatus] = useState();
  const [instances, setInstances] = useState([]);
  const [filteredAMIs, setFilteredAMIs] = useState([]);
  const [selectedVCPU, setSelectedVCPU] = useState('');
  const [selectedMemory, setSelectedMemory] = useState('');
  const [compatibleInstancesOptions, setCompatibleInstancesOptions] = useState([]);
  const [VCPUoptions, setVCPUoptions] = useState([]);
  const [Memoryoptions, setMemoryoptions] = useState([]);
  const [compatibleInstances, setCompatibleInstances] = useState([]);
 
  const [selectedSecurityGroups, setSelectedSecurityGroups] = useState([]); // State for selected Security Groups

  const [selectedBusinessArea, setSelectedBusinessArea] = useState('');
  const [selectedBusinessSegment, setSelectedBusinessSegment] = useState('');
  const [selectedCostCenter, setSelectedCostCenter] = useState('');
  const [selectedBusinessSegmentDescription, setSelectedBusinessSegmentDescription] = useState('');
  const [selectedCostCenterDescription, setSelectedCostCenterDescription] = useState('');
  const [businessSegments, setBusinessSegments] = useState([]);
   const [costCenters, setCostCenters] = useState([]);
   const [businessDescriptions, setbusinessDescriptions] = useState([]);
   const [selectedFunctionArea, setSelectedFunctionArea] = useState('');
  const [email, setEmail] = useState('');
  const [backupPlanLabel, setBackupPlanLabel] = useState('');
  const [envPlanLabel, setEnvPlanLabel] = useState('');
  const [data1, setData1] = useState({
    uniqueBusinessAreas: [],
    businessSegmentPairs: {},
    costCenterPairs: {},
 
  });
  
  const [user, setUser] = useState(
    {
      email: '<EMAIL>',
      displayName: 'test displayname',
      firstName: 'test firstname'
    });
  const [systemTags, setSystemTags] = useState({
    supportTiers: [],
    supportTierPairs: [],
    instanceSources: []
  });
  const [platform, setPlatform] = useState("windows");
  const [archi, setarchi] = useState("x86_64");

  
  const [selectedSupportTier, setSelectedSupportTier] = useState('');
  const [accountId,setAccountId]=useState([]);
  const [supportTierDescription, setSupportTierDescription] = useState('');
  const [selectedInstanceSource, setSelectedInstanceSource] = useState('');
  const [manualAmi, setManualAmi] = useState(false);
  const infoIcon = document.getElementById('info-icon');

  const [additionalInfo, setAdditionalInfo] = useState({
    _BusinessContact: '',
    map:'migSITGOMR8R2',
    _Environment: '',
    _Region: '',
    _NetworkLocation: 'INTERNAL',
    _BackupPlan: '',
    _TechnicalContact: '',
    _TechnicalContactMail: '',
    _ProvisionContact: '',
    _ProvisionEntity:'ABC',
    _ProvisionJustification:'',
    ProvisioningEngineer: ''
  });
  const [expandedSections, setExpandedSections] = useState({
    configuration: true,
    storage: true,
    network: true,
    tags: true,
    additionalInfo: true,
  });
  const regionMapping = {
    "us-east-1": "UE1",
    "eu-central-1": "EC1",
    "ap-south-1": "AS1",
    // Add more mappings
  };
  
  
  
  const osMapping = {
    "windows": "MS",
    "linux": "LX",
    
  };
  const [appCode, setAppCode] = useState(""); 
  useEffect(() => {
    // Log the current values for debugging
    console.log("Region from formData:", formData.Region);
    console.log("AZ from formData:", formData.az);
    console.log("Environment from formData:",formData._Environment);
    console.log("Platform:", platform);
    console.log("AppCode:", appCode);
  
    // Safely extract AZ code
    const azCode = formData.az ? formData.az.slice(-1).toUpperCase() : ""; // Default to empty string if undefined
    console.log("Extracted AZ Code:", azCode);
  
    const regionCode = regionMapping[formData.Region] || "";
    console.log("Mapped Region Code:", regionCode);
  
    const environmentCode = additionalInfo._Environment ?formData._Environment : "";
    console.log("Mapped Environment Code:", environmentCode);
  
    const osCode = osMapping[platform] || "";
    console.log("Mapped OS Code:", osCode);
  
    // Check if all values are valid before generating the instance name
    if (regionCode && azCode && environmentCode && appCode.length === 3) {
      const generatedName = `A${regionCode}${azCode}${environmentCode}${osCode}${appCode}`;
      console.log("Generated Instance Name:", generatedName);
  
      setFormData((prevData) => ({
        ...prevData,
        InstanceName: generatedName, // Save the instance name in formData
      }));
    } else {
      console.log("Instance Name not generated: Missing or invalid inputs.");
    }
    console.log("Environment from formData:",additionalInfo._Environment);
  }, [formData.Region, formData.az,formData._Environment,  platform, appCode]);
  // Toggle function for sections
  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };
  const handleAdditionalInfoChange = (e) => {
    const { name, value } = e.target;
    setAdditionalInfo(prevState => ({
        ...prevState,
        [name]: value
    }));
};


const handleBackupPlanChange = (e) => {
    const value = e.target.value;
    setAdditionalInfo(prevState => ({
        ...prevState,
        _BackupPlan: value
    }));
 
    let label = '';
    if (value === 'GOLD') {
        label = 'Back Up will Done Every Day';
    } else if (value === 'SILVER') {
        label = 'Back Up will be run Weekly';
    } else if (value === 'BRONZE') {
        label = 'Back Up will be run Monthly';
    } else if (value === 'PLATINUM') {
      label = 'Multiple Backups Every Day';
    } else if (value === 'N/A') {
      label = 'No Backup';
  }
    // console.log(backupPlanLabel);
    setBackupPlanLabel(label);
};
 
const handleEnvPlanChange = (e) => {
  const value = e.target.value;
  setAdditionalInfo(prevState => ({
      ...prevState,
      _Environment: value
  }));
  setFormData(prevState => ({
    ...prevState,
    _Environment: value
}));
  let label = '';
  if (value === 'production') {
      label = 'Resource will be deployed in Production';
  } else if (value === 'non-production') {
      label = 'Resource will be deployed in Non-Production';
  } else if (value === 'shared') {
      label = 'Resource will be deployed in Shared';
  }
  setEnvPlanLabel(label);
};
 
 
const handleBusinessAreaChange = (e) => {
  const selectedArea = e.target.value;
  setSelectedBusinessArea(selectedArea);
 
  // Reset selections
  //setSelectedBusinessSegment('');
  //setSelectedBusinessSegmentDescription('');
  const businessSegments = data1.businessSegmentPairs
      .filter(pair => pair.BusinessArea === selectedArea)
      .map(pair => ({
          segment: pair._BusinessSegment,
          description: pair._BusinessSegmentDescription
      }));
      // // console.log(businessSegments);
 
  // Update state with new business segments
  setBusinessSegments(businessSegments);
  const businessDescriptions = data1.businessSegmentPairs
      .filter(pair => pair.BusinessArea === selectedArea)
      .map(pair => ({
          segment: pair._BusinessSegment,
          description: pair._BusinessSegmentDescription
      }));
      // // console.log(businessSegments);
 
  // Update state with new business segments
  setbusinessDescriptions(businessDescriptions);
  const filteredCostCenters = data1.costCenterPairs
  .map(pair => ({
      center: pair.CostCenter,
      description: pair.Description
  }));
 
// Update state with new cost centers
setCostCenters(filteredCostCenters);
    //// console.log(filteredCostCenters);
};
 
 
const handleBusinessSegmentChange = (e) => {
    const selectedSegment = e.target.value;
    setSelectedBusinessSegment(selectedSegment);
    let description = '';
 
    // Find the segment data manually
    for (let i = 0; i < businessSegments.length; i++) {
        if (businessSegments[i].segment === selectedSegment) {
            description = businessSegments[i].description;
            break;
        }
    }
 
    // Set the description
    setSelectedBusinessSegmentDescription(description);
 
};
const handleBusinessSegmentDescriptionChange = (e) => {
  const selectedDescription = e.target.value;
  setSelectedBusinessSegmentDescription(selectedDescription);
  let segment;
 
  // Find the segment data manually
  for (let i = 0; i < businessSegments.length; i++) {
      if (businessSegments[i].description === selectedDescription) {
          segment = businessSegments[i].segment;
          break;
      }
  }
 
  // Set the description
  setSelectedBusinessSegment(segment);
   // console.log(segment);
};
 
const handleCostCenterChange = (e) => {
  const selectedCenter = e.target.value;
  setSelectedCostCenter(selectedCenter);
 
  const matchingPair =data1.costCenterPairs.find(pair => pair.CostCenter === (selectedCenter));
 
  setSelectedCostCenterDescription(matchingPair.Description);
  setSelectedFunctionArea(matchingPair.FunctionArea);
 
};
 
const handleCostCenterDescriptionChange = (e) => {
  const selectedCenterdescription = e.target.value;
  setSelectedCostCenterDescription(selectedCenterdescription);
 
 
  const matchingPair = data1.costCenterPairs.find(pair => pair.Description === selectedCenterdescription);
  setSelectedFunctionArea(matchingPair.FunctionArea);  setSelectedCostCenter(matchingPair.CostCenter);
};
useEffect(() => {
  async function checkAuth() {
    try {
      const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
      setUser(response.data.user);
      setFormData({ ...formData, ProvisioningEngineer: response.data.user.email });
    } catch (error) {
    
       // Set user to null in case of an error
    }
    
  }
  checkAuth();
},[navigate]);
useEffect(() => {
  let x=[];
  axios.get('https://umanage.dev.hidglobal.com/api/user')
    .then(response => {
      const fetchedData = response.data;
      // console.log(user);
      // console.log(fetchedData); 
       //console.log('Fetched user data:', fetchedData);
     // console.log(user);
      const userEntry = fetchedData.find(entry => entry.user === user.email);
      //console.log('User entry:', userEntry);

      if (userEntry) {
        const accountIds = userEntry.accounts.split(',').map(account => account.trim());
        //console.log(accountIds);
        x=accountIds;
        // console.log('Parsed account IDs:', accountIds);
        setAccountId(accountIds);
      } else {
        setAccountId([]);
      }
    })
    .catch(error => {
      // console.error('Error fetching user accounts:', error);
    });
    axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
    .then((response) => {
      const uniqueAccounts = Array.from(new Set(response.data.map(item => item.CreateAccountId)))
         .map(accountId => response.data.find(item => item.CreateAccountId === accountId));
         let fetchedData = response.data;
        
        fetchedData = uniqueAccounts.filter(item => x.includes(item.CreateAccountId));
           //console.log('Filtered S3 data:', fetchedData);
  
          //const uniqueAccounts = [...new Set(fetchedData.map(item => item.CreateAccountName))];
           //console.log('Unique account names:', uniqueAccounts);
      setAccounts(fetchedData); // Store the entire account object
      //console.log("account"+accounts);
    })
    .catch((error) => {
      // console.error('Error fetching accounts:', error);
    });
}, [user]);
   const [selectedAccount, setSelectedAccount] = useState('');
 const handleAccountChange = (value) => {
    setSelectedAccount(value);
  };
const handleSecurityGroupChange = (selectedOptions) => {
  setSelectedSecurityGroups(selectedOptions);
  setFormData({ ...formData, securityGroupIds: selectedOptions.map(option => option.value) });
};
// Load data from S3 on component mount
useEffect(() => {
  axios.get('https://umanage.dev.hidglobal.com/api/get-from-s3')
    .then((response) => {
      const allRegions = response.data.results;
      const allInstances = response.data.instances;

      // Filter instances based on the platform (e.g., "windows")
      

      setRegions(allRegions);  // Set all regions
      setInstances(allInstances);  // Set only instances matching the platform
      
    })
    .catch((error) => {
      // console.error("There was an error fetching the AMI data!", error);
    });
},[] );  // The platform is a dependency, so this re-runs if platform changes

useEffect(() => {
  axios.get('https://umanage.dev.hidglobal.com/api/ec2-price')
    .then((response) => {
        // Set only instances matching the platform
      setVCPUoptions(response.data.results);  // Set all regions
      setMemoryoptions(response.data.results);  // Set only instances matching the platform
      if (selectedVCPU || selectedMemory) {
        const filteredOptions = response.data.results.filter(option => {
          const matchesVCPU = selectedVCPU ? option.vCPU === selectedVCPU : true;
          const matchesMemory = selectedMemory ? option.memory === selectedMemory : true;
          return matchesVCPU && matchesMemory;
        });
        setCompatibleInstancesOptions(filteredOptions);
      } else {
        setCompatibleInstancesOptions(response.data.results);
      }
    })
    .catch((error) => {
      // console.error("There was an error fetching the AMI data!", error);
    });
},[] );  // The platform is a dependency, so this re-runs if platform changes

// Handle region change and filter AMIs based on the region
const handleRegionChange = (e) => {
  const selectedRegion = e.target.value.toLowerCase();
  setFormData({ ...formData, Region: selectedRegion });

  // Filter AMIs for the selected region
  const filtered = regions.filter(inst =>
    inst.Region.toLowerCase() === selectedRegion &&
    inst.Platform.toLowerCase() === platform
  );

  // console.log("Filtered AMIs for selected region:", filtered);
  setFilteredAMIs(filtered);  // Update the filtered AMIs based on the region
};
const handleInstanceNameChange = (e) => {
  setFormData({ ...formData, InstanceName: e.target.value });
};
// Handle architecture change and further filter AMIs based on architecture
const handleArchitectureChange = (e) => {
  const selectedArchitecture = e.target.value;
  setFormData({ ...formData, Architecture: selectedArchitecture });

  // Filter AMIs based on both the selected region and architecture
  const filtered = regions
    .filter(ami => ami.Region.toLowerCase() === formData.Region && ami.Architecture === archi &&
    ami.Platform.toLowerCase() === platform );

  // console.log("Filtered AMIs for selected architecture:", filtered);
  setFilteredAMIs(filtered);  // Update filtered AMIs based on architecture
};

// Handle AMI selection and find compatible instances
const handleAMIChange = (e) => {
  const selectedAMI = e.target.value;
  const amiData = instances.find(amiObject => amiObject.ami && amiObject.ami === selectedAMI);

  // console.log('Selected AMI:', selectedAMI);
  // console.log('AMI Data:', amiData);

  setFormData({ ...formData, AMI: selectedAMI });

  // Check if amiData exists and has compatible instances
  if (amiData && amiData.instance && amiData.instance.length > 0) {
    const compatibleInstancesArray = amiData.instance;
    // console.log('Compatible Instances Array:', compatibleInstancesArray);
    setCompatibleInstances(compatibleInstancesArray);  // Set compatible instances based on selected AMI
  } else {
    setCompatibleInstances([]);  // Clear if no compatible instances are found
    // console.log('No Compatible Instances found.');
  }
};

  const handleCustomAMIInstanceTypeChange = (e) => {
    // console.log(e.target.value );
     setFormData({ ...formData, InstanceType: e.target.value });
   };
 
  // Function to handle Compatible Instance selection
  const handleInstanceTypeChange = (selectedOption) => {
   // console.log(selectedOption);
    setFormData({ ...formData, InstanceType: selectedOption.value });
  };
 
  // Map compatibleInstances to options
  const compatibleInstanceOptions = compatibleInstances.map((instance, index) => ({
    value: instance,
    label: instance,
  }));
 
  const handleUseIAMRoleChange = (e) => {
    setFormData({
      ...formData,
      CreateIAMRole: e.target.value,  // Update the selection
    });
  };

  const handleIAMRoleNameChange = (e) => {
    setFormData({ ...formData, IAMRoleName: e.target.value });
  };
  // const [user, setUser] = useState(null);
    
  

  useEffect(() => {
    if (formData.Region && formData.accountId) {
      axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
        .then((response) => {
          // console.log('Original VPC Data:', response.data);
 
          // Filter VPCs based on Region and AccountId
          const filteredVPCs = response.data.filter(vpc => {
            return (
              vpc.VpcRegion.toLowerCase() === formData.Region.toLowerCase() &&
              vpc.CreateAccountId.toLowerCase() === formData.accountId.toLowerCase()
            );
          });
 
          // Create a map to store unique VPCs by VpcId
          const uniqueVPCsMap = {};
 
          filteredVPCs.forEach(vpc => {
            // Using VpcId as the key for uniqueness
            uniqueVPCsMap[vpc.VpcId] = vpc;
          });
 
          // Convert the map values back to an array
          const uniqueVPCs = Object.values(uniqueVPCsMap);
 
          // console.log('Filtered Unique VPCs:', uniqueVPCs);
          setVpcs(uniqueVPCs);
        })
        .catch((error) => {
          // console.error('Error fetching VPCs:', error);
        });
    }
  }, [formData.Region, formData.accountId]);
 
 
  // Handle VPC selection and fetch unique subnets
  const handleVpcChange = (selectedVpcId) => {
    setFormData({ ...formData, vpcId: selectedVpcId });
 
    // Fetch subnets based on the selected VPC
    axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
      .then((response) => {
        const filteredSubnets = response.data.filter(item => item.VpcId === selectedVpcId);
        const filterCIDR = response.data.find(item => item.VpcId === selectedVpcId);
        setFormData({
          ...formData,
          CIDR:filterCIDR.CIDR,
        });
        // Use a Set to ensure uniqueness by SubnetId
        const uniqueSubnets = Array.from(new Set(filteredSubnets.map(subnet => subnet.SubnetId)))
          .map(id => filteredSubnets.find(subnet => subnet.SubnetId === id));
 
        setSubnets(uniqueSubnets); // Store the unique subnets
      })
      .catch((error) => {
        // console.error('Error fetching subnets:', error);
      });
  };
 
  
  // Handle Subnet selection and fetch security groups
  const handleSubnetChange = (selectedSubnetId) => {
    setFormData({ ...formData, subnetId: selectedSubnetId });
    // Update form data with selected subnet
  console.log(formData);
    // Fetch security groups based on the selected subnet
    axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
    .then((response) => {
      

       
      const filteredSecuritygroup = response.data.filter(item => item.SubnetId === selectedSubnetId);
      const filterAz = response.data.find(item => item.SubnetId === selectedSubnetId);
      
        // Update formData with AZ and Available IPs
        setFormData({
          ...formData,
          subnetId:selectedSubnetId,
          az: filterAz.AZ,
          availableIps: filterAz.AvailableIPs,
        });
       
      
      const handlemapmigratedChange = (selectedSubnetId) => {
        setFormData({ ...formData, subnetId: selectedSubnetId }); }
      // Use a Set to ensure uniqueness by SubnetId
      const uniqueSecurityGroup = Array.from(new Set(filteredSecuritygroup.map(sec => sec.SecurityGroupId)))
        .map(id => filteredSecuritygroup.find(sec => sec.SecurityGroupId === id));
      
      setSecurityGroups(uniqueSecurityGroup); // Store the unique subnets
      
      // Automatically select the first subnet if available
      // if (uniqueSecurityGroup.length > 0) {
      //   const firstSubnetId = uniqueSecurityGroup[0].SecurityGroupId;
      //   setFormData(prev => ({ ...prev, subnetId: firstSubnetId })); // Update form data with selected subnet
      //   handleSubnetChange(firstSubnetId); // Fetch security groups for the first subnet
      // }
    })
    .catch((error) => {
      // console.error('Error fetching subnets:', error);
    });
  };
  
 
  const accountOptions = accounts.map(account => ({
    value: account.CreateAccountId,
    label: account.CreateAccountName,
  }));
 
  // Find the currently selected account in options for the value prop
  
 
  const vpcOptions = vpcs.map(vpc => ({
    label: vpc.VpcName,
    value: vpc.VpcId
  }));
 
 
  // Map Subnets to react-select options
  const subnetOptions = subnets.map((subnet) => ({
    value: subnet.SubnetId,
    label: `${subnet.SubnetName} - ${subnet.SubnetId}`, // Customize display text as needed
  }));
 
  const businessSegmentOptions = businessSegments.map(({ segment }) => ({
    value: segment,
    label: segment,
  }));
 
  // Map Business Segment Descriptions to react-select options
  const businessDescriptionOptions = businessDescriptions.map(({ description }) => ({
    value: description,
    label: description,
  }));
 
  const costCenterOptions = costCenters.map(({ center }) => ({
    value: center,
    label: center,
  }));
 
  // Map Cost Center Descriptions to react-select options
  const costCenterDescriptionOptions = costCenters.map(({ description }) => ({
    value: description,
    label: description,
  }));
 
  const instanceSourceOptions = systemTags.instanceSources.map((source) => ({
    value: source,
    label: source,
  }));
 
 
  useEffect(() => {
   
 
    const fetchData = async () => {
      try {
        const response1 = await fetch('https://umanage.dev.hidglobal.com/api/tags', {
          method: 'GET',
        });
        const data1 = await response1.json();
        // console.log(data1);
       
       
        setData1({
          uniqueBusinessAreas: data1.uniqueBusinessAreas || [],
          businessSegmentPairs: data1.businessSegmentPairs || {},
          costCenterPairs: data1.costCenterPairs || {},
        });
        // console.log(data1);
 
       
 
      } catch (error) {
        // console.error('Error fetching data:', error);
      }
    };
 
    fetchData();
 
  }, [step]);
 
  // Handle changes for form inputs
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
 
  // Progress bar width calculation
  const calculateProgressBarWidth = () => `${(step - 1) * 25}%`;
 
  const goToNextStep = () => {
    if (step < 5) setStep(step + 1);
  };
 
  const goToPreviousStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const goToStep = (step) => {
    setStep(step); // Assuming you have a state like currentStep
  };
  
  
  
const defaultDeviceNames = ['/dev/sda1', 'xvdb', 'xvdc', 'xvdd', 'xdve'];
  
  const addVolume = () => {
    if (formData.volumeCount < 5) {
      const newVolumeKey = `volume${formData.volumeCount + 1}`; // Create a new volume key
      const defaultDeviceName = defaultDeviceNames[formData.volumeCount]; // Get the default device name based on volume count
  
      setFormData((prevData) => ({
        ...prevData,
        [newVolumeKey]: { ebsVolumeSize: '1', ebsVolumeType: 'gp3', DeviceName: defaultDeviceName }, // Set default device name
        volumeCount: prevData.volumeCount + 1,
      }));
    }
  };
  
  const handleVolumeChange = (e, volumeNumber) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [`volume${volumeNumber}`]: {
        ...prevData[`volume${volumeNumber}`],
        [name]: value,
      },
    }));
  };
  
  const removeVolume = (volumeNumber) => {
    const newData = { ...formData };
    delete newData[`volume${volumeNumber}`];
  
    for (let i = volumeNumber + 1; i <= formData.volumeCount; i++) {
      newData[`volume${i - 1}`] = newData[`volume${i}`];
      delete newData[`volume${i}`];
    }
  
    setFormData({ ...newData, volumeCount: Math.max(volumeNumber - 1, 1) });
    if (selectedVolume >= volumeNumber) {
      setSelectedVolume((prevSelected) => Math.max(prevSelected - 1, 1)); // Set to the previous volume or 1
    }
  };
  
  const [selectedVolume, setSelectedVolume] = useState(1);

  const handleSubmit = async (e) => {
    setIsProcessing(true);
    console.log("isProcessing"+isProcessing);
    setAlertMessage("Awaiting Process Completion !");
    // console.warn(formData);
    
  
    // console.log("formdata"+formData);
    // console.log(additionalInfo);
    const updatedFormData = {
      ...formData,
      SelectedAccount:selectedAccount,
      CostCenter: selectedCostCenter,
  CostCenterDescription: selectedCostCenterDescription,
  BusinessArea: selectedBusinessArea,
  SupportTier: selectedSupportTier,
  SupportTierDescription:supportTierDescription ,
  InstanceSource: selectedInstanceSource,
  mapMigrated:additionalInfo.map,
 BusinessContactEmail: email,
  FunctionalArea: selectedFunctionArea,
  BusinessSegment: selectedBusinessSegment,
  BusinessSegmentDescription: selectedBusinessSegmentDescription,
      BusinessContact: additionalInfo._BusinessContact,
      Environment: additionalInfo._Environment,
      NetworkLocation: additionalInfo._NetworkLocation,
      BackupPlan: additionalInfo._BackupPlan,
      TechnicalContact: additionalInfo._TechnicalContact,
      TechnicalContactEmail: additionalInfo._TechnicalContactMail,
      ProvisioningEntity: additionalInfo._ProvisionEntity,
      ProvisioningJustification: additionalInfo._ProvisionJustification,
      from:'without',
      platform:platform 
    };

    let hasEmptyValues = false;
  console.log(updatedFormData);

// Loop through updatedFormData and check for empty values, ignoring specified keys

  if ( updatedFormData.CostCenter === '' ||
    updatedFormData.CostCenterDescription === '' ||
    updatedFormData.BusinessArea === '' ||
    updatedFormData.SupportTier === '' ||
    updatedFormData.InstanceType === '' ||
    updatedFormData.BusinessContactEmail === '' ||
    updatedFormData.FunctionalArea === '' ||

    updatedFormData.BusinessSegment === '' ||
    updatedFormData.BusinessContact === '' ||
    updatedFormData.Environment === '' ||
    
    updatedFormData.BackupPlan === '' ) {
    
      
      hasEmptyValues = true;
      // Exit the loop if any non-ignored value is empty
    
  }


if (hasEmptyValues) {
  // console.log(updatedFormData.securityGroupIds);
  // console.log(updatedFormData.InstanceName);
  setMessagestatus(false);
  setMessage('Error: Please fill in all required fields . please refresh the page ');
  return; // Exit early if there are empty fields
}



// Stream the response in chunks

    
    try {
      const response = await fetch('/api/accountform', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedFormData),
      });
      const reader = response.body.getReader();
const decoder = new TextDecoder();
let receivedText = "";

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  receivedText = decoder.decode(value, { stream: true });
  console.log(receivedText);
  setAlertMessage(receivedText); // Append new chunks to message
  if(receivedText.substring(0,4)=="Succ")
    { 
      setAlertMessage("");
      
      setMessagestatus(true);
    }
  if(receivedText.substring(0,5)=="Error"){
    setAlertMessage("");
    //setMessage(data.message);
    setMessagestatus(false);
  }

}
     
    } catch (err) {
      setMessage(`Error: ${err.message}`);
    }
  };
  useEffect(() => {
    console.log("isProcessing changed:", isProcessing);
  }, [isProcessing]);
  const handleToggleChange = (e) => {
    setManualAmi(e.target.value === 'yes');
    // Reset AMI when switching between input and dropdown
    setFormData({ ...formData, AMI: '' });
  };
  console.log("Status"+messagestatus);
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/tags')
      .then(response => {
        const { systemTags } = response.data;
        setSystemTags(systemTags);
      })
      .catch(error => {
        // console.error("There was an error fetching system tags!", error);
      });
  }, []);
 
  // Handle SupportTier change
  const handleSupportTierChange = (e) => {
    const selectedTier = e.target.value;
    setSelectedSupportTier(selectedTier);
 
    // Find and set the description based on the selected SupportTier
    const tierPair = systemTags.supportTierPairs.find(pair => pair._SupportTier === selectedTier);
    if (tierPair) {
      setSupportTierDescription(tierPair._SupportTierDescription);
    } else {
      setSupportTierDescription('');
    }
  };
 
  const [selectedAAO, setSelectedAAO] = useState('');

  const handleAAOChange = (value) => {
    setSelectedAAO(value);
  };

  const [selectedAAU, setSelectedAAU] = useState('');

  const handleAAUChange = (value) => {
    setSelectedAAU(value);
  };

  const [showBackupTooltip, setshowBackupTooltip] = useState(false); 
  const useCaseOptions = [
  {
    useCase: "Default (Segmented)",
    description: "Default configuration Fully compliant Full access"
  },
  {
    useCase: "Isolated w/Internet (https)",
    description: "Non-compliant Internet access (https) No internal access"
  },
  {
    useCase: "Isolated w/Internet (https + exceptions)",
    description: "Non-compliant Internet access (https + exceptions) No internal access"
  },
  {
    useCase: "Isolated",
    description: "Non-compliant Completely isolated No internal/external access"
  },
  {
    useCase: "DMZ w/Inbound NAT",
    description: "Compliant DMZ isolated with inbound port forwarding All access requires documentation / exceptions"
  },
  {
    useCase: "Isolated on-prem -> Isolated in-cloud",
    description: "Non-compliant No access internally or externally VPN connects VLAN to VPC"
  }
];

const handleUseCaseChange = (e) => {
  const selected = e.target.value;
  const found = useCaseOptions.find(item => item.useCase === selected);
  setFormData({
    ...formData,
    UseCase: selected,
    UseCaseDescription: found ? found.description : ''
  });
};

const [showPopup, setShowPopup] = useState(false); // State to toggle popup visibility

  const togglePopup = () => {
    setShowPopup(!showPopup); // Toggle popup visibility
  };
 
  return (
    <div className="create-class">
     <Navbar />

      <div className="create-instance">
      <div className="form-container">
             
      <div className="form-section business-info">
  <h5><strong>Account Details</strong></h5>
  <div className="row">
    <div className="col-md-6 form-group">
      <label htmlFor="accountName">Account Name <span className="required">*</span></label>
      <p className="form-text text-muted">
        Format: &lt;BA&gt;&lt;GROUP&gt;&lt;PRODUCT or SERVICE&gt;<br />
        <em>Example: PACS Partner Services</em>
      </p>
      <input
        id="accountName"
        type="text"
        className="form-control"
        value={accountName}
        onChange={e => setAccountName(e.target.value)}
        placeholder="e.g. PACS Partner Services"
        required
      />
    </div>
    <div className="col-md-6 form-group">
      <label htmlFor="aaoApprover">AAO Approver <span className="required">*</span></label>
      <p className="form-text text-muted">
        A L3/L4 person from PACS leadership who will be approving the AWS Account Owner role.
      </p>
      <input
        id="aaoApprover"
        type="text"
        className="form-control"
        value={aaoApprover}
        onChange={e => setAAOApprover(e.target.value)}
        placeholder="Approver Name"
        required
      />
    </div>
  </div>
  <div className="row">
    <div className="col-md-6 form-group">
      <label htmlFor="aao">AAO <span className="required">*</span></label>
      <p className="form-text text-muted">
        A Manager/Budget owner who will be AWS account Owner – Typically this is the DIRECTOR role in the group.
      </p>
      <input
        id="aao"
        type="text"
        className="form-control"
        value={selectedAAO}
        onChange={e => handleAAOChange(e.target.value)}
        placeholder="AAO Name"
        required
      />
    </div>
    <div className="col-md-6 form-group">
      <label htmlFor="aau">AAU <span className="required">*</span></label>
      <p className="form-text text-muted">
        Individuals who will be using the AWS account to perform provisioning, start/stop, etc. This could be yourself.
      </p>
      <input
        id="aau"
        type="text"
        className="form-control"
        value={selectedAAU}
        onChange={e => handleAAUChange(e.target.value)}
        placeholder="AAU Name(s)"
        required
      />
    </div>
  </div>
</div>
 
  <div className="form-section business-info">
    <h5><strong>Business Tags</strong></h5>
    <div className="row">
      <div className="col-md- form-group">
      <label htmlFor="businessArea">Business Area <span className="required">*</span></label>
      <p className="form-text text-muted">
          Select the business area that corresponds to this project.
        </p>
        <select
          id="businessArea"
          value={selectedBusinessArea}
          className="form-control"
          onChange={handleBusinessAreaChange}
        >
          <option value="">Select Business Area</option>
          {data1.uniqueBusinessAreas.map(area => (
            <option key={area} value={area}>{area}</option>
          ))}
        </select>
      </div>
    </div>
 
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="businessSegment">Business Segment <span className="required">*</span></label>
        <p className="form-text text-muted">
          Choose the relevant business segment for this task.
        </p>
        <Select
        id="businessSegment"
        className="re-select"
        value={businessSegmentOptions.find(option => option.value === selectedBusinessSegment)} // Find the selected segment
        onChange={(selectedOption) => handleBusinessSegmentChange({ target: { value: selectedOption.value }})} // Handle change
        options={businessSegmentOptions}
        isSearchable={true}
        placeholder="Search or select Business Segment"
       
      />
      </div>
 
      <div className="col-md-6 form-group">
        <label htmlFor="businessSegmentDescription">Business Segment Description <span className="required">*</span></label>
        <p className="form-text text-muted">
          Provide a description of the selected business segment.
        </p>
        <Select
        id="businessSegmentDescription"
        className="re-select"
        value={businessDescriptionOptions.find(option => option.value === selectedBusinessSegmentDescription)} // Find the selected description
        onChange={(selectedOption) => handleBusinessSegmentDescriptionChange({ target: { value: selectedOption.value }})} // Handle change
        options={businessDescriptionOptions}
        isSearchable={true}
        placeholder="Search or select Business Segment Description"
       
      />
      </div>
      </div>    
         
  </div>
 
  {/* Cost Center Section */}
  <div className="form-section cost-center-info">
  <h5><strong>Cost Tags</strong></h5>
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="costCenter">Cost Center <span className="required">*</span></label>
        <p className="form-text text-muted">
          Select the cost center related to this project.
        </p>
        <Select
        id="costCenter"
        className="re-select"
        value={costCenterOptions.find(option => option.value === selectedCostCenter)} // Find the selected cost center
        onChange={(selectedOption) => handleCostCenterChange({ target: { value: selectedOption.value }})} // Handle change
        options={costCenterOptions}
        isSearchable={true}
        placeholder="Search or select Cost Center"
       
      />
      </div>
 
      <div className="col-md-6 form-group">
        <label htmlFor="costCenterDescription">Cost Center Description <span className="required">*</span></label>
        <p className="form-text text-muted">
          Provide a description for the selected cost center.
        </p>
        <Select
        id="costCenterDescription"
        className="re-select"
        value={costCenterDescriptionOptions.find(option => option.value === selectedCostCenterDescription)} // Find the selected cost center description
        onChange={(selectedOption) => handleCostCenterDescriptionChange({ target: { value: selectedOption.value }})} // Handle change
        options={costCenterDescriptionOptions}
        isSearchable={true}
        placeholder="Search or select Cost Center Description"
       
      />
      </div>
    </div>
 
    {/* <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="functionArea">Function Area <span className="required">*</span></label>
        <p className="form-text text-muted">
          The functional area for the selected cost center.
        </p>
        <input
          type="text"
          id="functionArea"
          className="form-control"
          value={selectedFunctionArea}
          readOnly
        />
      </div>
    </div> */}
  </div>
 
  {/* Region & Environment Section */}
  <div className="form-section region-environment-info">
  <h5><strong>System Tags</strong></h5>
    <div className="row">
     
 
      <div className="col-md-6 form-group">
        <label htmlFor="environment" className="break">Environment <span className="required">*</span></label>
        <p className="form-text text-muted">
          Choose the environment for this deployment.
        </p>
        <select
          id="environment"
          name="_Environment"
          value={additionalInfo._Environment}
          className="form-control"
          onChange={handleEnvPlanChange}
        >
          <option value="">Select Environment</option>
          <option value="SBX">Sandbox</option>
          <option value="DEV">Development</option>
          <option value="SUS">Sustaining</option>
          <option value="SUP">Support</option>
          <option value="AQ1">Acquisitions 1</option>
          <option value="AQ2">Acquisitions 2</option>
          <option value="CIN">Customer Integration</option>
          <option value="UAT">User Acceptance Testing</option>
          <option value="LIV">Live (Production)</option>

        </select>
        {envPlanLabel && <span className="backup-plan-label">{envPlanLabel}</span>}
      </div>
 
      <div className="col-md-6 form-group">
      <label htmlFor="backupPlan" className="label-with-icon">
        Backup Plan <span className="required">*</span>
        
      </label>
      <p className="form-text text-muted">
        Select a backup plan for data protection. <a href="#" onClick={togglePopup}>Know more</a>
      </p>

      {/* Popup Content */}
      {showPopup && (
        <div className="popup-overlay">
          <div className="popup-content">
          <button className="btn btn-close" onClick={togglePopup}></button>
            <h4>What are the Backups SLA details? </h4>
            <p>
              Below is the SLA frequency and retention policy for different service tiers:
            </p>
            <table border="1" cellpadding="8" cellspacing="0" style={{ borderCollapse: 'collapse', width: '100%' }}>
              <thead>
                <tr>
                  <th>Backupplan Name</th>
                  <th>Frequency</th>
                  <th>Retention</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Platinum</td>
                  <td>
                    Daily (4 times/day)<br />
                    Monthly (1 time/month)<br />
                    Yearly (1 time/year)
                  </td>
                  <td>
                    10 days<br />
                    12 months<br />
                    3 years
                  </td>
                </tr>
                <tr>
                  <td>Gold</td>
                  <td>
                    Daily (1 time/day)<br />
                    Weekly (every week)<br />
                    Monthly (1 time/month)
                  </td>
                  <td>
                    6 days<br />
                    1 month<br />
                    2 months
                  </td>
                </tr>
                <tr>
                  <td>Silver</td>
                  <td>
                    Daily (1 time/day)<br />
                    Weekly (every week)
                  </td>
                  <td>
                    6 days<br />
                    2 weeks
                  </td>
                </tr>
                <tr>
                  <td>Bronze</td>
                  <td>
                    Weekly (every week)
                  </td>
                  <td>
                    2 weeks
                  </td>
                </tr>
              </tbody>
            </table>
            
          </div>
        </div>
      )}
        <select
          id="backupPlan"
          name="_BackupPlan"
          value={additionalInfo._BackupPlan}
          className="form-control"
          onChange={handleBackupPlanChange}
        >
          <option value="">Select Backup Plan</option>
          <option value="PLATINUM">PLATINUM</option>
          <option value="GOLD">GOLD</option>
          <option value="SILVER">SILVER</option>
          <option value="BRONZE">BRONZE</option>
          <option value="N/A">N/A</option>
        </select>
       
      </div>
 
    </div>
   
   
 
    {/* <div className="row">
     
      <div className="col-md-6 form-group">
        <label htmlFor="location">Network Location <span className="required">*</span></label>
        <p className="form-text text-muted">
          Specify the network location for this instance.
        </p>
        <select
          id="location"
          name="_NetworkLocation"
          value={additionalInfo._NetworkLocation}
          className="form-control"
          onChange={handleAdditionalInfoChange}
        >
          <option value="">Select Network Location</option>
          <option value="INTERNAL">INTERNAL</option>
          <option value="EXTERNAL">EXTERNAL</option>
          <option value="ISOLATED">ISOLATED</option>
        </select>
      </div>
      <div className="col-md-6 form-group">
      <label>Instance Source:</label>
      <p className="form-text text-muted">
          Choose the source of the instance.
        </p>
      
    <input
            type="text"
            id="functionArea"
            className="form-control"
            value={"NEW"}
            readOnly
          />
      </div>
    </div> */}
    <div className="row">
      <div className="col-md-6 form-group">
      <label>Support Tier <span className="required">*</span></label>
      <p className="form-text text-muted">
          Choose the appropriate support tier for this system.
        </p>
        <select value={selectedSupportTier} onChange={handleSupportTierChange} className="form-control">
          <option value="">Select Support Tier</option>
          {systemTags.supportTiers.map((tier, index) => (
            <option key={index} value={tier}>{tier}</option>
          ))}
        </select>
      </div>
 
      <div className="col-md-6 form-group">
     
        <div>
          <label htmlFor="functionArea">Support Tier Description</label>
          <p className="form-text text-muted">
          Description of the selected support tier.
        </p>
          <input
            type="text"
            id="functionArea"
            className="form-control"
            value={supportTierDescription}
            readOnly
          />
         
        </div>
     
      </div>
    </div>   
    {/* <div className="row">
     
 
     <div className="col-md-6 form-group">
       <label htmlFor="mapmigrated" className="break">Map-Migrated <span className="required">*</span></label>
       <p className="form-text text-muted">
         Choose the map migration for this deployment.
       </p>
       <input
          type="text"
          id="mapmigrated"
          name="map"
          value="migSITGOMR8R2"
          className="form-control"
          readOnly
    />
       
     </div>
     </div>    */}
   
  </div>
 
  {/* Contact Information Section */}
  <div className="form-section contact-info">
  <h5><strong>Contact Tags</strong></h5>
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="businessContact">Business Contact</label>
        <p className="form-text text-muted">
          Enter the name of the business contact for this request.
        </p>
        <input
          id="businessContact"
          name="_BusinessContact"
          type="text"
          className="form-control"
          value={additionalInfo._BusinessContact}
          onChange={handleAdditionalInfoChange}
          placeholder="Ex : Prasana Srinivasan"
        />
      </div>
 
      <div className="col-md-6 form-group">
        <label htmlFor="BusinessContactMail">Business Contact Email</label>
        <p className="form-text text-muted">
          Enter the email of the business contact.
        </p>
        <input
          id="BusinessContactMail"
          name="_BusinessContactEmail"
          type="text"
          value={additionalInfo._BusinessContactEmail}
          className="form-control"
          onChange={handleAdditionalInfoChange}
          required
          placeholder="Ex : <EMAIL>"
        />
      </div>
    </div>
 
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="TechnicalContact">Technical Contact</label>
        <p className="form-text text-muted">
        Enter the name of the technical contact for this request.
        </p>
        <input
          id="TechnicalContact"
          name="_TechnicalContact"
          type="text"
          className="form-control"
          value={additionalInfo._TechnicalContact}
          onChange={handleAdditionalInfoChange}
          placeholder="Ex : Abhiram Pabbisetty"
        />
      </div>
 
      <div className="col-md-6 form-group">
        <label htmlFor="TechnicalContactMail">Technical Contact Mail</label>
        <p className="form-text text-muted">
          Enter the email of the technical contact.
        </p>
        <input
          id="TechnicalContactMail"
          name="_TechnicalContactMail"
          type="text"
          className="form-control"
          value={additionalInfo._TechnicalContactMail}
          onChange={handleAdditionalInfoChange}
          placeholder="Ex : <EMAIL>"
        />
      </div>
    </div>
  </div>
 
   
  </div>


        
        
      </div>
     
    </div>
  );
};
 
export default Accountform;


