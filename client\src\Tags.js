import React, { useState } from 'react';

const TagUpdater = () => {
  const [formData, setFormData] = useState({
    accountId: '************',
    region: 'us-east-1',
    instanceId: '',
    firstname: 'dundivenkatanagasaikondalaabhiram',
    tags: [
      { Key: 'Cost Center', Value: '4310' },
      { Key: 'Cost Center Description', Value: 'Engineering Printers' },
      { Key: 'Support Tier', Value: 'TIER2' },
      { Key: 'Support Tier Description', Value: 'SLA: P2 *' },
      { Key: 'Business Area', Value: 'SI' },
      { Key: 'Business Segment', Value: '1050' },
      { Key: 'Business Segment Description', Value: 'Desktop' },
      { Key: 'Environment', Value: 'DEV' },
      { Key: 'Network Location', Value: 'INTERNAL' },
      { Key: 'Functional Area', Value: 'R and D' },
      { Key: 'Backup Plan', Value: 'SILVER' },
    ],
    
      email: '<EMAIL>',
     
      accountId: '************',
  });

  const handleTagChange = (index, field, value) => {
    const updatedTags = [...formData.tags];
    updatedTags[index][field] = value;
    setFormData({ ...formData, tags: updatedTags });
  };

  const handleSubmit = async () => {
    try {
      const res = await fetch('https://umanage.dev.hidglobal.com/api/s3/xcvsx', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await res.json();
      alert(data.message || 'Tag update submitted!');
    } catch (err) {
      console.error('Error:', err);
      alert('Failed to update tags.');
    }
  };

  return (
    <div>
      <h2>Update EC2 Tags</h2>
      <input
        placeholder="Instance ID"
        value={formData.instanceId}
        onChange={(e) => setFormData({ ...formData, instanceId: e.target.value })}
      />
      <input
        placeholder="Account ID"
        value={formData.accountId}
        onChange={(e) => setFormData({ ...formData, accountId: e.target.value })}
      />
      <input
        placeholder="Your First Name"
        value={formData.firstname}
        onChange={(e) => setFormData({ ...formData, firstname: e.target.value })}
      />

      <h3>Editable Tags</h3>
      {formData.tags.map((tag, index) => (
        <div key={index}>
          <input
            value={tag.Key}
            readOnly
          />
          <input
            value={tag.Value}
            onChange={(e) => handleTagChange(index, 'Value', e.target.value)}
          />
        </div>
      ))}

      <button onClick={handleSubmit}>Submit Tag Update</button>
    </div>
  );
};

export default TagUpdater;
