import React, { useState, useEffect } from 'react';
import <PERSON> from 'papapar<PERSON>';
import accountData from '../assets/csv/Account.csv'; // Adjust the path to your CSV file
import './AboutAccount.css';
const AccountMap = () => {
  const [data, setData] = useState([]);

  useEffect(() => {
    // Load and parse CSV data
    Papa.parse(accountData, {
      header: true,
      download: true,
      complete: (result) => {
        setData(result.data);
      },
    });
  }, []);

  return (
    <div className="account-map">
      <h2 className="account-map-title">Account Map</h2>
      
      <table className="account-map-table">
        <thead>
          <tr>
            <th className="table-header">Account Id</th>
            <th className="table-header">Account Name</th>
          </tr>
        </thead>
        <tbody>
          {data.map((row, index) => (
            <tr key={index}>
              <td className="table-data">{row['Account Id']}</td>
              <td className="table-data">{row['Account Name']}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default AccountMap;
