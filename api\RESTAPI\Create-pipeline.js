const crypto = require('crypto');
const express = require('express');
const router = express.Router();
const ENCRYPTION_KEY = crypto.createHash('sha256').update('umanage').digest(); // 32 bytes for AES-256
const IV_LENGTH = 16; // AES block size

const AWS = require('aws-sdk');
const nodemailer = require('nodemailer');
const csvParser = require('csv-parser');
const axios = require('axios');
function decrypt(encryptedText) {
  const parts = encryptedText.split(':');
  const iv = Buffer.from(parts[0], 'base64');
  const encrypted = parts[1];
  const decipher = crypto.createDecipheriv('aes-256-cbc', ENCRYPTION_KEY, iv);
  let decrypted = decipher.update(encrypted, 'base64', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

// Example usage: decrypt and extract email, date, and time
function decryptAndExtract(token) {
  const decrypted = decrypt(token);
  // decrypted format: email|YYYYMMDDHHmmss|umanage
  const [email, datetime, umanageFlag] = decrypted.split('|');
  if (umanageFlag !== 'umanage') {
    throw new Error('Invalid token: missing umanage flag');
  }
  const year = datetime.slice(0, 4);
  const month = datetime.slice(4, 6);
  const day = datetime.slice(6, 8);
  const hour = datetime.slice(8, 10);
  const minute = datetime.slice(10, 12);
  const second = datetime.slice(12, 14);
  return {
    email,
    date: `${year}-${month}-${day}`,
    time: `${hour}:${minute}:${second}`,
    umanage: true
  };
}
 
module.exports = (s3,Readable,generateTicketNumber,) => {
   let originalCredentials = AWS.config.credentials;
// POST route to decrypt token and return email, date, and time
router.post('/decrypt-token', (req, res) => {
  const { token } = req.body;
  console.log('Received token:', token);
   const ip = req.headers['x-forwarded-for'] || req.connection.remoteAddress || req.socket.remoteAddress || (req.connection.socket ? req.connection.socket.remoteAddress : null);
  // If IPv6 format, extract IPv4
console.log(ip);
  if (!token) {
    return res.status(400).json({ error: 'Token is required' });
  }
  try {
    const result = decryptAndExtract(token);
    res.status(200).json(result);
  } catch (error) {
    res.status(400).json({ error: 'Invalid or malformed token' });
  }
});
const assumeRole = async (accountId,firstname) => {
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    console.log(firstname);
    console.log(params);
    try {
      const data = await sts.assumeRole(params).promise();
      return data.Credentials;
    } catch (error) {
      AWS.config.update({ credentials: null });
        console.error('Error assuming role:', error.code, error.message);
        if (error.code === 'AccessDenied') {
          const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
            // Handle AccessDenied specifically, maybe log the accounts involved
       
     
    }else{
      console.error('Error assuming role:', error);
      throw error;
    }}
  };
 
  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };
 
  // Function to configure AWS services (SSM, S3, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      cloudFormation: new AWS.CloudFormation()
    };
  };
 
 
  const handleSSMRequest = async (req, res, documentName) => {
    const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} =  req.body;
 console.log("i am in new ssm");
    let firstname= req.body.firstname;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');

    const sendUpdate = (message) => {
      res.write(`${message }\n\n`);
    };
// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
    if (!instanceId || !region || !accountId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
 let params;
 if (documentName === 'AWS-ResizeInstance') {
  params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId],
      InstanceType: [req.body.instanceType], // assuming you have a variable `instanceType`
    }
  };
} else {
  params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId],
      // Add other parameters required by your SSM document here
    }
  };
}
 console.log(params);
 console.log(req.body);
    try {
      // AWS.config.update({ credentials: originalCredentials });
      if(accountId!=************){
      const credentials = await assumeRole(accountId,y);
      await initializeAWS(credentials, region);}else{AWS.config.update({
        region: region
      });}
      const { ssm } = configureAWS();
 
      const ticketNumber = generateTicketNumber();
      let executionID;
      let startTime = new Date();
      
  const day = String(startTime.getDate()).padStart(2, '0');
  const month = String(startTime.getMonth() + 1).padStart(2, '0');
  const year = startTime.getFullYear();

   let qdate=`${day}-${month}-${year}`;
   console.log(qdate);
      const data = await ssm.startAutomationExecution(params).promise();
      executionID = data.AutomationExecutionId;
      console.log(originalCredentials);
      AWS.config.update({ credentials: originalCredentials });
      const getAutomationssmExecutionStatus1 = async (ssm, executionId) => {
        try {
          const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
          console.log('Automation execution data:', data); // Log entire response
          const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
          return data;
        } catch (error) {
          console.error('Error fetching execution status:', error);
          throw error;
        }
      };
      let servicenownumber;
        // Poll status and fetch detailed result
        const pollAutomationssmExecutionStatus1 = async (ssm, executionId, interval = 1000) => {
          return new Promise((resolve, reject) => {
            const intervalId = setInterval(async () => {
              try {
                const data = await getAutomationssmExecutionStatus1(ssm, executionId);
                const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
                console.log('Current status:', status);
               
                sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
                if (['Success', 'Failed', 'TimedOut', 'Cancelled','Pending'].includes(status)) {
                    if ([ 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                        sendUpdate(`Error: ${data.AutomationExecution.CurrentStepName} step is ${status}`);
                    }
                  clearInterval(intervalId);
                  resolve(status);
                }
              } catch (error) {
                clearInterval(intervalId);
                reject(error);
              }
            }, interval);
          });
        };
      // Poll status and fetch detailed result
      const status = await pollAutomationssmExecutionStatus1(ssm, executionID);
      const executionDetails = await getAutomationssmExecutionStatus1(ssm, executionID);
      // const data = await getAutomationssmExecutionStatus1(ssm, executionId);
      const status2= executionDetails.AutomationExecution ? executionDetails.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      console.log(executionDetails);
      console.log(qdate);
      //await storeDataInS3(ticketNumber, executionID,instancename,accountId, documentName, qdate,endTime,status, instanceId,businesscontact,email,accountname,servicenownumber);
     
    // const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} = req.body;
      const endTime =new Date();
      sendUpdate('Execution Successfull Sending Email');
      console.log('Execution Successfull Sending Email');
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${email},<EMAIL>`,
        cc: `${businesscontact} `,
        subject: `${documentName} Notification - ${instanceId}`,
        html: `<p>Hi,</p>  
 
<p>We are pleased to inform you that the EC2 instance operation has been successfully completed. Below are the details of the operation:</p>  
 
<p><strong>Status:</strong>${status}</p>  
<p><strong>Operation:</strong> ${documentName}</p>  
<p><strong>Instance Name:</strong> ${instancename}</p>  
<p><strong>Account ID:</strong> ${accountId}</p>  
<p><strong>Execution Date:</strong> ${startTime}</p>  
<p><strong>Instance ID:</strong> ${instanceId}</p>  
<p><strong>Region:</strong> ${region}</p>  
 
<p>For any inquiries, please reach out to:</p>  
<p><strong>Business Contact:</strong>${businesscontact}</p>  
<p><strong>Service Initialization Engineer:</strong> ${email}</p>  
<p><strong>AWS Account:</strong>${accountname}</p>  
 
<p>You can access the AWS Portal here:  
<a href="https://hidglobal.awsapps.com/" style="color: blue; text-decoration: none;">Click here</a>.</p>  
<p>Thanks,<br>GDIAS Team</p>
 
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</em></p>
 
    `
   
  ,
});
console.log('Execution Successfull  Email sent');
      sendUpdate('Successfull Executed Storing Logs');
      await new Promise(resolve => setTimeout(resolve, 10000));
     
      
      AWS.config.update({ credentials: originalCredentials });
      //sendUpdate('Successfull');
      res.end('Successfull');
     

    } catch (err) {
     // AWS.config.update({ credentials: originalCredentials });
 console.log(err);
      sendUpdate(`Error: ${err}`);
     
      res.end(`Error: ${err}`);
    }
  };
    router.post('/restart', (req, res) => handleSSMRequest(req, res, 'AWS-RestartEC2Instance'));
router.post('/start', (req, res) => handleSSMRequest(req, res, 'AWS-StartEC2Instance'));
return router;
}
