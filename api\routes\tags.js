const express = require('express');
const csvParser = require('csv-parser');
const router = express.Router();

module.exports = (s3, Readable) => {
  router.get('/', async (req, res) => {
    try {
      const params = {
        Bucket: 'server-provision-application',
        Key: 'Data/Tags.csv'
      };

      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }

        const stream = Readable.from(data.Body);
        const results = [];

        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
              _BusinessEmail: row[Object.keys(row)[0]],
              _BusinessSegment: row[Object.keys(row)[1]],
              _BusinessSegmentDescription: row[Object.keys(row)[2]],
              BusinessArea: row[Object.keys(row)[3]],
              CostCenter: row[Object.keys(row)[4]],
              costCenterDescription: row[Object.keys(row)[5]],
              FunctionArea: row[Object.keys(row)[6]],
              _InstanceSource: row[Object.keys(row)[7]],  // InstanceSource added
              _TechnicalContact: row[Object.keys(row)[8]],
              _SupportContact: row[Object.keys(row)[9]],
              _ProvisioningEntity: row[Object.keys(row)[10]],
              _ProvisioningEngineer: row[Object.keys(row)[11]],
              _ProvisioningJustification: row[Object.keys(row)[12]],
              _SupportTier: row[Object.keys(row)[13]],  // SupportTier added
              _SupportTierDescription: row[Object.keys(row)[14]],  // SupportTierDescription added
              _Application: row[Object.keys(row)[15]],
              _DatabaseType: row[Object.keys(row)[16]],
              _WebServerType: row[Object.keys(row)[17]],
              _SubBusinessArea: row[Object.keys(row)[18]],
              _Environment: row[Object.keys(row)[19]],
              _NetworkLocation: row[Object.keys(row)[20]],
              _SubEnvironment: row[Object.keys(row)[21]],
              h: row[Object.keys(row)[22]],
              _OperatingSystemSubType: row[Object.keys(row)[23]],
              _OperatingSystemVersion: row[Object.keys(row)[24]],
              _MaintenanceVendor: row[Object.keys(row)[25]],
              _MaintenanceEndOfLife: row[Object.keys(row)[26]],
              mapmigrated: row[Object.keys(row)[27]],
              DependencyGroups: row[Object.keys(row)[28]],
              UseCase: row[Object.keys(row)[29]],
              UseCaseDescription: row[Object.keys(row)[30]]
            });
          })
          .on('end', () => {
            // 1. Get unique Business Areas
            const uniqueBusinessAreas = [...new Set(results.map(item => item.BusinessArea))];

            // 2. Get pairs of _BusinessSegment and _BusinessSegmentDescription
            const businessSegmentPairs = results.map(item => ({
              BusinessArea: item.BusinessArea,
              _BusinessSegment: item._BusinessSegment,
              _BusinessSegmentDescription: item._BusinessSegmentDescription
            }));

            // 3. Get pairs of Cost Center and its Description
            const costCenterPairs = results.map(item => ({
              CostCenter: item.CostCenter,
              Description: item.costCenterDescription,
              FunctionArea: item.FunctionArea
            }));

            // 4. Get unique Support Tiers and corresponding Descriptions
            const supportTiers = [...new Set(results.map(item => item._SupportTier))];
            const supportTierPairs = results.map(item => ({
              _SupportTier: item._SupportTier,
              _SupportTierDescription: item._SupportTierDescription
            }));

            // 5. Get unique Instance Sources
            const instanceSources = [...new Set(results.map(item => item._InstanceSource))];

            // Organize under system tags
            const systemTags = {
              supportTiers,  // Dropdown for Support Tiers
              supportTierPairs,  // Support Tier descriptions (mapped dynamically)
              instanceSources  // Dropdown for Instance Sources
            };

            // Return the original results plus the new variables
            res.json({
              results, // Original CSV data
              uniqueBusinessAreas, // New unique business areas
              businessSegmentPairs, // New business segment pairs
              costCenterPairs, // New cost center pairs
              systemTags // System Tags for SupportTier and InstanceSource
            });
          })
          .on('error', (err) => {
            res.status(500).send(err.message);
          });
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });

  return router;
};
