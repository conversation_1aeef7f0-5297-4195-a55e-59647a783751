import React from 'react';

const AboutVolumeAttach = () => {
    return (
        <div className="about-section">
                <h2 className="about-start-title">Volume Attach</h2>
           <p className="about-start-description">
  The Volume Attach action is used to <strong>add storage to an EC2 instance</strong> by either creating a new EBS volume or attaching an existing one. 
  This is useful when you need more disk space, want to separate data from the root volume, or reuse a volume with pre-existing data. 
  Once attached, the volume can be mounted and used by the instance like any other disk, without needing to stop or restart the instance.
</p>


            <h3 className="about-start-subtitle">Steps to Attach Volume</h3>
            <ol className="about-start-steps">
              <li className="about-start-step">
                <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
                <div className="about-start-image-container">
                  {/* <img src={Start} alt="Select Account Sample" className="about-start-image" /> */}
                </div>
              </li>
              <li className="about-start-step">
                <strong>Select the AZ(AvailabilityZone):</strong> Pick the AvailabilityZone associated with that account, as Volume availability is AvailabilityZone-specific.
              </li>
              <li className="about-start-step">
                <strong>Select the Instance:</strong> Locate the instance by its ID or Name for easy identification.
                
              </li>
              
              <li className="about-start-step">
  <strong>Select Volume Option:</strong> Choose one of the following methods to attach a volume:
  Note: Bydefault it will be set to "New Volume" option.
  <ol>
    <li><strong>Existing Volume:</strong>Want to attach Existing Volume. <br></br>
    Select from the list of available EBS volumes in the region.<br></br>
    Select Volume: Locate the volume to be attached.<br></br>
    Select Device Name: Select the device name to mount volume.<br></br>
    </li>

    <li><strong>New Volume:</strong>Create a new volume by specifying size, type, and other settings before attaching.<br></br>
    Enter Required Volume Size<br></br> 
    Select Required Volume Type<br></br>
    Select Device Name<br></br>
    </li>
  </ol>
</li>
             
              <li className="about-start-step">
                <strong>Click the "Attach Volume" Button:</strong> Execute the Attach action. You can either wait in the portal for a 
                status update or log out, as a confirmation email will be sent indicating the result of the action.
              </li>
            </ol>
            {/* <h1>About Volume Attach</h1>
            <p>
                The Volume Attach feature allows you to attach additional storage volumes to your instances. 
                This is useful for expanding storage capacity or adding new storage for specific workloads.
            </p>
            <h2>Key Features</h2>
            <ul>
                <li>Attach volumes to running instances.</li>
                <li>Support for multiple volume types.</li>
                <li>Easy-to-use interface for selecting instances and volumes.</li>
            </ul>
            <h2>How to Use</h2>
            <ol>
                <li>Navigate to the Volume Attach page.</li>
                <li>Select the account, availability zone, and instance.</li>
                <li>Choose the volume to attach or create a new one.</li>
                <li>Confirm the attachment to apply the changes.</li>
            </ol> */}
        </div>
    );
};

export default AboutVolumeAttach;