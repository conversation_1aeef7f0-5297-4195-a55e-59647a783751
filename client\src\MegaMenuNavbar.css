.mega-navbar {
  background-color: #002244;
  color: white;
  padding: 12px 24px;
  font-family: sans-serif;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-logo {
  display: flex;
  align-items: center;
}

.logo {
  height: 40px;
  margin-right: 12px;
}

.nav-brand {
  font-size: 1.8rem;
  font-weight: bold;
}

.nav-items {
  display: flex;
  gap: 30px;
}

.nav-item {
  position: relative;
  cursor: pointer;
}

.nav-item span {
  padding: 6px 12px;
  transition: background 0.3s;
  font-weight: bold;
}

.nav-item:hover span {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.mega-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background-color: #fff;
  color: #000;
  display: grid;
  grid-template-columns: 1fr 1fr 1.5fr;
  padding: 24px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  z-index: 100;
  min-width: 700px;
  border-radius: 12px;
}

.menu-column {
  padding: 0 20px;
}

.menu-column h4 {
  font-size: 1.2rem;
  margin-bottom: 16px;
  color: #333;
}

.menu-link {
  margin-bottom: 12px;
  cursor: pointer;
  transition: color 0.2s;
  font-size: 1rem;
}

.menu-link:hover {
  color: #007BFF;
}

.description-column p {
  margin-top: 8px;
  font-size: 1rem;
  color: #444;
}

.nav-user {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-logo {
  height: 40px;
  border-radius: 50%;
}

.user-name {
  font-size: 1rem;
  font-weight: bold;
}

/* Modern Dropdown Styles */
.modern-dropdown {
  display: flex;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.12);
  padding: 20px 30px;
  min-width: 420px;
  gap: 32px;
  z-index: 100;
}

.menu-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.modern-menu-link {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 10px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.15s;
  font-size: 1rem;
  background: none;
  border: none;
}

.modern-menu-link.hovered, .modern-menu-link:hover {
  background: #f0f4fa;
}

.icon-and-label {
  display: flex;
  align-items: center;
  gap: 10px;
  min-width: 160px;
}

.action-icon {
  font-size: 1.2em;
  color: #2d72d9;
}

.action-label {
  font-weight: 500;
  color: #222;
}

.action-desc {
  color: #666;
  font-size: 0.95em;
  margin-top: 2px;
  max-width: 220px;
  line-height: 1.4;
}

.simple-link {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 0 12px;
  cursor: pointer;
  font-size: 1rem;
  border-radius: 6px;
  transition: background 0.15s;
}

.simple-link:hover {
  background: #f0f4fa;
}

/* --- Professional Modern Dropdown Styles --- */
.modern-dropdown-pro {
  display: flex;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 6px 32px rgba(0,0,0,0.13);
  padding: 24px 36px;
  min-width: 420px;
  gap: 40px;
  z-index: 100;
}

.menu-column-pro {
  display: flex;
  flex-direction: column;
  gap: 18px;
  min-width: 260px;
}

.menu-section-title {
  font-size: 1.08em;
  font-weight: 600;
  color: #2d72d9;
  margin-bottom: 8px;
  letter-spacing: 0.01em;
}

.menu-link-pro {
  background: none;
  border: none;
  border-radius: 8px;
  padding: 0;
  cursor: pointer;
  transition: background 0.15s;
}
.menu-link-pro.hovered, .menu-link-pro:hover {
  background: #f3f6fa;
}

.action-row-pro {
  display: flex;
  align-items: flex-start;
  gap: 18px;
  padding: 12px 18px 12px 0;
}

.icon-pro {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 38px;
  min-height: 38px;
}

.action-icon-pro {
  font-size: 2.1em;
  color: #2d72d9 !important;
  opacity: 0.92;
}

.action-text-block {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.action-title-pro {
  font-size: 1.08em;
  font-weight: 500;
  color: #23272f;
  margin-bottom: 2px;
  letter-spacing: 0.01em;
}

.action-desc-pro {
  font-size: 0.97em;
  color: #6b7280;
  line-height: 1.4;
  margin-top: 0;
  max-width: 220px;
}

/* Top-level nav and simple links (no icons) */
.nav-label {
  font-size: 1.07em;
  font-weight: 500;
  color: #23272f;
  letter-spacing: 0.01em;
  padding: 0 8px;
}

.simple-link-pro {
  display: flex;
  align-items: center;
  padding: 0 14px;
  cursor: pointer;
  font-size: 1.07em;
  border-radius: 7px;
  transition: background 0.15s;
  height: 100%;
}
.simple-link-pro:hover {
  background: #f3f6fa;
}

/* --- Professional Navbar and Dropdown Styles --- */

.navbar-pro {
  width: 100%;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0,0,0,0.06);
  padding: 0;
  position: relative;
  z-index: 100;
}

.navbar-container-pro {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 32px;
  height: 60px;
}

.no-side-padding {
  padding-left: 0 !important;
  padding-right: 0 !important;
}

.navbar-logo-pro {
  height: 32px;
  margin-right: 24px;
  margin-left: 0;
}

.no-left-margin {
  margin-left: 0 !important;
}

.no-right-margin {
  margin-right: 0 !important;
}

.navbar-links-center-pro {
  display: flex;
  align-items: center;
  gap: 22px;
}

.navbar-link-pro {
  position: relative;
  font-size: 1.01em;
  font-weight: 600;
  color: #23272f;
  text-decoration: none;
  padding: 6px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background 0.13s, color 0.13s;
  display: flex;
  align-items: center;
  letter-spacing: 0.01em;
}

.navbar-link-pro:hover {
  background: #f3f6fa;
  color: #1a4fa3;
}

.dropdown-menu-pro {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 260px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.13);
  margin-top: 8px;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  z-index: 200;
}

.dropdown-item-pro {
  display: flex;
  align-items: flex-start;
  gap: 14px;
  padding: 10px 22px 10px 16px;
  color: #23272f;
  font-size: 0.98em;
  font-weight: 500;
  text-decoration: none;
  border: none;
  background: none;
  transition: background 0.13s, color 0.13s;
  border-radius: 6px;
  min-width: 220px;
}

.dropdown-item-pro:hover {
  background: #f3f6fa;
  color: #1a4fa3;
}

.dropdown-icon-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 30px;
  margin-top: 2px;
}

.dropdown-icon {
  font-size: 1.25em;
  color: #2d72d9;
  opacity: 0.93;
}

.dropdown-label-desc-wrap {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  min-width: 0;
}

.dropdown-label-pro {
  font-size: 0.99em;
  font-weight: 600;
  color: #23272f;
  line-height: 1.1;
}

.dropdown-desc-pro {
  font-size: 0.93em;
  color: #6b7280;
  line-height: 1.3;
  margin-top: 1px;
  font-weight: 400;
  max-width: 260px;
  white-space: normal;
}

/* User section */
.navbar-user-pro {
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  cursor: pointer;
}

.user-name-pro {
  font-size: 0.99em;
  font-weight: 600;
  color: #23272f;
}

.user-icon-pro {
  height: 30px;
  width: 30px;
  border-radius: 50%;
  object-fit: cover;
  margin-left: 4px;
  border: 2px solid #e5e7eb;
}

.user-menu-pro {
  position: absolute;
  top: 110%;
  right: 0;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.13);
  padding: 16px 20px 12px 20px;
  min-width: 190px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  z-index: 300;
}

.user-email-pro {
  font-size: 0.95em;
  color: #6b7280;
  margin-bottom: 8px;
}

.logout-btn-pro {
  background: #2d72d9;
  color: #fff;
  border: none;
  border-radius: 6px;
  padding: 6px 14px;
  font-size: 0.98em;
  font-weight: 500;
  cursor: pointer;
  margin-top: 4px;
  transition: background 0.13s;
}
.logout-btn-pro:hover {
  background: #1a4fa3;
}
