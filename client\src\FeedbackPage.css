@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;700&display=swap');

.feedback-page {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
    min-height: 100vh;
    background: #f5f5f5;
    font-family: 'Poppins', sans-serif;
    color: #333;
}

/* Header Styling */
.feedback-header {
    text-align: center;
    padding: 40px;
    background: #ffffff;
    width: 100%;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.feedback-header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.feedback-header p {
    font-size: 1.2rem;
}

/* Feedback Form Container */
.feedback-container {
    max-width: 700px;
    width: 90%;
    background: #ffffff;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-top: 30px;
}

.form-title {
    font-size: 2rem;
    font-weight: bold;
    color: #333;
}

/* Star Rating */
.star-rating {
    margin: 20px 0;
}

.star {
    font-size: 2.5rem;
    color: #ccc;
    cursor: pointer;
    transition: color 0.3s ease-in-out;
}

.star.selected {
    color: #ffcc00;
}

/* Subtext */
.subtext {
    font-size: 1rem;
    color: #666;
    margin-bottom: 10px;
}

/* Feedback Textbox */
.feedback-text {
    width: 100%;
    height: 150px;
    margin-top: 20px;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    resize: none;
    font-size: 1rem;
    color: #333;
    background: #f9f9f9;
}

/* Submit Button */
.submit-btn {
    margin-top: 20px;
    width: 100%;
    padding: 12px;
    font-size: 1.2rem;
    font-weight: bold;
    color: white;
    background: #2575fc;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background 0.3s, transform 0.2s;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.submit-btn:hover {
    background: #6a11cb;
    transform: translateY(-3px);
}

/* Pop Effect for Thank You Message */
.pop-effect {
    animation: pop 0.5s ease-in-out;
}

@keyframes pop {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.1); opacity: 1; }
    100% { transform: scale(1); }
}

/* Thank You Message */
.thank-you-message {
    text-align: center;
    padding: 20px;
    color: #333;
}

.thank-you-message h2 {
    font-size: 2rem;
    color: #4CAF50;
}

/* Footer */
.feedback-footer {
    text-align: center;
    padding: 20px;
    font-size: 1rem;
    width: 100%;
    background: #ffffff;
    box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
}
