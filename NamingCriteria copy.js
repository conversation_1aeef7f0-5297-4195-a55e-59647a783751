// NamingCriteria.js
import React from 'react';
import './NamingCriteria.css'; // Ensure this path is correct

const NamingCriteria = () => {
  return (
    <div className="naming-criteria">
      <h2>Server Naming Dictionary</h2>
      <table className="criteria-table">
        <thead>
          <tr>
            <th>Key</th>
            <th>Definition</th>
            <th>Description</th>
            <th>Acceptable Values</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>P</td>
            <td>Cloud Provider</td>
            <td>1-character representing vendor</td>
            <td>
              A - Amazon AWS <br />
              M - Microsoft Azure <br />
              O - Oracle Cloud Infrastructure
            </td>
          </tr>
          <tr>
            <td>R</td>
            <td>Global Region</td>
            <td>1-character representing geographical region of cloud data center</td>
            <td>
              U - United States (us) <br />
              F - Africa (af) <br />
              A - Asia Pacific (ap) <br />
              C - Canada (ca) <br />
              E - Europe (eu) <br />
              I - Israel (il) <br />
              X - Mexico (mx) <br />
              M - Middle East (me) <br />
              S - South America (sa) <br />
              K - United Kingdom (uk)
            </td>
          </tr>
          <tr>
            <td>L</td>
            <td>Regional Locale</td>
            <td>1-character representing regional location</td>
            <td>
              C - Central <br />
              E - East <br />
              W - West <br />
              S - South <br />
              O - Southeast <br />
              N - Northeast <br />
              M - Melbourne
            </td>
          </tr>
          <tr>
            <td>D</td>
            <td>Regional Site Designator</td>
            <td>1-digit representing the physical data center in a given region</td>
            <td>1, 2, 3, 4, 5, 6, 7, 8, 9</td>
          </tr>
          <tr>
            <td>Z</td>
            <td>Availability Zone</td>
            <td>1-alphanumeric character representing the AZ</td>
            <td>1, 2, 3, 4, A, B, C, D</td>
          </tr>
          <tr>
            <td>E</td>
            <td>Environment</td>
            <td>3-character environment abbreviation</td>
            <td>
              SBX - Sandbox <br />
              DEV - Development <br />
              SUS - Sustaining <br />
              SUP - Support <br />
              AQ1 - Acquisitions 1 <br />
              AQ2 - Acquisitions 2 <br />
              CIN - Customer Integration <br />
              UAT - User Acceptance Testing <br />
              LIV - Live (production)
            </td>
          </tr>
          <tr>
            <td>O</td>
            <td>Operating System</td>
            <td>2-character abbreviation</td>
            <td>
              MS - Microsoft Windows <br />
              LX - Linux <br />
              UX - Unix
            </td>
          </tr>
          <tr>
            <td>A</td>
            <td>Application Code</td>
            <td>
              3-character abbreviation for installed application; 4-digits permitted, but reduces server number to max 9
            </td>
            <td>
              BAS - Bastion Host <br />
              EBA - EBS Application Server <br />
              EBD - EBS Database Server <br />
              DBS - Database Server (other) <br />
              SQL - MSSQL Server <br />
              UTIL - Utility Server <br />
              SMTP - SMTP Relay
            </td>
          </tr>
          <tr>
            <td>#</td>
            <td>Server Number</td>
            <td>
              Two-digit node identifier, prefaced with 0 for values less than 10. <br />
              If application code is overloaded, max values are 1-9.
            </td>
            <td>01, 02, ..., 98, 99</td>
          </tr>
        </tbody>
      </table>

      <h3>Examples of Server Naming Dictionary:</h3>
      <table className="example-table">
        <thead>
          <tr>
            <th>Example</th>
            <th>Meaning</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>pursgdevlxbas01</td>
            <td>Cloud: AWS, Region: Singapore, Env: Development, OS: Linux, App: Bastion Host, Node: 01</td>
          </tr>
          <tr>
            <td>ausdevmssql02</td>
            <td>Cloud: Azure, Region: Australia, Env: Development, OS: Microsoft SQL, Node: 02</td>
          </tr>
          <tr>
            <td>usauslivlxebd01</td>
            <td>Cloud: AWS, Region: USA, Env: Live, OS: Linux, App: EBS Database Server, Node: 01</td>
          </tr>
        </tbody>
      </table>

      <h2>Legacy Server Naming Standard</h2>
      <p>
        Adopted mid-2018 (pre-AWS): <strong>SSSSSEEEOOAAA##</strong> (15 char exactly)
        <br />
        (still valid for on-premises assets)
      </p>

      <table className="legacy-table">
        <thead>
          <tr>
            <th>Key</th>
            <th>Definition</th>
            <th>Description</th>
            <th>Acceptable Values</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>S</td>
            <td>Site Code</td>
            <td>2-character country; 3-character airport code as defined by ASSA ABLOY</td>
            <td>
              USAUS <br />
              INBLR <br />
              CZKAR <br />
              IEGWY <br />
              OIAD1 <br />
              OIAD2
            </td>
          </tr>
          <tr>
            <td>E</td>
            <td>Environment</td>
            <td>3-character environment abbreviation</td>
            <td>
              SBX <br />
              DEV <br />
              SUS <br />
              SUP <br />
              AQ1 <br />
              AQ2 <br />
              CIN <br />
              UAT <br />
              LIV
            </td>
          </tr>
          <tr>
            <td>O</td>
            <td>Operating System</td>
            <td>2-character abbreviation</td>
            <td>
              MS <br />
              LX <br />
              UX
            </td>
          </tr>
          <tr>
            <td>A</td>
            <td>Application Code</td>
            <td>3-character abbreviation for installed application</td>
            <td>
              BAS <br />
              EBA <br />
              EBD <br />
              DBS <br />
              SQL <br />
              UTIL <br />
              SMTP
            </td>
          </tr>
          <tr>
            <td>#</td>
            <td>Server Number</td>
            <td>Two-digit node identifier, prefaced with 0 for values less than 10</td>
            <td>01, 02, ..., 98, 99</td>
          </tr>
        </tbody>
      </table>

      <h3>Examples of Legacy Server Naming Standard:</h3>
      <table className="example-table">
        <thead>
          <tr>
            <th>Example</th>
            <th>Meaning</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>oiad1livlxebd01</td>
            <td>Oracle Infrastructure, Live, Linux, EBS Database Server, Node 01</td>
          </tr>
          <tr>
            <td>oiad1livlxeba11</td>
            <td>Oracle Infrastructure, Live, Linux, EBS Application Server, Cluster 1, Node 1</td>
          </tr>
          <tr>
            <td>usauslivmsbas01</td>
            <td>United States, Austin, Live, Microsoft Windows, Bastion Host, Node 01</td>
          </tr>
          <tr>
            <td>iegwylivlxsmtp1</td>
            <td>Ireland, Galway, Live, Linux, SMTP Relay Server, Node 1</td>
          </tr>
        </tbody>
      </table>
    </div>
  );
};

export default NamingCriteria;
