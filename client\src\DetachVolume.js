import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import Navbar from './Navbar'; // Add Navbar for consistency
import './AttachVolume.css'; // Updated CSS for better design

const AttachVolume = () => {
    const navigate = useNavigate();
    const [accounts, setAccounts] = useState([]);
    const [regions, setRegions] = useState([]);
    const [instances, setInstances] = useState([]);
    const [volumes, setVolumes] = useState([]);
    const [selectedAccount, setSelectedAccount] = useState('');
    const [selectedRegion, setSelectedRegion] = useState('');
    const [selectedInstance, setSelectedInstance] = useState('');
    const [volumeOption, setVolumeOption] = useState('existing');
    const [selectedVolume, setSelectedVolume] = useState('');
    const [newVolumeSize, setNewVolumeSize] = useState('');
    const [newVolumeType, setNewVolumeType] = useState('gp3');
    const [deviceName, setDeviceName] = useState('');
    const [message, setMessage] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [user, setUser] = useState({
        email: '<EMAIL>',
        displayName: 'Guest',
        firstName: 'Guest',
    });
    const [accountId, setAccountId] = useState([]);
    const [data, setData] = useState([]);
    const [accountNames, setAccountNames] = useState([]);
    const[existinginstanceData,setexistinginstanceData]=useState([]);
    const[volumedetails,setvolumedetails]=useState([]);
    const [selectedAZ, setSelectedAZ] = useState('');
    const [availabilityZones, setAvailabilityZones] = useState([]);
    const [deviceNames, setDeviceNames] = useState([]);
    const [selectedDeviceNames, setSelectedDeviceNames] = useState([]);
    // Check authentication and fetch user profile
    useEffect(() => {
        async function checkAuth() {
          try {
            const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
            setUser(response.data.user);
            
          } catch (error) {
          
            setUser(null); // Set user to null in case of an error
          }
          
        }
        checkAuth();
      },[navigate]);

    // Fetch user data and account details
    useEffect(() => {
        axios.get('https://umanage.dev.hidglobal.com/api/user')
          .then(response => {
            const fetchedData = response.data;
            // console.log('Fetched user data:', fetchedData);
            // console.log(user);
            const userEntry = fetchedData.find(entry => entry.user === user.email);
            // console.log('User entry:', userEntry);
      
            if (userEntry) {
              const accountIds = userEntry.accounts.split(',').map(account => account.trim());
              // console.log('Parsed account IDs:', accountIds);
              setAccountId(accountIds);
            } else {
              setAccountId([]);
            }
          })
          .catch(error => {
            // console.error('Error fetching user accounts:', error);
          });
      }, [user]);

    // Fetch regions and instances based on account and region
    useEffect(() => {
        if (accountId.length > 0) {
          axios.get('https://umanage.dev.hidglobal.com/api/s3')   
            .then(response => {
              let fetchedData = response.data;
              // console.log('Fetched S3 data:', fetchedData);
                
              fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
              
                    // console.log('Filtered S3 data:', fetchedData);
    
                    // Create account options with label as AccountName and value as accountId
              const accountOptions = Array.from(
                new Map(fetchedData.map(item => [item.accountId, item])).values()
                    ).map(item => ({
                        value: item.accountId,
                        label: item.AccountName,
                    }));
    
              
              // console.log('Unique account names:', uniqueAccounts);
      
              
              setAccountNames(accountOptions);
            })
            .catch(error => {
              // console.error('Error fetching S3 data:', error);
            });
        }
      }, [accountId]);
      useEffect(() => {
        if (accountId.length > 0) {
          axios.get('https://umanage.dev.hidglobal.com/api/ebs')   
            .then(response => {
              let fetchedData = response.data;
              // console.log('Fetched S3 data:', fetchedData);
      
              setData(fetchedData);
              setexistinginstanceData(fetchedData);
              setvolumedetails(fetchedData);
              // console.log('Filtered S3 data:', fetchedData);
      
              
            })
            .catch(error => {
              // console.error('Error fetching S3 data:', error);
            });
        }
      }, [accountId]);
      console.log("Data",data);
      // Ef
      const handleAccountChange = (selectedOption) => {
        setSelectedAccount(selectedOption.value);

        // Filter data for AZs based on the selected account
        const filteredData = data.filter(item => item.accountId === selectedOption.value);
        const uniqueAZs = [...new Set(filteredData.map(item => item.availabilityzone))];
        setAvailabilityZones(uniqueAZs.map(az => ({ value: az, label: az })));

        // Reset dependent fields
        setSelectedAZ('');
        setInstances([]);
        setVolumes([]);
    };

    // Handle AZ selection
    const handleAZChange = (selectedOption) => {
        setSelectedAZ(selectedOption.value);

        // Filter instances based on the selected account and AZ
        const filteredInstances = data.filter(item =>
            item.accountId === selectedAccount && item.availabilityzone === selectedOption.value
        );
        setInstances(filteredInstances.map(instance => ({
            value: instance,
            label: `${instance.InstanceName} (${instance.InstanceId})`,
        })));

        // Reset volumes
        setVolumes([]);
    };
    console.log("instances",instances);

    // Handle volume option selection
   console.log ("Volume data",volumedetails);
    const HandleInstanceChange = (selectedOption) => {
        setSelectedInstance(selectedOption.value);
        const filteredVolumes = volumedetails.filter(item =>
          item.accountId === selectedAccount &&
          item.availabilityzone === selectedAZ &&
          item.InstanceId === selectedOption.value.InstanceId
      );
        // Filter device names from volumedetails based on the selected instance's InstanceId
        
        setDeviceNames(filteredVolumes.map(volume => ({
            value: volume.volumeid,
            label: `${volume.device} - ${volume.volumeid} `,
        })));
    };

    console.log(selectedInstance);
    console.log("deviceNames",deviceNames);
    const handleSubmit = () => {
      setIsProcessing(true);
  
      // Validate required fields
      
  
      // Extract instance details
      
      
  
      // Extract volume details if 'existing' volume option is selected
      const volumeDetails = volumes.find(volume => volume.value === selectedVolume);
  
      // Prepare payload with mapped details
      const payload = {
          firstname: user.firstName,
          email :user.email,
          accountname: selectedInstance.AccountName,
          instanceId: selectedInstance.InstanceId,
          InstanceName: selectedInstance.InstanceName,
          CostCenter: selectedInstance.CostCenter,
          CostCenterDescription: selectedInstance.CostCenterDescription,
          SupportTier: selectedInstance.SupportTier,
          SupportTierDescription: selectedInstance.SupportTierDescription,
          InstanceSource: selectedInstance.InstanceSource,
          ProvisioningEntity: selectedInstance.ProvisioningEntity,
          BusinessArea: selectedInstance.BusinessArea,
          BusinessContact: selectedInstance.BusinessContact,
          BusinessContactEmail: selectedInstance.BusinessContactEmail,
          BusinessSegment: selectedInstance.BusinessSegment,
          mapmigrated: selectedInstance.mapmigrated,
          BusinessSegmentDescription: selectedInstance.BusinessSegmentDescription,
          TechnicalContact: selectedInstance.TechnicalContact,
          TechnicalContactEmail: selectedInstance.TechnicalContactEmail,
          Environment: selectedInstance.Environment,
          NetworkLocation: selectedInstance.NetworkLocation,
          FunctionalArea: selectedInstance.FunctionalArea,
          ProvisioningEngineer: selectedInstance.ProvisioningEngineer,
          BackupPlan: selectedInstance.BackupPlan,
          ProvisionJustification: selectedInstance.ProvisioningJustification,
          
          
          accountId: selectedAccount,
          availabilityZone: selectedAZ,
          
          volumeid: selectedDeviceNames,
          region:selectedInstance.Region,
      };
  
      // Log the payload
      console.log("Payload:", payload);
  
      // Submit data to the backend
      axios.post('https://umanage.dev.hidglobal.com/api/ebs/ebs-detach', payload)
          .then(response => {
              setMessage('Volume attached successfully!');
          })
          .catch(error => {
              setMessage('Failed to attach volume. Please try again.');
              console.error('Error attaching volume:', error);
          })
          .finally(() => setIsProcessing(false));
  };

    return (
       <div className="attach-volume-page">
            <Navbar />
            <div className="container mt-4">
                <h1 className="page-title text-center mb-4">Attach Volume</h1>

                <div className="row">
                    {/* Account Selection */}
                    <div className="col-md-5">
                        <label className="form-label">Select Account:</label>
                        <Select
                            options={accountNames}
                            onChange={handleAccountChange}
                            placeholder="Select an account"
                        />
                        <p className="form-description">Choose the account where the instance resides.</p>
                    </div>

                    {/* Region Selection */}
                    <div className="col-md-5">
                        <label className="form-label">Select AZ:</label>
                        <Select
                            options={availabilityZones}
                            onChange={handleAZChange}
                            placeholder="Select an AZ"
                            isDisabled={!selectedAccount}
                        />
                        <p className="form-description">Select the region of the instance.</p>
                    </div>

                    {/* Instance Selection */}
                    
                </div>
                
                <div className="col-md-10">
                        <label className="form-label">Select Instance:</label>
                        <Select
                            options={instances}
                            onChange={HandleInstanceChange}
                            placeholder="Select an instance"
                            isDisabled={!selectedAZ}
                        />
                        <p className="form-description">Choose the instance to attach the volume to.</p>
                    </div>
                {/* Volume Option Toggle */}
                <div className="row mt-4">
                    <div className="col-md-10">
                        <label className="form-label">Select Device Names:</label>
                        <Select
                            options={deviceNames}
                            
                            onChange={setSelectedDeviceNames}
                            placeholder="Select device names"
                            isDisabled={!selectedInstance}
                        />
                        <p className="form-description">Select the device names of the volumes to detach.</p>
                    </div>
                </div>

                {/* Submit Button */}
                <div className="row mt-4">
                    <div className="col-md-12 text-center">
                        <button
                            className="btn btn-primary submit-button"
                            onClick={handleSubmit}
                            
                        >
                            {isProcessing ? 'Processing...' : 'Attach Volume'}
                        </button>
                        {message && <p className="message mt-3">{message}</p>}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AttachVolume;