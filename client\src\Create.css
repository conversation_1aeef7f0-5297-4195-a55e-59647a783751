/* Navbar Styles */
@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');

.create-class {
  height: 100vh; /* Full viewport height */
  display: flex;
  flex-direction: column;
}

/* Progress Bar Styles */
.progress-container {
  display: flex;
  margin-right: 150px;
  margin-left: 150px;
  justify-content: space-between;
  position: relative;
  margin-bottom: 20px;
  
  padding-top: 70px;
  font-size: 14px;
}/* Container styling */
.accountmigration-container {
  width: 800px !important; /* Fixed width */
  height: 480px !important; /* Fixed height */
  margin: 40px auto !important;
  padding: 20px !important;
  background-color: #f9f9f9 !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  overflow-y: auto !important; /* Add scroll if content overflows */
}
.accountmigration-textarea {
  width: 100% !important;
  height: 300px !important;
  padding: 12px !important;
  font-size: 14px !important;
  border: 1px solid #ccc !important;
  border-radius: 8px !important;
  resize: vertical !important;
}

.accountmigration-button {
  background-color: #0d2162;
  color: #fff;
  padding: 12px 20px;
  width: 240px;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}
.exportaction-button {
  background-color: #0d2162;
  color: #fff;
  padding: 12px 20px;
  width: 130px;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: background-color 0.3s ease;
}
.progress-line {
  position: absolute;
  top: 58%;
  left: 5px;
  width: 97%;
  height: 4px;
  background-color: #e9ecef;
  z-index: 0;
  transform: translateY(-50%);
}

.progress-line-fill {
  position: absolute;
  top: 50%;
  left: 0;
  height: 4px;
  background-color: #08549c;
  z-index: 0;
  transform: translateY(-50%);
  transition: width 0.3s;
}

.step-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  
  
}/* Main switch container */
.switch {
  position: relative;
  width: 65px; /* Width of the switch (6rem) */
  height: 40px; /* Height of the switch (2.5rem) */
  padding: 4px; /* Padding in pixels (0.25rem) */
  font-family: Verdana, sans-serif;
}

/* Background of the switch */
.switch:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 10px;
  width: 100%;
  height: 22px; /* Height of the switch (2rem) */
  background: #e4e4e4; /* Default background */
  border-radius: 30px;
  transition: background 300ms ease; /* Smooth transition for background */
}

/* Indicator for the sliding circle */
.switch__indicator {
  width: 28px; /* Width for the sliding circle (2rem) */
  height: 28px; /* Height for the sliding circle (2rem) */
  position: absolute;
  top: 6px; /* Position within the switch (0.25rem) */
  left: 3; /* Initial position */
  background: white; /* Circle color */
  border-radius: 50%; /* Circular shape */
  transition: transform 300ms ease, background 300ms ease; /* Smooth transition for sliding */
  display: flex;
  align-items: center;
  justify-content: center; /* Center tick and cross */
  z-index: 1;
}

/* Change the background and position of the indicator for Yes */
.switch input#yes:checked ~ .switch__indicator {
  background: #1976d2; /* Blue background for Yes */
  transform: translate3d(30px, 0, 0); /* Move to the right (3rem) */
}

.switch input#yes:checked ~ .switch {
  background: #1976d2; /* Blue background for Yes */
   /* Move to the right (3rem) */
}
/* Change the background and position of the indicator for No */
.switch input#no:checked ~ .switch__indicator {
  background: grey; /* Grey background for No */
  transform: translate3d(0, 0, 0); /* Move to the left */
}

.switch input#useIAMRoleYes:checked ~ .switch__indicator {
  background: #1976d2; /* Blue background for Yes */
  transform: translate3d(30px, 0, 0); /* Move to the right (3rem) */
}

/* Change the background and position of the indicator for No */
.switch input#useIAMRoleNo:checked ~ .switch__indicator {
  background: grey; /* Grey background for No */
  transform: translate3d(0, 0, 0); /* Move to the left */
}

.switch input#createOptionalVolumeYes:checked ~ .switch__indicator {
  background: #1976d2; /* Blue background for Yes */
  transform: translate3d(30px, 0, 0); /* Move to the right (3rem) */
}

/* Change the background and position of the indicator for No */
.switch input#createOptionalVolumeNo:checked ~ .switch__indicator {
  background: grey; /* Grey background for No */
  transform: translate3d(0, 0, 0); /* Move to the left */
}

/* Hide the radio buttons */
.switch input[type="radio"] {
  display: none; /* Hide the default radio buttons */
}

/* Styling for tick and cross */
.switch__indicator .tick {
  font-size: 19px; /* Size of the tick (1.2rem) */
  color: white; /* Tick color */
  display: none; /* Initially hidden */
}

.switch__indicator .cross {
  font-size: 19px; /* Size of the cross (1.2rem) */
  color: white; /* Cross color */
  display: none; /* Initially hidden */
}

/* Show tick when Yes is checked */
.switch input#yes:checked ~ .switch__indicator .tick {
  display: flex; /* Show tick */
}

/* Show cross when No is checked */
.switch input#no:checked ~ .switch__indicator .cross {
  display: flex; /* Show cross */
}

/* Full-width labels for the switch */
.switch__label_yes {
  position: relative;
  top: 0;
  left: 35px; /* Adjusted position for yes label */
  width: 20%; /* Width for yes label */
  height: 100%;
  color: #00000000; /* Hidden text color */
  cursor: pointer; /* Change cursor to pointer */
  z-index: 2; /* Ensure it is on top */
}

.switch__label_no {
  position: relative;
  top: -35px; /* Adjusted position for no label */
  left: 5px;
  color: #00000000; /* Hidden text color */
  width: 20%; /* Width for no label */
  height: 100%;
  cursor: pointer; /* Change cursor to pointer */
  z-index: 2; /* Ensure it is on top */
}



.step-icon {
  background-color: #e9ecef;
  border-radius: 50%;
  padding: 15px;
  color: #6c757d;
  font-size: 24px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s, color 0.3s;
}

.step-icon.active {
  background-color: #08549c;
  color: #fff;
}

.step-name {
  margin-top: 5px;
  font-size: 12px;
  color: #333;
}
/* Form Styles */
.create-instance {
  padding: 20px;
  margin-top: -8vh;
   /* Add space for navbar */
}

.form-section {
  margin-bottom: 20px;
  padding: 15px;
  
  border-radius: 5px;
}

.storage-options, .upload-section {
  margin-top: 10px;
}

label {
  display: block;
  margin-bottom: 5px;
  color: #08549c;
  font-weight: bold;
}

input[type="text"] {
  width: 100%;
  padding: 5px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

input[type="file"] {
  margin-top: 10px;
}

.form-buttons {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
}

.form-buttons button {
  padding: 10px 20px;
  border: none;
  cursor: pointer;
  border-radius: 4px;
}

.form-buttons button:disabled {
  background-color: #ccc;
}

.form-buttons button[type="submit"] {
  background-color: #0d2162;
  color: white;
}

.form-buttons button[type="submit"]:disabled {
  background-color: #8c8c8c;
  color: white;
  cursor: not-allowed;
}
/* Summary Section Container */
.summary-section {
  padding: 10px; /* Reduced padding for compactness */
  background-color: #ffffff;
}

/* Main Summary Heading */
.summary-section h3 {
  color: #333;
  font-weight: 600;
  font-size: 1.4rem;
  margin-bottom: 10px;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
}

/* Collapsible Sub-headings */
.summary-heading {
  cursor: pointer;
  color: #0056b3;
  font-weight: 500;
  font-size: 1rem;
  margin: 5px 0; /* Reduced margin for compactness */
  transition: color 0.3s ease;
}

.summary-heading:hover {
  color: #003366;
}

/* Content Box Styling */
.summary-content {
  margin-top: 5px;
  padding: 8px; /* Reduced padding for compactness */
  background-color: #ffffff;
  border: 1px solid #3b4070;
  border-radius: 4px;
}

/* Each Row in the Summary */
.summary-row {
  display: flex;
  justify-content: space-between;
  padding: 6px 0; /* Reduced vertical padding */
  border-bottom: 1px solid #e0e0e0;
}

.summary-row:last-child {
  border-bottom: none;
}

/* Summary Items */
.summary-item {
  flex: 1;
  padding: 3px; /* Reduced padding for compactness */
  font-size: 0.9rem;
  color: #333;
}

/* Bold Key Styling */
.summary-item strong {
  color: #333;
  font-weight: 600;
}

/* Button Styles for 'Go' Button */
.btn {
  background-color: #0056b3;
  color: #ffffff;
  font-size: 0.9rem;
  font-weight: 500;
  border: none;
  border-radius: 4px;
  padding: 6px 10px; /* Compact button padding */
  margin-top: 10px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.btn:hover {
  background-color: #003366;
}


/* Responsive layout for mobile screens */

.form-control {
  appearance: none;  /* Remove default browser styling for the arrow */
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEgMUwxMSA0TDEgNyIgU3Ryb2tlPSIjNzhBNkE4IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgLz48L3N2Zz4=') no-repeat right 10px center;
  background-color: #fff;  /* Keep the background color */
  background-size: 12px 8px;  /* Size of the arrow */
  padding-right: 30px;  /* Space for the arrow */
  border: 1px solid #ced4da;  /* Keep Bootstrap border */
  border-radius: 0.25rem;  /* Default Bootstrap border-radius */
}

.form-control:focus {
  border-color: #80bdff;  /* Keep the Bootstrap focus color */
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);  /* Bootstrap default focus shadow */
  outline: none;  /* Remove outline */
}

/* Tab Bar Styles */
.tab-bar {
  display: flex;
  border-bottom: 3px solid #ccc;
  margin-bottom: -11px; /* Space below the tab bar */
   /* Blue bottom border for emphasis */
  overflow: hidden; /* Hide overflow for a neat look */
}

/* Tab Styles */
.tab {
  padding-right:  35px;
  padding-top: 3px;
  padding-bottom: 1px;
  padding-left: 7px;
  /* Border color */
  border-radius: 10px 10px 0 0; /* Rounded top corners */
  background-color: #ffffff; /* Light blue background for tabs */
  margin-right: 5px;
  position: relative; 
  cursor: pointer; /* Change cursor to pointer for interactivity */
  transition: background-color 0.3s, box-shadow 0.3s; /* Smooth transitions */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.318); /* Subtle shadow for depth */
}

.tab.active {
  background-color: #ccc;
  color: #000000; /* White background for active tab */
  border-bottom: 2px solid transparent; /* Prevent overlap with tab content */
  z-index: 1; /* Ensure active tab is above others */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2); /* Enhanced shadow for active tab */
}

.tab:hover {
  background-color: #d0e8ff; /* Slightly darker blue on hover */
}

.add-volume {
  padding-top: 3px;
  padding-bottom: 1px;
   /* Dashed border for add button */
  color: #007bff; /* Text color */
  cursor: pointer; /* Pointer cursor for interactivity */
   /* Rounded corners */
   /* Light blue for add button */
   /* Smooth transition */
}

.add-volume:hover {
  background-color: #c4e3ff; /* Darker blue on hover */
  transform: scale(1.05); /* Slight scaling effect on hover */
}

/* Remove Tab Styles */
.remove-tab {
  position: absolute;
  right: 5px; /* Space from the right edge of the tab */
  top: 1.5px; /* Space from the top edge of the tab */
  cursor: pointer; /* Pointer cursor */
  color: red; /* Color for the remove button */
  font-weight: bold; /* Bold text for visibility */
  font-size: 18px; /* Size of the X mark */
  transition: color 0.3s, transform 0.3s; /* Smooth transition */
}

.remove-tab:hover {
  color: darkred; /* Change color on hover */
  transform: scale(1.1); /* Slightly increase size on hover */
}

/* Storage Options Container */
.storage-options {
  border: 2px solid #ccc;
  border-top: 2px solid #cccccc00; /* Border around the volume options */
  border-radius: 0 0 10px 10px; /* Rounded corners */
  padding: 20px; /* Padding inside the storage options */
  background-color: #ffffff; /* White background for volume configs */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Light shadow for depth */
  transition: transform 0.3s; /* Animation for showing/hiding */
}

/* Volume Configuration Styles */
.volume-config {
  margin-bottom: 15px; /* Spacing between volume config fields */
}

h4 {
  margin-top: 10px; /* Space above the volume heading */
  margin-bottom: 10px; /* Space below the volume heading */
  color: #333; /* Heading color */
  font-weight: bold; /* Bold for visibility */
  text-align: left; /* Center heading for aesthetic */
}

/* Input and Select Styles */
select{
 margin-bottom: 10px;
}
.re-select{
  margin-bottom: 10px;
}
/* Label Styles */

/* Tab Animation */
@keyframes tabTransition {
  from {
    opacity: 0;
    transform: translateY(-10px); /* Slide in from top */
  }
  to {
    opacity: 1;
    transform: translateY(0); /* Original position */
  }
}

/* Volume Config Transition */
.volume-config {
  animation: tabTransition 0.3s ease-in-out; /* Apply animation */
}

.label-with-icon {
  display: inline-block;
  margin-right: 5px; /* Add some spacing between the label and icon */
}
/* Container for info icon and tooltip */
.info-icon-container {
  position: relative; /* Ensures the tooltip is positioned relative to this container */
  display: inline-block; /* Keeps the icon and tooltip inline */
}

/* Icon styling for the MdInfo icon */
.info-icon {
  font-size: 1.5rem; /* Adjust size as needed */
  cursor: pointer; /* Show pointer on hover */
  margin-left: 5px;
  margin-top: -3px;
  color: #0056b3;
}

/* Tooltip styling */
.tooltip-card {
  position: absolute;
  top: 30px; /* Adjust this value to control the distance from the icon */
  left: 0; /* Align the tooltip to the left of the icon */
  background: #f9f9f9;
  border: 1px solid #ccc;
  padding: 10px;
  border-radius: 5px;
  width: 450px; /* Adjust the width as needed */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;
  font-size: 0.85rem;
  color: #333;
}

/* Tooltip arrow styling */
.tooltip-card::before {
  content: '';
  position: absolute;
  top: -10px;
  left: 15px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #f9f9f9;
}
.close-button{
  margin-left:400px;
}
/* Scrollable container for Tags */
.scrollable-tags-container {
  max-height: 60vh; /* Set the height you want */
  overflow-y: auto;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #ffffff;
}

/* Additional spacing and styling for form sections within Tags */


/* Optional: Style the scrollbar for the scrollable section */
.scrollable-tags-container::-webkit-scrollbar {
  width: 8px;
}

.scrollable-tags-container::-webkit-scrollbar-thumb {
  background-color: #cccccc;
  border-radius: 4px;
}

.scrollable-tags-container::-webkit-scrollbar-thumb:hover {
  background-color: #aaaaaa;
}
.notification-container {
  position: fixed;
  top: 17%;
  right: 20px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 9999;  /* Ensure it's always above other content */
}

.alert-card {
  width: 350px;
  height: 70px;
  padding-left: 15px;
  background-color: rgb(255, 255, 255);
  border-radius: 8px;
  border: 3px solid #ffffff00;
  box-shadow: 2px 2px 4px 6px rgba(1, 5, 45, 0.414);
  position: relative;
  transition: all 0.3s ease-in-out;
}
.status-card {
  width: 350px;
  
  padding: 15px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 3px solid #ffffff;
  box-shadow: 2px 2px 4px 6px rgba(2, 8, 69, 0.414);
  position: relative;
  transition: all 0.3s ease-in-out;
}

.alert-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.loading-icon {
  width: 60px;
  height: 60px;
}

.loading-gif {
  width: 60px;
  height: 60px;
  object-fit: contain;
}

.alert-message {
  margin-left: 10px;
  flex-grow: 1;
  background-color: #f8f9fa00;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
  text-align: left;
}

.close-button {
  position: absolute;
  top: 5px;
  right: 5px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #344d0d;
  padding: 0;
}

.close-button:hover {
  color: #333;
}

.status-card {
  padding: 15px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
}

.status-card.success {
  background-color: #28a745; /* Green for success */
}
.status-card.done {
  background-color: #0a1646; /* Green for success */
}

.status-card.error {
  background-color: #dc3545; /* Red for error */
}

.status-icon {
  flex-shrink: 0;
}

.pop-animation {
  animation: pop 0.5s ease-out forwards;
}

.shake-animation {
  animation: shake 0.5s ease-out forwards;
}

@keyframes pop {
  0% {
    transform: scale(0);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0% {
    transform: translateX(-10px);
  }
  50% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0);
  }
}

.status-card p {
  margin: 0;
  font-size: 14px;
}
.additional-notification {
  margin-top: 20px;
  text-align: center;
  font-size: 16px;
  font-weight: bold;
  color: #333;
}
.disclaimer-root-volume-config {
  background-color: #eef1f8;
  border: 1px solid #ccc;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  font-family: Arial, sans-serif;
  font-size: 14px;
  color: #333;
}

.disclaimer-root-volume-config ul {
  list-style-type: none;
  padding: 0;
}

.disclaimer-root-volume-config ul > li {
  margin-bottom: 8px;
  font-weight: bold;
}

.disclaimer-root-volume-config ul ul {
  padding-left: 20px;
}

.disclaimer-root-volume-config ul ul li {
  font-weight: normal;
}

.disclaimer-root-volume-config ul > li:last-child {
  margin-top: 12px;
  font-weight: normal;
}
.btn-add {
  background-color: #2a1ed100; /* Green background */
  color: #09549C; /* Text color */
  border: none; /* Remove border */
  padding: 8px 12px; /* Default padding for button size */
  border-radius: 4px; /* Rounded corners */
  cursor: pointer; /* Pointer cursor */
  font-size: 20px; /* Default font size */
  display: flex; /* Flex for icon alignment */
  align-items: center; /* Center icon and text */
  justify-content: center; /* Center content */
  margin-top: 27px; /* Move button 10px down */
}
.btn-remove {
  background-color: #dc354600; /* Red background */
  color: #dc3545; /* White text */
  border: none; /* Remove border */
  padding: 8px 12px; /* Default padding for button size */
  border-radius: 4px; /* Rounded corners */
  cursor: pointer; /* Pointer cursor */
  font-size: 20px; /* Default font size */
  display: flex; /* Flex for icon alignment */
  align-items: center; /* Center icon and text */
  justify-content: center; /* Center content */
  margin-top: 27px; /* Move button 10px down */
}
.btn-add:hover {
  background-color: #09549C;
  color: white; /* Darker green on hover */
}

.btn-remove:hover {
  background-color: #c82333;
  color: white; /* Darker red on hover */
}
/* Popup Overlay */
.popup-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5); /* Semi-transparent background */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Popup Content */
.popup-content {
  position: relative; /* Enable positioning for child elements */
  background: white;
  padding: 20px;
  border-radius: 8px;
  width: 80%;
  max-width: 600px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Close Button */
.btn-close {
  position: absolute; /* Position relative to the popup content */
  top: 10px; /* Distance from the top */
  right: 10px; /* Distance from the right */
  background-color: transparent; /* Transparent background */
  border: none; /* Remove border */
  font-size: 20px; /* Font size for the close icon */
  cursor: pointer; /* Pointer cursor */
  color: #dc3545; /* Red color for the close button */
}

.btn-close:hover {
  color: #c82333; /* Darker red on hover */
}
/* Styling for Admin Users Heading */
.admin-users-heading {
  font-size: 22px; /* Larger font size for emphasis */
  font-weight: bold; /* Bold text */
  color: #000000; /* Dark blue color */
  margin-bottom: 5px; /* Space below the heading */
}

/* Styling for Admin Users Description */
.admin-users-description {
  font-size: 17px;
  font-weight:500; /* Smaller font size for description */
  color: #4a4e52; /* Muted gray color */
  margin-bottom: 20px; /* Space below the description */
  line-height: 1.5; /* Improve readability */
}
.password-container {
  position: relative;
}

.password-toggle-btn {
  position: absolute;
  top: 50%;
  right: 10px;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  color: #6c757d; /* Muted gray color */
}

.password-toggle-btn:hover {
  color: #495057; /* Darker gray on hover */
}