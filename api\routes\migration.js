const { json } = require('body-parser');
const express = require('express');
const XLSX = require('xlsx');
const router = express.Router();

const csvParser = require('csv-parser');
const csvWriter = require('csv-writer');
const stream = require('stream');

module.exports = (s3, Readable) => {


router.get('/data', async (req, res) => {
  try {
    const params = {
      Bucket: 'server-provision-application',
      Key: 'migration-details/Consolidated_Server_List.csv' // Change to your CSV file
    };

    s3.getObject(params, (err, data) => {
      if (err) {
        return res.status(500).send(err.message);
      }

      const results = [];
      const bufferStream = new stream.PassThrough();
      bufferStream.end(data.Body);

      bufferStream
        .pipe(csvParser()) // Parse the CSV data
        .on('data', (row) => {
          console.log(row);
          results.push({
            ServerName: row['Server Name'],
            IPAddress: row['IP Address'],
            newipaddress: row['NEW IP address of \r\nMigrated VM'],
            Country: row['Country'],
            Location: row['Location'],
            DependencyGroup: row['Dependency Group'],
            MigrationDisposition: row['Migration Disposition'],
            UseCase: row['Use Case'],
            UseCaseDescription: row['Use Case Description'],
            TargetAWSAccount: row['Target AWS Account'],
            SiteCode: row['Site Code'],
            _CostCenter: row['_CostCenter'],
            _CostCenterDescription: row['_CostCenterDescription'],
            _BusinessSegment: row['_BusinessSegment'],
            _BusinessSegmentDescription: row['_BusinessSegmentDescription'],
            DeprecatedOS: row['Deprecated OS'],
            DomainJoined: row['Domain Joined'],
            PatchedtoDate: row['Patched to Date'],
            map_migrated: row['map-migrated'],
            _SupportTier: row['_SupportTier'],
            _SupportTierDescription: row['_SupportTierDescription'],
            _BackupPlan: row['_BackupPlan'],
            _BackupPlanDescription: row['_BackupPlanDescription'],
            _BusinessContact: row['_BusinessContact'],
            SecondBusinessContact: row['Second Business Contact'],
            _BusinessContactEmail: row['_BusinessContactEmail'],
            _TechnicalContact: row['_TechnicalContact'],
            _TechnicalContactEmail: row['_TechnicalContactEmail'],
            _ProvisioningJustification: row['_ProvisioningJustification'],
            _NetworkLocation: row['_NetworkLocation'],
            _BusinessArea: row['_BusinessArea'],
            _FunctionalArea: row['_FunctionalArea'],
            Hostname: row['Hostname'],
          });
        })
        .on('end', () => {
          console.log(results[1]); // Example log for debugging
          res.json(results);
        })
        .on('error', (parseErr) => {
          res.status(500).send(parseErr.message);
        });
    });
  } catch (err) {
    res.status(500).send(err.message);
  }
});

router.post('/submit', async (req, res) => {
  try {
    const { ServerName, IPAddress, ...updatedData } = req.body;
    console.log(req.body);

    // const columnMapping = req.body.array.length === 1 ? {
    //   'IP Address': req.body.IPAddress,
    //   'Migration Disposition': req.body.MigrationDisposition,
    //   'Use Case Description': req.body.UseCaseDescription,
    //   'Site Code': req.body.SiteCode,
    //   _CostCenterDescription: req.body._CostCenterDescription,
    //   _BusinessSegmentDescription: req.body._BusinessSegmentDescription,
    //   Country: req.body.Country,
    //   Location: req.body.Location,
    //   _SupportTierDescription: req.body._SupportTierDescription,
    //   _BackupPlan: req.body._BackupPlan,
    //   _BackupPlanDescription: req.body._BackupPlanDescription,
    //   _BusinessArea: req.body._BusinessArea,
    //   _FunctionalArea: req.body._FunctionalArea,
    //   'Use Case': req.body.UseCase,
    //   'Target AWS Account': req.body.TargetAWSAccount,
    //   _CostCenter: req.body._CostCenter,
    //   _BusinessSegment: req.body._BusinessSegment,
    //   'NEW IP address of \r\nMigrated VM': req.body.newipaddress,
    //   'Dependency Group': req.body.DependencyGroup,
    //   _SupportTier: req.body._SupportTier,
    //   _BusinessContact: req.body._BusinessContact,
    //   'Second Business Contact': req.body.SecondBusinessContact,
    //   _BusinessContactEmail: req.body._BusinessContactEmail,
    //   _TechnicalContact: req.body._TechnicalContact,
    //   _TechnicalContactEmail: req.body._TechnicalContactEmail,
    //   _ProvisioningJustification: req.body._ProvisioningJustification
    // } : {
    //   'Migration Disposition': req.body.MigrationDisposition,
    //   'Use Case Description': req.body.UseCaseDescription,
    //   'Site Code': req.body.SiteCode,
    //   _CostCenterDescription: req.body._CostCenterDescription,
    //   _BusinessSegmentDescription: req.body._BusinessSegmentDescription,
    //   Country: req.body.Country,
    //   Location: req.body.Location,
    //   _SupportTierDescription: req.body._SupportTierDescription,
    //   _BackupPlan: req.body._BackupPlan,
    //   _BackupPlanDescription: req.body._BackupPlanDescription,
    //   _BusinessArea: req.body._BusinessArea,
    //   _FunctionalArea: req.body._FunctionalArea,
    //   'Use Case': req.body.UseCase,
    //   'Target AWS Account': req.body.TargetAWSAccount,
    //   _CostCenter: req.body._CostCenter,
    //   _BusinessSegment: req.body._BusinessSegment,
    //   'NEW IP address of \r\nMigrated VM': req.body.newipaddress,
    //   'Dependency Group': req.body.DependencyGroup,
    //   _SupportTier: req.body._SupportTier,
    //   _BusinessContact: req.body._BusinessContact,
    //   'Second Business Contact': req.body.SecondBusinessContact,
    //   _BusinessContactEmail: req.body._BusinessContactEmail,
    //   _TechnicalContact: req.body._TechnicalContact,
    //   _TechnicalContactEmail: req.body._TechnicalContactEmail,
    //   _ProvisioningJustification: req.body._ProvisioningJustification
    // };

    // const params = {
    //   Bucket: 'server-provision-application',
    //   Key: 'migration-details/Consolidated_Server_List.csv'
    // };

    // // Fetch the CSV file from S3
    // s3.getObject(params, (err, data) => {
    //   if (err) {
    //     console.error('Error fetching file from S3:', err);
    //     return res.status(500).send(err.message);
    //   }

    //   const bufferStream = new stream.PassThrough();
    //   bufferStream.end(data.Body);

    //   const rows = [];

    //   bufferStream
    //     .pipe(csvParser())
    //     .on('data', (row) => {
    //       rows.push(row);
    //     })
    //     .on('end', () => {
    //       req.body.array.forEach((serverName, index) => {
    //         console.log(`Instance ${index + 1}: ${serverName}`);

    //         // Find the row to update
    //         const rowIndex = rows.findIndex((row) => row['Server Name'] === serverName);
    //         if (rowIndex === -1) {
    //           return res.status(404).send(`Row not found for Server Name: ${serverName}`);
    //         }

    //         // Update the row with new data
    //         rows[rowIndex] = { ...rows[rowIndex], ...columnMapping };
    //       });

    //       // Write the updated rows back to CSV
    //       const csvStringifier = csvWriter.createObjectCsvStringifier({
    //         header: Object.keys(rows[0]).map((key) => ({ id: key, title: key }))
    //       });

    //       const csvData = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(rows);

    //       // Upload the updated CSV file to S3
    //       const uploadParams = {
    //         Bucket: 'server-provision-application',
    //         Key: 'migration-details/Consolidated_Server_List.csv',
    //         Body: csvData
    //       };

    //       s3.upload(uploadParams, (uploadErr) => {
    //         if (uploadErr) {
    //           console.error('Error uploading file to S3:', uploadErr);
    //           return res.status(500).send(uploadErr.message);
    //         }

    //         console.log('CSV file successfully updated in S3');
    //         res.status(200).json({ message: 'CSV file successfully updated!' });
    //       });
    //     })
    //     .on('error', (parseErr) => {
    //       console.error('Error parsing CSV:', parseErr);
    //       res.status(500).send(parseErr.message);
    //     });
    // });
  } catch (error) {
    console.error('Error processing request:', error);
    res.status(500).send(error.message);
  }
});

  // Route for /data to get data from Sheet1
//   router.get('/data', async (req, res) => {
//     try {
//       const params = {
//         Bucket: 'server-provision-application',
//         Key: 'migration-details/Consolidated_Server_List.xlsx' // Your Excel file
//       };

//       s3.getObject(params, (err, data) => {
//         if (err) {
//           return res.status(500).send(err.message);
//         }

//         const workbook = XLSX.read(data.Body, { type: 'buffer' });
//         const worksheet = workbook.Sheets['All Regions']; // Accessing 'Sheet1'
//         const jsonData = XLSX.utils.sheet_to_json(worksheet);
//         console.log(jsonData[1]);
//         const results = jsonData.map(row => ({

//           ServerName: row['Server Name'],
//           IPAddress: row['IP Address'],
//           newipaddress:row['NEW IP address of \r\nMigrated VM'],
//           Country: row['Country'],
//           Location: row['Location'],
//           DependencyGroup: row['Dependency Group'],
//             MigrationDisposition: row['Migration Disposition'], // Column G (index 6)
//             UseCase: row['Use Case'], // Column C (index 2)
//             UseCaseDescription: row['Use Case Description'], // Column M (index 12)
//             TargetAWSAccount: row['Target AWS Account'], // Column O (index 14)
//             SiteCode: row['Site Code'],
//             _CostCenter: row['_CostCenter'], // Column P (index 15)
//             _CostCenterDescription: row['_CostCenterDescription'], // Column Q (index 16)
//             _BusinessSegment: row['_BusinessSegment'], // Column U (index 20)// Column R (index 17)
//             _BusinessSegmentDescription: row['_BusinessSegmentDescription'], // Column S (index 18)
            
//             DeprecatedOS: row['Deprecated OS'], // Column T (index 19)Site Code
//             DomainJoined: row['Domain Joined'], // Column W (index 22)
//             PatchedtoDate: row['Patched to Date'], // Column F (index 5)
          
           
//             map_migrated: row['map-migrated'] ,
           
//             _SupportTier: row['_SupportTier'],
//             _SupportTierDescription: row['_SupportTierDescription'] ,
//             _BackupPlan: row['_BackupPlan'], // Column AD (index 29)
//             _BackupPlanDescription: row['_BackupPlanDescription'], // Column AF (index 31)
//             // Column AH (index 33)
            
//             _BusinessContact: row['_BusinessContact'],
//             SecondBusinessContact: row['Second Business Contact'] ,
//             _BusinessContactEmail: row['_BusinessContactEmail'], // Column AD (index 29)
            
//             _TechnicalContact: row['_TechnicalContact'] ,
//             _TechnicalContactEmail: row['_TechnicalContactEmail'], // Column AD (index 29)
           
            
//             _ProvisioningJustification: row['_ProvisioningJustification'],
//             _NetworkLocation: row['_NetworkLocation'] ,
            
//             _BusinessArea: row['_BusinessArea'],
//             _FunctionalArea: row['_FunctionalArea'] ,
            
            
           
//           }));
//           //console.log(results);
//           console.log(results[1]);
//         res.json(results);
//       });
//     } catch (err) {
//       res.status(500).send(err.message);
//     }
//   });

//   router.post('/submit', async (req, res) => {
//     console.log(req.body);
//     try {
//       const { ServerName, IPAddress, ...updatedData } = req.body;
//       console.log(req.body);
//       console.log(req.body.array.length);
//       let columnMapping ;
//       if(req.body.array.length==1){
//        columnMapping = {
//        'IP Address':req.body.IPAddress,
//   'Migration Disposition': req.body.MigrationDisposition,
//   'Use Case Description': req.body.UseCaseDescription,
//   'Site Code': req.body.SiteCode,
//   _CostCenterDescription: req.body._CostCenterDescription,
//   _BusinessSegmentDescription: req.body._BusinessSegmentDescription,
//   Country: req.body.Country,
//   Location: req.body.Location,
  
//   _SupportTierDescription: req.body._SupportTierDescription,
//   _BackupPlan:  req.body._BackupPlan,
//   _BackupPlanDescription:  req.body._BackupPlanDescription,
//   _BusinessArea:  req.body._BusinessArea,
//   _FunctionalArea:  req.body._FunctionalArea,
//   'Use Case': req.body.UseCase,
//   'Target AWS Account': req.body.TargetAWSAccount,
//   _CostCenter:req.body._CostCenter,
//   _BusinessSegment: req.body._BusinessSegment,
  
//   'NEW IP address of \r\nMigrated VM': req.body.newipaddress,
//   'Dependency Group': req.body.DependencyGroup,
//   _SupportTier: req.body._SupportTier,
//   _BusinessContact: req.body._BusinessContact,
//   'Second Business Contact': req.body.SecondBusinessContact,
//   _BusinessContactEmail: req.body._BusinessContactEmail,
//   _TechnicalContact:req.body._TechnicalContact,
//   _TechnicalContactEmail:req.body._TechnicalContactEmail,
  
//   _ProvisioningJustification: req.body._ProvisioningJustification,
  
//   //MigrationDisposition: req.body.MigrationDisposition,
  
// }}else{
//  columnMapping = {
       
//   'Migration Disposition': req.body.MigrationDisposition,
//   'Use Case Description': req.body.UseCaseDescription,
//   'Site Code': req.body.SiteCode,
//   _CostCenterDescription: req.body._CostCenterDescription,
//   _BusinessSegmentDescription: req.body._BusinessSegmentDescription,
//   Country: req.body.Country,
//   Location: req.body.Location,
  
//   _SupportTierDescription: req.body._SupportTierDescription,
//   _BackupPlan:  req.body._BackupPlan,
//   _BackupPlanDescription:  req.body._BackupPlanDescription,
//   _BusinessArea:  req.body._BusinessArea,
//   _FunctionalArea:  req.body._FunctionalArea,
//   'Use Case': req.body.UseCase,
//   'Target AWS Account': req.body.TargetAWSAccount,
//   _CostCenter:req.body._CostCenter,
//   _BusinessSegment: req.body._BusinessSegment,
  
//   'NEW IP address of \r\nMigrated VM': req.body.newipaddress,
//   'Dependency Group': req.body.DependencyGroup,
//   _SupportTier: req.body._SupportTier,
//   _BusinessContact: req.body._BusinessContact,
//   'Second Business Contact': req.body.SecondBusinessContact,
//   _BusinessContactEmail: req.body._BusinessContactEmail,
//   _TechnicalContact:req.body._TechnicalContact,
//   _TechnicalContactEmail:req.body._TechnicalContactEmail,
  
//   _ProvisioningJustification: req.body._ProvisioningJustification,
  
//   //MigrationDisposition: req.body.MigrationDisposition,
// } 
// }

//       const params = {
//         Bucket: 'server-provision-application',
//         Key: 'migration-details/Consolidated_Server_List.xlsx',
//       };
//       console.log(req.body);
//       // Fetch the file from S3
//       s3.getObject(params, async (err, data) => {
//         if (err) {
//           console.error('Error fetching file from S3:', err);
//           return res.status(500).send(err.message);
//         }
  
//         // Read and parse the Excel file
//         const workbook = XLSX.read(data.Body, { type: 'buffer' });
//         const worksheet = workbook.Sheets['All Regions'];
//         const jsonData = XLSX.utils.sheet_to_json(worksheet);
//         let newWorksheet;
//         console.log(req.body.array);
//         req.body.array.forEach((name, index) => {
//           console.log(`Instance ${index + 1}: ${name}`);
//           // You can perform other operations, like saving to a database
       
//         // Find the row to update
//         const rowIndex = jsonData.findIndex(
//           (row) => row['Server Name'] ===  req.body.array[index] 
//         );
  
//         if (rowIndex === -1) {
//           return res.status(404).send('Row not found.');
//         }
//         console.log(jsonData[rowIndex]);
  
//         // Update the row with new data
//         jsonData[rowIndex] = { ...jsonData[rowIndex], ...columnMapping };
      
//         // Convert the updated JSON data back to worksheet
//          newWorksheet = XLSX.utils.json_to_sheet(jsonData);
//       });
//         // Replace the old worksheet with the updated one
//         workbook.Sheets['All Regions'] = newWorksheet;
//         const updatedBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });

//         // Write the updated workbook to a temporary file
//         // const tempFilePath = path.join(__dirname, 'updated_file.xlsx');
//         // XLSX.writeFile(workbook, tempFilePath);
  
//         // // Upload the updated file back to S3
//         // const updatedFileData = fs.readFileSync(tempFilePath);
//         const uploadParams = {
//           Bucket: 'server-provision-application',
//           Key: 'migration-details/Consolidated_Server_List.xlsx',
//           Body: updatedBuffer,
//         };
  
//         s3.upload(uploadParams, (uploadErr) => {
//           if (uploadErr) {
//             console.error('Error uploading file to S3:', uploadErr);
//             return res.status(500).send(uploadErr.message);
//           }
  
//           // Cleanup temporary file
//           //fs.unlinkSync(tempFilePath);
//           console.log('response is ok');
//           res.status(200).json({
//                message: 'SSM Automation Document triggered successfully!'});
//         });
//       });
//     } catch (error) {
//       console.error('Error processing request:', error);
//       res.status(500).send(error.message);
//     }
//   });

  // Route for /tages to get data from another sheet
  router.get('/tages', async (req, res) => {
    try {
        const params = {
            Bucket: 'server-provision-application',
            Key: 'migration-details/Tags.xlsx' // Your Excel file
          };
    

      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }

        const workbook = XLSX.read(data.Body, { type: 'buffer' });
        const worksheet = workbook.Sheets['Tags']; // Replace with your actual sheet name
        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        console.log(jsonData[0]);
        console.log(jsonData[1]);
        console.log(jsonData[3]);
        // Adjust the mapping based on the columns in the new sheet
        const results = jsonData.slice(1).map(row => ({
            _CorporateDivision: row['_CorporateDivision'], // Column H (index 7)
            MigrationStatus: row['Migration Status'], // Column A (index 0)
            _BusinessContact: row[2], // Column B (index 1)
            _BusinessEmail: row[3], // Column G (index 6)
            _BusinessSegment: row['_BusinessSegment'], // Column C (index 2)
            _BusinessSegmentDescription: row['_BusinessSegmentDescription'], // Column M (index 12)
            _CostCenter: row['_CostCenter'], // Column O (index 14)
            _CostCenterDescription: row['_CostCenterDescription'],
            _InstanceSource: row[8], // Column P (index 15)
            TargetAWSAccount: row['Target AWS Account'], // Column Q (index 16)
            _TechnicalContact: row[10], // Column U (index 20)// Column R (index 17)
            _SupportContact: row[11], // Column S (index 18)
            
            _ProvisioningEntity: row[12], // Column T (index 19)
            _ProvisioningEngineer: row[13], // Column W (index 22)
            _ProvisioningJustification: row[14], // Column F (index 5)
            _SupportTier: row['_SupportTier'], // Column X (index 23)
            _SupportTierDescription: row['_SupportTierDescription'], // Column AA (index 26)
            _BackupPlan: row['_BackupPlan'], // Column Z (index 25)
            _BackupPlanDescription: row['_BackupPlanDescription'], // Column V (index 21)
            _Application: row[19], // Column AD (index 29)
            _Function: row[20], // Column AF (index 31)
            _DatabaseType: row[21], // Column AH (index 33)
            _WebServerType: row[22], // Column L (index 11)
            _BusinessArea: row['_BusinessArea'], // Column AD (index 29)
            _SubBusinessArea: row[24], // Column AF (index 31)
            _Environment: row[25], // Column AH (index 33)
            _NetworkLocation: row[26] ,
            _SubEnvironment: row[27], // Column AD (index 29)
            _OperatingSystem: row[28], // Column AF (index 31)
            _OperatingSystemSubType: row[29], // Column AH (index 33)
            _OperatingSystemVersion: row[30],
            _MaintenanceEndOfLife: row[31] ,
            map_migrated: row[32], // Column AD (index 29)
            DependencyGroups: row[33], // Column AF (index 31)
            UseCase: row['Use Case'], // Column AH (index 33)
            
            UseCaseDescription: row['Use Case Description'],
            _FunctionalArea: row['_FunctionalArea'] ,
            _FunctionalArea_mappings: row[37], // Column AD (index 29)
            _BackupPlanDescription: row[38], // Column AF (index 31)
            // Column AH (index 33)
            
          // Add more fields as needed
        }));
        //console.log(results);
        res.json(results);
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });

  return router;
};
