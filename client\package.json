{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.6.0", "@fortawesome/react-fontawesome": "^0.2.2", "@mui/icons-material": "^6.4.11", "@mui/material": "^7.1.0", "@mui/x-data-grid": "^7.28.0", "@mui/x-date-pickers": "^8.3.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "amazon-quicksight-embedding-sdk": "^2.10.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "cron-parser": "^5.2.0", "dayjs": "^1.11.13", "lucide-react": "^0.482.0", "material-react-table": "^3.1.0", "papaparse": "^5.4.1", "react": "^18.3.1", "react-awesome-reveal": "^4.3.1", "react-datepicker": "^8.3.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-loading-indicators": "^1.0.0", "react-router-dom": "^6.26.1", "react-scripts": "^5.0.1", "react-select": "^5.8.1", "react-time-picker": "^7.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}}