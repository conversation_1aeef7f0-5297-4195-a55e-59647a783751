import React from 'react';

const AboutScheduler = () => {
    return (
        <div className="about-section">
              <h2 className="about-start-title">About Scheduler</h2>
           <p className="about-start-description">
  The Instance Scheduler is used to <strong>automate the start and stop times of EC2 instances</strong> based on a predefined schedule. 
  This helps reduce costs by ensuring instances run only during business hours or specific time windows. 
 </p>



            <h3 className="about-start-subtitle">Steps to Create Schedule</h3>
            <ol className="about-start-steps">
              <li className="about-start-step">
                <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
                <div className="about-start-image-container">
                  {/* <img src={Start} alt="Select Account Sample" className="about-start-image" /> */}
                </div>
              </li>
              <li className="about-start-step">
        <strong>Select the Region:</strong> Pick the region associated with that account, as instance availability is region-specific.
      </li>
              <li className="about-start-step">
                <strong>Select the Instance:</strong> Locate the instance by its ID or Name for easy identification.
                
              </li>
              <li className="about-start-step">
              <strong>Select the Time Zone:</strong> Choose the appropriate time zone in which the scheduled actions (start/stop) should be executed.

                
              </li>
              
              <li className="about-start-step">
  <strong>Select Schedule Type:</strong> Choose one of the following:
  Note: Bydefault it will be set to "Recurring" option.
  <ol>
   <li>
  <strong>One-Time:</strong> Schedule a one-time action to start and stop the instance at specific date and time values. 
  If the instance is already in the desired state when the action is triggered, no change will occur.<br />
  Select the date and time to <strong>start</strong> the instance.<br />
  Select the date and time to <strong>stop</strong> the instance.
</li>


   <li>
  <strong>Recurring:</strong> Use this option when you want the scheduler to perform actions on a regular basis—either daily or weekly.
  There are <strong>two options:</strong>
  <ol>
    <li><strong>Daily:</strong> Select this if you want to start and stop the instance every day at the specified times.</li>
    <li><strong>Weekly:</strong> Select this if you want to start and stop the instance once a week on the selected day(s).</li>
  </ol>
  <br />
  
</li>
    </ol></li>
              <li className="about-start-step">
                <strong>Click the "Add Schedule" Button:</strong> Execute the Attach action. You can either wait in the portal for a 
                status update or log out, as a confirmation email will be sent indicating the result of the action.
              </li>
            </ol>
            <strong>NOTE:</strong> 
  <ul>
    <li>If you select <strong>Daily</strong> and choose Monday and Friday, the instance will start and stop on both days based on the start and stop times you provide.</li>
    <li>If you select <strong>Weekly</strong> with Monday as the start day and Friday as the stop day, the instance will start on Monday and remain running until it is stopped on Friday.</li>
    <li>Only one recurring schedule can be added per instance. To add a new recurring schedule, you must first delete the existing one.</li>
<li> you can  remove the schedule anytime by navigating to the <strong>Track</strong> section in the left task menu.</li>
<li>
  A schedule can be removed up to 5 minutes before the scheduled action. If it's less than 5 minutes before execution, the remove button will be disabled. 
  
</li>

  </ul>
            {/* <h1>About Scheduler</h1>
            <p>
                The Scheduler feature allows you to automate the start and stop of instances based on a predefined schedule. 
                This helps optimize resource usage and reduce costs by ensuring instances are only running when needed.
            </p>
            <h2>Key Features</h2>
            <ul>
                <li>Define custom schedules for instances.</li>
                <li>Automate start and stop actions.</li>
                <li>Monitor and manage schedules easily.</li>
            </ul>
            <h2>How to Use</h2>
            <ol>
                <li>Navigate to the Scheduler page.</li>
                <li>Select the instances you want to schedule.</li>
                <li>Define the start and stop times.</li>
                <li>Save the schedule to apply the changes.</li>
            </ol> */}
        </div>
    );
};

export default AboutScheduler;