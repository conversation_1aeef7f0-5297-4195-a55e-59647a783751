
/* Navbar styling */
.navbar {
    background-color: #ffffff00;
    height: 70px;
    display: flex;
    font-family: "Lato", sans-serif;
    align-items: center;
    margin-top: 10px;
    margin-bottom: -10px;
    position: sticky;
    top: 0;
    z-index: 10;
  }
  
  .navbar-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px;
    width: 100%;
  }
  
  /* Centered links in the navbar */
  .navbar-links-center {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-grow: 1;
  }
  
  .navbar-logo {
    height: 60px;
    position: absolute;
    left: 20px;
  }
  
  .navbar-link {
    position: relative;
    color: #02569b;
    text-decoration: none;
    font-weight: 800;
    font-size: 16px;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s, transform 0.3s;
    cursor: pointer;
  }
  
  .navbar-link:hover .dropdown-menu {
    display: flex;
  }
  /* Updated Dropdown Menu */
  .dropdown-menu {
    position: absolute;
    top: 100%;
    left: -20%;
    transform: translateX(-50%);
    background-color: rgb(255, 255, 255);
    
    border-radius: 4px;
    padding: 15px;
    display: none;
    flex-direction: column;
    z-index: 1000;
    min-width: 220px; /* Wider dropdown */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.34);
    transition: all 0.3s ease-in-out;
  }
  
  
  .dropdown-menu a {
    padding: 10px 15px;
    text-decoration: none;
    color: #02569b;
    font-weight: bold;
    transition: all 0.3s ease;
  }
  
  .dropdown-menu a:hover {
    background-color: #02569b;
    color: white;
    border-radius: 4px;
  }
  
  /* User dropdown styling */
  .navbar-user {
    position: relative;
    display: flex;
    cursor: pointer;
    align-items: center;
    gap: 8px;
    margin-left: auto;
    padding: 0 20px;
  }
  
  .user-icon {
    color: #02569b;
    font-size: 24px;
    cursor: pointer;
  }
  
  .user-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background-color: #fff;
    
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 1000;
    min-width: 180px; /* Set minimum width for user dropdown */
    transition: all 0.3s ease-in-out;
  }
  
  .navbar-user:hover .user-menu {
    display: block;
  }
  
  .user-menu p {
    margin: 0;
    font-size: 16px;
    color: #02569b;
  }
  
  .logout-btn {
    background-color: #ff4d4d;
    border: none;
    color: white;
    cursor: pointer;
    padding: 10px 15px;
    border-radius: 3px;
    margin-top: 10px;
    width: 100%;
    text-align: center;
    transition: background-color 0.3s ease;
  }
  
  .logout-btn:hover {
    background-color: #cc3c3c;
  }
  
  /* Add Hover Effects */
  .navbar-link:hover,
  .user-icon:hover {
    color: #02569b;
    transform: scale(1.05);
  }
  .user-icon{
    height: 35px;
    
    left: 20px;
  }
  .break{
    margin-bottom: 12px;
  }
  /* Transitions for smoother dropdown */
  .dropdown-menu,
  .user-menu {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
  }
  .user-name{
    font-size:17px;
    font-weight: bolder;
  }
  .navbar-link:hover .dropdown-menu,
  .navbar-user:hover .user-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }