

const express = require('express');
const csvParser = require('csv-parser');

const AWS = require('aws-sdk');
//const nodemailer = require('nodemailer');
const fastCsv = require('fast-csv');

const { PassThrough } = require('stream');
const { parse } = require('json2csv'); // For creating CSV logs
//const moment = require('moment'); // To handle timestamps
const router = express.Router();
 
module.exports = (s3, Readable) => {

    const assumeRole = async (accountId, firstname) => {
        const sts = new AWS.STS();
        const params = {
          RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
          RoleSessionName: firstname,
        };
        console.log(params);
        try {
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
        } catch (error) {
          AWS.config.update({ credentials: null });
            console.error('Error assuming role:', error.code, error.message);
            if (error.code === 'AccessDenied') {
              const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
              console.log(`Retrying in ${delay / 1000} seconds...`);
              await new Promise(resolve => setTimeout(resolve, delay)); 
              const data = await sts.assumeRole(params).promise();
              return data.Credentials;
                // Handle AccessDenied specifically, maybe log the accounts involved
            
          
        }else{
          console.error('Error assuming role:', error);
          throw error;
        }}
      };
    
      // Function to initialize AWS SDK with temporary credentials and region
      const initializeAWS = async (credentials, region) => {
        AWS.config.update({
          credentials: new AWS.Credentials(
            credentials.AccessKeyId,
            credentials.SecretAccessKey,
            credentials.SessionToken
          ),
          region: region
        });
      };
    
      // Function to configure AWS services (SSM, S3, EC2, CloudFormation)
      const configureAWS = () => {
        return {
          ssm: new AWS.SSM(),
          s3: new AWS.S3(),
          ec2: new AWS.EC2(),
          cloudFormation: new AWS.CloudFormation()
        };
      };
  
      router.get('/', async (req, res) => {
        try {
          const params = {
            Bucket: 'server-provision-application',
            Key: 'Data/server.csv'
          };
     
          s3.getObject(params, (err, data) => {
            if (err) {
              return res.status(500).send(err.message);
            }
     
            const stream = Readable.from(data.Body);
            const results = [];
            stream.pipe(csvParser())
              .on('data', (row) => {
                results.push({
                  IPaddress: row[Object.keys(row)[35]],
                  AccountName: row[Object.keys(row)[37]],
                  platform: row[Object.keys(row)[36]],
                  ImageId: row[Object.keys(row)[0]],
                  InstanceId: row[Object.keys(row)[1]],
                  Region: row[Object.keys(row)[7]],
                  InstanceType: row[Object.keys(row)[2]],
                  InstanceName: row[Object.keys(row)[13]],
                  BusinessArea: row[Object.keys(row)[15]],
                  CostCenter: row[Object.keys(row)[16]],
                  CostCenterDescription:row[Object.keys(row)[17]],
                  BusinessSegment: row[Object.keys(row)[18]],
                  BusinessSegmentDescription: row[Object.keys(row)[19]],
                  BusinessContactEmail:row[Object.keys(row)[21]],
                  BusinessContact:row[Object.keys(row)[20]],
                  Environment:row[Object.keys(row)[23]],
                  accountId:row[Object.keys(row)[5]],
                  VPCid:row[Object.keys(row)[4]],
                  SupportTier: row[Object.keys(row)[24]],
                  ProvisioningEngineer: row[Object.keys(row)[27]],
                  ProvisioningEntity: row[Object.keys(row)[26]],
                  ProvisioningJustification: row[Object.keys(row)[22]],
                  TechnicalContact:row[Object.keys(row)[30]],
                  FunctionalArea:row[Object.keys(row)[32]],
                  BackupPlan: row[Object.keys(row)[34]],
                  Environment: row[Object.keys(row)[23]],
                  state:row[Object.keys(row)[12]]
                });
              })
              .on('end', () => {
                res.json(results);
              })
              .on('error', (err) => {
                res.status(500).send(err.message);
              });
          });
        } catch (err) {
          res.status(500).send(err.message);
        }
      });
///approver/trigger-ssm
const csvParser = require('csv-parser');
const { PassThrough } = require('stream');
const fastCsv = require('fast-csv');
const nodemailer = require('nodemailer');


  const xlsx = require('xlsx');

const Approver = async (req, res, s3, bucketName, fileKey) => {
   console.log(req);
    try {
      let groups = '';
      let firstname=req.body.firstname;
      let x=firstname.replace(/\s+/g,'');
      let result=x.substring(0,8);
      let randomValue = Math.floor(Math.random() * 9000) + 1000;
    
    // Concatenate the random value to the string
    let y = result + randomValue.toString();
    const { instanceId, region,  accountId,tags} = req.body;
       
        try{
            if(accountId!=************){
                console.log('in tryin if');
              const credentials = await assumeRole(accountId,y);
              console.log('in try after if');
              await initializeAWS(credentials, region);}else{AWS.config.update({
                region: region
              });}
              const { ssm } = configureAWS();
          
         // const startTime = new Date();
          const { ec2  } = configureAWS();
            //const ssm = new AWS.SSM();
            const params = {
                Resources: [instanceId],
                Tags: tags,
              };
              await ec2.createTags(params).promise();
       
         
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
  
    //   const newRow = {
    //     Email: req.body.email || '',
    //     Type: req.body.type || '',
    //     Account: req.body.account || '',
    //     Approver: req.body.type === 'AAO' ?req.body.AAO:req.body.AAU|| '',
    //   };
  
    //   const results = [];
    //   let fileExists = true;
  
    //   // Step 1: Check if the CSV file exists and read it
    //   try {
    //     const s3Data = await s3.getObject({  Bucket: 'server-provision-application',
    //       Key: 'Data/request.csv'}).promise();
    //     const stream = new PassThrough();
    //     stream.end(s3Data.Body);
  
    //     await new Promise((resolve, reject) => {
    //       stream
    //         .pipe(csvParser())
    //         .on('data', (row) => results.push(row))
    //         .on('end', resolve)
    //         .on('error', reject);
    //     });
  
    //    // console.log('Existing CSV data fetched successfully.');
    //   } catch (err) {
    //     if (err.code === 'NoSuchKey') {
    //       fileExists = false;
    //       console.log('CSV file does not exist. Creating a new one.');
    //     } else {
    //       throw err; // Other S3 errors
    //     }
    //   }
    //   //console.log(results);
    //   // Step 2: Append the new row
    //   const itemToRemove = req.body.item; // Identify which row to delete
    //  // console.log(itemToRemove);
    //   const updatedResults = results.filter(
    //     (row) => row.Email !== itemToRemove.email || row.Type !== itemToRemove.type || row.Account !== itemToRemove.account
    //   );
  
    //   if (updatedResults.length === results.length) {
    //     return res.status(404).json({ message: 'Item not found in the CSV file.' });
    //   }
  
    //  // console.log('Item removed from CSV.');
  
    //   // Step 3: Write updated data back to a CSV buffer
    //   const csvStream = fastCsv.format({ headers: true });
    //   const csvBuffer = await new Promise((resolve, reject) => {
    //     const bufferStream = new PassThrough();
    //     const chunks = [];
    //     bufferStream.on('data', (chunk) => chunks.push(chunk));
    //     bufferStream.on('end', () => resolve(Buffer.concat(chunks)));
  
    //     csvStream.pipe(bufferStream);
    //     if (!fileExists) {
    //       csvStream.write(['Email', 'Type', 'Account']); // Add headers for a new file
    //     }
    //     updatedResults.forEach((row) => csvStream.write(row));
    //     csvStream.end();
    //   });
  
    //   // Step 4: Upload the updated CSV file back to S3
    //   await s3.putObject({
    //     Bucket: bucketName,
    //     Key: fileKey,
    //     Body: csvBuffer,
    //     ContentType: 'text/csv',
    //   }).promise();
  
      
  
      // Step 5: Send the email
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${req.body.email}`,
        cc: `<EMAIL> `,
        subject: `Tag Ramediator`,
        html: `
        <p>Hi tag is changed to . </p> 
        <p>If you would like to know further details, we kindly recommend that you contact irectly to inquire about the reason for the rejection. </p>
         <p>Thanks,<br>GDIAS Team</p>`
    
    
  ,
});
  
      res.status(200).json({ message: 'Request processed successfully', email: mail });
    } catch (error) {
      console.error('Error in sendingmail:', error);
      res.status(500).json({ error: error.message });
    }
  }catch (error) {
    console.error('Error in approving:', error);
    res.status(500).json({ error: error.message });
  };}
  

  
  router.post('/xcvsx', (req, res) => Approver(req, res,s3,'server-provision-application', 'Data/request.csv'));
  return router;
};