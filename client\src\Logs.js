import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './Logs.css'; // Assuming you create a separate CSS file for styles
import { useNavigate } from 'react-router-dom';
const Logs = () => {
  const [filename, setFilename] = useState('');
  const [data, setData] = useState(null);
  const [error, setError] = useState('');
  const navigate = useNavigate();
     const [user, setUser] = useState(
  {
    email: '<EMAIL>',
    displayName: 'test displayname',
    firstName: 'test firstname'
  });
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
        setUser(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);
  const handleFetchData = async () => {
    if (!filename) {
      setError('Filename is required');
      return;
    }
    
    setError(''); // Clear any previous errors
    
      
    try {
      const response = await fetch('https://umanage.dev.hidglobal.com/api/logs/get-excel-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ filename }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch data');
      }

      const result = await response.json();
      //const result = await response.json();
      console.log(result.results[0]);
      console.log(result.results[0]["Service Initialization Engineer"]);
      const resultsArray = Object.values(result.results);
      console.log(user.email);
      // Sort data based on "Provision Engineer" matching the user's email
      const sortedData = resultsArray.filter(item => item["Service Initialization Engineer"] == user.email);
      console.log(sortedData);
      setData(sortedData);
      //setData(result.results); // Update state with fetched data
    } catch (err) {
      setError(err.message);
    }
  };

  return (
    <div className="logs-container">
      <h1>Excel Data Fetcher</h1>
      <input
        type="text"
        value={filename}
        onChange={(e) => setFilename(e.target.value)}
        placeholder="Enter Excel filename"
        className="input-field"
      />
      <button onClick={handleFetchData} className="fetch-button">Fetch Data</button>

      {error && <p className="error-message">{error}</p>}
      {data && (
        <div className="data-results">
          <h2>Data Results:</h2>
          <table>
            <thead>
              <tr>
                {Object.keys(data[0]).map((key) => (
                  <th key={key}>{key}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {data.map((item, index) => (
                <tr key={index}>
                  {Object.values(item).map((value, i) => (
                    <td key={i}>{value}</td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default Logs;
