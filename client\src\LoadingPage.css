/* Full-page loading container */
.loading-page {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(135deg, rgba(53, 32, 213, 0.8), rgba(53, 32, 213, 0.6));
    position: relative;
    overflow: hidden;
  }
  
  /* Animated background effect */
  .loading-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 1%, transparent 60%);
    animation: moveBackground 10s linear infinite;
  }
  
  /* Smooth background animation */
  @keyframes moveBackground {
    0% {
      transform: translate(0, 0);
    }
    100% {
      transform: translate(-50%, -50%);
    }
  }
  
  /* Loading animation container */
  .loading-animation {
    position: relative;
    z-index: 10;
    text-align: center;
    color: #ffffff;
  }
  
  /* Spinner animation */
  .spinner {
    width: 80px;
    height: 80px;
    border: 8px solid rgba(255, 255, 255, 0.2);
    border-top: 8px solid #ffffff;
    border-radius: 50%;
    animation: spin 1.5s linear infinite;
    margin: 0 auto 20px;
  }
  
  /* Spinner animation keyframes */
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  
  /* Loading text */
  .loading-animation h2 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 10px;
  }
  
  .loading-animation p {
    font-size: 1.2rem;
    opacity: 0.8;
  }
  