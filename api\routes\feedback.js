

const express = require('express');
const csvParser = require('csv-parser');

const AWS = require('aws-sdk');
const nodemailer = require('nodemailer');


///
const { PassThrough } = require('stream');
const fastCsv = require('fast-csv');
const { parse } = require('json2csv'); // For creating CSV logs
//const moment = require('moment'); // To handle timestamps
const router = express.Router();
 
module.exports = (s3, Readable) => {

    
  router.post('/submit', async (req, res) => {
    try {
        const transporter = nodemailer.createTransport({
          host: 'relay.assaabloy.net',
          port: 25,
          secure: false,
          auth: {
            user: '<EMAIL>',
            pass: '',
          },
        });
    
        const newRow = {
          Email: req.body.email || '',
          Type: req.body.type || '',
          Account: req.body.account || '',
          Approver: req.body.type === 'AAO' ?req.body.AAO:req.body.AAU|| '',
          Username:req.body.username,
            Justification:req.body.justification,
        };
       console.log(req);
       const getStarsAndEmoji = (stars) => {
        let emoji = '⭐️'; // Default emoji for stars
        switch (stars) {
          case 1:
            emoji = '😞'; // Sad face for 1 star
            break;
          case 2:
            emoji = '😕'; // Disappointed face for 2 stars
            break;
          case 3:
            emoji = '😐'; // Neutral face for 3 stars
            break;
          case 4:
            emoji = '🙂'; // Happy face for 4 stars
            break;
          case 5:
            emoji = '😃'; // Very happy face for 5 stars
            break;
          default:
            break;
        }
        return `${'⭐️'.repeat(stars)} ${emoji}`;
      };
      
      const starsRating = req.body.stars; // Assume stars come from the frontend as a number from 1 to 5
      const starContent = getStarsAndEmoji(starsRating);
      
        // Step 5: Send the email
        const mail = await transporter.sendMail({
          from: '<EMAIL>',
          to: req.body.email,
          cc: ``,
          subject: `Thank You for Your Feedback`,
          html: `
       <p>Hi ${req.body.firstname},<br></p>
    <p>Thank you for sharing your feedback.<br></p>
    <p>Your Rating: ${starContent}</p>
    <p>${req.body.feedback}</p>
    <p>Best regards,<br>EIT CLOUD Team</p>
    `,
    
        });
    
        res.status(200).json({ message: 'Request processed successfully', email: mail });
      } catch (error) {
        console.error('Error in sending feedback email:', error);
        res.status(500).json({ error: error.message });
      }
  });

  return router;
};