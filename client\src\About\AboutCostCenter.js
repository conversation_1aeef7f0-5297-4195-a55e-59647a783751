import React, { useState, useEffect } from 'react';
import <PERSON> from 'papaparse';
import costCenterData from '../assets/csv/CostCenter.csv'; // Adjust the path to your CSV file
import './AboutCostCenter.css';
const CostCenters = () => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [costCenterFilter, setCostCenterFilter] = useState('');
  const [descriptionFilter, setDescriptionFilter] = useState('');

  useEffect(() => {
    // Load and parse CSV data
    Papa.parse(costCenterData, {
      header: true,
      download: true,
      complete: (result) => {
        setData(result.data);
        setFilteredData(result.data);
      },
    });
  }, []);

  // Filter table data based on Cost Center and Description input
  const handleFilterChange = () => {
    const filtered = data.filter(row =>
      row['Cost Center'].toLowerCase().includes(costCenterFilter.toLowerCase()) &&
      row['Description'].toLowerCase().includes(descriptionFilter.toLowerCase())
    );
    setFilteredData(filtered);
  };

  // Reset filters and show all data
  const handleReset = () => {
    setCostCenterFilter('');
    setDescriptionFilter('');
    setFilteredData(data);
  };

  return (
    <div className="cost-centers">
      <h2 className="cost-centers-title">Cost Centers</h2>

      <div className="cost-centers-filter">
        <label className="filter-label">Cost Center:</label>
        <input
          type="text"
          className="filter-input"
          value={costCenterFilter}
          onChange={(e) => {
            setCostCenterFilter(e.target.value);
            handleFilterChange();
          }}
          placeholder="Enter Cost Center"
        />

        <label className="filter-label">Description:</label>
        <input
          type="text"
          className="filter-input"
          value={descriptionFilter}
          onChange={(e) => {
            setDescriptionFilter(e.target.value);
            handleFilterChange();
          }}
          placeholder="Enter Description"
        />

        <button className="reset-button" onClick={handleReset}>Reset</button>
      </div>

      <table className="cost-centers-table">
        <thead>
          <tr>
            <th className="table-header">Cost Center</th>
            <th className="table-header">Description</th>
            <th className="table-header">Function</th>
          </tr>
        </thead>
        <tbody>
          {filteredData.map((row, index) => (
            <tr key={index}>
              <td className="table-data">{row['Cost Center']}</td>
              <td className="table-data">{row['Description']}</td>
              <td className="table-data">{row['Function']}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default CostCenters;
