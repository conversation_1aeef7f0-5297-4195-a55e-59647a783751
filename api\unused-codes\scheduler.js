const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');
const ExcelJS = require('exceljs');
const nodemailer = require('nodemailer');
const XLSX = require('xlsx');
// Export the function to set up the router with necessary utilities
module.exports = (Readable, storeDataInS3) => {
  let originalCredentials = AWS.config.credentials;

  // Function to assume an IAM role and return temporary credentials
  
  
  
  const updateExcelWithInstanceIds = async (accountId, instanceIds,region,filename,email) => {
     const s3 = new AWS.S3();
    const bucketName = 'server-provision-application';
      const excelFileKey = `Scheduler/${filename}`;
    console.log(accountId);
    console.log(instanceIds);
    let rowFound = false;
    // Download the Excel file from S3
    const params = {
      Bucket: bucketName,
      Key: excelFileKey,
    };
    const data = await s3.getObject(params).promise();
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(data.Body);

    const worksheet = workbook.worksheets[0]; // Assuming you want the first sheet

    // Iterate through rows to find the account ID
    for (let row = 1; row <= worksheet.lastRow.number; row++) {
     
      const cellValue = worksheet.getCell(`A${row}`).value;
      const regionex = worksheet.getCell(`C${row}`).value;
      console.log(cellValue); // Assuming account IDs are in column A
      if (String(cellValue) === String(accountId)&&String(regionex)===String(region)) {
        rowFound = true;
        const existingIds = worksheet.getCell(`B${row}`).value;
        console.log(existingIds); 
        const existingIdsArray = existingIds ? existingIds.split(', ').map(id => id.trim()) : [];

        // Check for duplicates
        const duplicateIds = instanceIds.filter(id => existingIdsArray.includes(id));
      
        if (duplicateIds.length > 0) {
          throw new Error(`Duplicate instance IDs found: ${duplicateIds.join(', ')}`);
        }// Assuming instance IDs are in column B
        const updatedIds = existingIds
          ? `${existingIds}, ${instanceIds.join(', ')}`
          : instanceIds.join(', ');
          console.log(updatedIds);
        worksheet.getCell(`B${row}`).value = updatedIds; // Update instance IDs
        break; // Stop searching after finding the account ID
      }
    }
    if (!rowFound) {
      const newRow = worksheet.addRow([String(accountId), instanceIds.join(', '), region]);
      newRow.commit();
  }
    // Save the updated Excel file to a buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Upload the updated file back to S3
    await s3.putObject({
      Bucket: bucketName,
      Key: excelFileKey,
      Body: buffer,
      ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }).promise();
   
      let logWorkbook = new ExcelJS.Workbook();
      let logWorksheet;
      const logFileKey = 'Scheduler/Scheduler-Logs.xlsx';
      const params1 = {
        Bucket: bucketName,
        Key: 'Scheduler/Scheduler-Logs.xlsx',
      };
      try {
          // Try fetching existing log file
          const logData = await s3.getObject(params1).promise();
          await logWorkbook.xlsx.load(logData.Body);
          logWorksheet = logWorkbook.worksheets[0]; // Get first sheet
      } catch (error) {
          console.log('Log file not found. Creating a new one...');
          logWorksheet = logWorkbook.addWorksheet('Logs');
          // Add headers
          logWorksheet.addRow(['Timestamp', 'AccountID', 'InstanceIDs', 'Region']);
      }

      // Append new log entry
     instanceIds.forEach((id) => {
  logWorksheet.addRow([
    accountId,
    id,          // Single instance ID
    region,
    filename,
    email
  ]);
});

      // Save log file to buffer
      const logBuffer = await logWorkbook.xlsx.writeBuffer();

      // Upload updated log file back to S3
      await s3.putObject({
          Bucket: bucketName,
          Key: logFileKey,
          Body: logBuffer,
          ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      }).promise();

      console.log('Log file updated successfully.');
   
    const transporter = nodemailer.createTransport({
      host: 'relay.assaabloy.net',
      port: 25,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: '',
      },
    });

    const mail = await transporter.sendMail({
      from: '<EMAIL>',
      to:email,
      // cc: `<EMAIL>,`,
      subject: `${instanceIds} is added to Scheduler`,
      html: `
  <p>Hi ,</p>
  <p>This is to inform you that <b>${instanceIds}</b> has been added to Scheduler IN  <b>${filename}</b>.</p>
  
  <p> Please note that instances are started and stopped based on the configured timezone.</p>
    <p> If you need any modifications to the schedule, kindly update the settings accordingly.</P>
    

  <p>Thanks,<br>GDIAS Team</p>
`,

    });
  };
  
 
  
  
  const removeInstanceIdsFromExcel = async (accountId, instanceIdsToRemove,region,filename,email) => {
    const s3 = new AWS.S3();
    const bucketName = 'server-provision-application';
    const excelFileKey = `Scheduler/${filename}`;
    console.log(accountId);
    console.log(instanceIdsToRemove);

    // Download the Excel file from S3
    const params = {
      Bucket: bucketName,
      Key: excelFileKey,
    };
    const data = await s3.getObject(params).promise();
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(data.Body);

    const worksheet = workbook.worksheets[0]; // Assuming you want the first sheet

    // Iterate through rows to find the account ID
    for (let row = 1; row <= worksheet.lastRow.number; row++) {
      const cellValue = worksheet.getCell(`A${row}`).value;
      const regionex = worksheet.getCell(`C${row}`).value;
      console.log(cellValue); // Assuming account IDs are in column A
      if (String(cellValue) === String(accountId)&&String(regionex)===String(region)) {
        let existingIds = worksheet.getCell(`B${row}`).value;
        console.log(existingIds); // Assuming instance IDs are in column B
        
        // Ensure existingIds is a string
        if (typeof existingIds !== 'string') {
          existingIds = ''; // Initialize as empty string if it's not
        }

        if (existingIds) {
          let existingIdsArray = existingIds.split(',').map(id => id.trim());
          // Filter out the instance IDs to remove
          existingIdsArray = existingIdsArray.filter(id => !instanceIdsToRemove.includes(id));
          
          // Update the cell with the filtered IDs
          worksheet.getCell(`B${row}`).value = existingIdsArray.join(', ');
          console.log(worksheet.getCell(`B${row}`).value); // Log updated instance IDs
        }
        break; // Stop searching after finding the account ID
      }
    }

    // Save the updated Excel file to a buffer
    const buffer = await workbook.xlsx.writeBuffer();

    // Upload the updated file back to S3
    await s3.putObject({
      Bucket: bucketName,
      Key: excelFileKey,
      Body: buffer,
      ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    }).promise();
    try {
      const params1 = {
        Bucket: bucketName,
        Key: 'Scheduler/Scheduler-Logs.xlsx',
      };
      let logWorkbook = new ExcelJS.Workbook();
      let logWorksheet;
      const logFileKey = 'Scheduler/Scheduler-Logs.xlsx';
      try {
          // Try fetching existing log file
          const logData = await s3.getObject(params1).promise();
          await logWorkbook.xlsx.load(logData.Body);
          logWorksheet = logWorkbook.worksheets[0];
      } catch (error) {
          console.log('Log file not found. No need to remove row.');
          return;
      }

      let rowToDelete = null;
      
      // Find row to delete   accountId,
          // instanceIds.join(', '),
          // region,
          // filename,
          // email
      logWorksheet.eachRow((row, rowNumber) => {
        const logInsatncetId = row.getCell(2).value;
          const logFilename = row.getCell(4).value;
          const logAccountId = row.getCell(1).value;
          const logRegion = row.getCell(3).value;
          const logemail = row.getCell(5).value;

          if (String(logAccountId) === String(accountId) && String(logRegion) === String(region)&&String(logInsatncetId) === String(instanceIdsToRemove) && String(logFilename) === String(filename)&& String(logemail) === String(email)) {
              rowToDelete = rowNumber;
          }
      });

      if (rowToDelete) {
          logWorksheet.spliceRows(rowToDelete, 1); // Remove the row
          console.log(`Removed row ${rowToDelete} from log.`);
      } else {
          console.log('No matching row found in log.');
          return;
      }

      // Save log file to buffer
      const logBuffer = await logWorkbook.xlsx.writeBuffer();

      // Upload updated log file back to S3
      await s3.putObject({
          Bucket: bucketName,
          Key: logFileKey,
          Body: logBuffer,
          ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      }).promise();
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
  
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to:email,
         cc: `<EMAIL>,`,
        subject: `${instanceIdsToRemove} is Removed From Scheduler`,
        html: `
    <p>Hi ,</p>
    <p>This is to inform you that <b>${instanceIdsToRemove}</b> has been Removed From Scheduler in  <b>${filename}</b>.</p>
    
    <p>Thanks,<br>GDIAS Team</p>
  `,   });
      console.log('Log file updated successfully (row removed).');
      
  } catch (error) {
      console.error('Error updating log file:', error.message);
  }
    
  };
  router.get('/Schedule', async (req, res) => {
    const s3 = new AWS.S3();
    try {
     // const fileName = req.query.fileName; 
      const params = {
        Bucket: 'server-provision-application',
        Key: `Scheduler/Scheduler-Logs.xlsx`
      };
      console.log(params);
      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }
 
        const workbook = XLSX.read(data.Body, { type: 'buffer' });
            const sheetName = workbook.SheetNames[0]; // Read the first sheet
            const sheet = workbook.Sheets[sheetName];

            // Convert sheet data to JSON
            const results = XLSX.utils.sheet_to_json(sheet, { header: 1 });
        console.log(results);
            // Map data to expected format (assuming columns are in order: AccountID, InstanceIDs, Region)
            const formattedResults = results.map(row => ({
                AccountID: row[0] || '',
                Instanceids: row[1] || '',
                Region: row[2] || '',
                  Zone: row[3] || '',
                engineer: row[4] || ''
            }));

            res.json(formattedResults);
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });
 
  // Route to update the Excel file with instance IDs
  router.post('/AddSchedule', async (req, res) => {
    const { accountId, instanceIds,region,filename,email } = req.body; // Expecting accountId and instanceIds in the request body
    console.log(req.body);
    console.log("in sched");
    if (!accountId || !instanceIds || !Array.isArray(instanceIds)) {
      return res.status(400).json({ error: 'Missing required fields: accountId and instanceIds (array).' });
    }

    try {
      await updateExcelWithInstanceIds(accountId, instanceIds,region,filename,email);
      res.status(200).json({ message: 'Excel file updated successfully.' });
    } catch (err) {
      console.log(err);
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
  
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to:email,
        // cc: `<EMAIL>,`,
        subject: `${instanceIds} Adding to Scheduler action went WRONG `,
        html: `
    <p>Hi ,</p>
    <p>This is to inform you that <b>${instanceIds}</b> is not properly add to  Scheduler in  <b>${filename}</b>.</p>
    <p><NAME_EMAIL> for details</p>
    <p>Thanks,<br>GDIAS Team</p>
  `,   });
      res.status(500).json({ error: err.message });
    }
  });

  // Route to remove instance IDs from the Excel file
  router.post('/RemoveSchedule', async (req, res) => {
    const { accountId, instanceIds,region, filename,email} = req.body;
    const  instanceIdsToRemove =instanceIds; // Expecting accountId and instanceIdsToRemove in the request body
    console.log("emsoau");
    console.log(req.body);
    if (!accountId || !instanceIdsToRemove || !Array.isArray(instanceIdsToRemove)) {
      return res.status(400).json({ error: 'Missing required fields: accountId and instanceIdsToRemove (array).' });
    }

    try {
      await removeInstanceIdsFromExcel(accountId, instanceIdsToRemove,region,filename,email);
      res.status(200).json({ message: 'Instance IDs removed successfully.' });
    } catch (err) {
      console.log(err);
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
  
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to:email,
        // cc: `<EMAIL>,`,
        subject: `${instanceIds} Remove from Scheduler action went WRONG `,
        html: `
    <p>Hi ,</p>
    <p>This is to inform you that <b>${instanceIds}</b> is not properly removed from  Scheduler in  <b>${filename}</b>.</p>
    <p><NAME_EMAIL> for details</p>
    <p>Thanks,<br>GDIAS Team</p>
  `,   });
     
      res.status(500).json({ error: err.message });

    }
  });

  return router;
};