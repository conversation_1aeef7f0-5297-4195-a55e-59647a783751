.provision-instances {
    margin: 20px;
    font-family: Arial, sans-serif; /* Change to your preferred font */
}

.header-title {
    font-size: 2.5em;
    font-weight: bold;
    color: #333;
    text-align: center;
    margin-bottom: 30px;
}

.overview-text {
    font-size: 1.2em;
    color: #666;
    margin-bottom: 40px;
    text-align: center;
}

.step-section {
    margin-bottom: 40px;
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 20px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.step-title {
    font-size: 2em;
    color: #2c3e50;
    margin-bottom: 15px;
}

.step-description {
    font-size: 1em;
    color: #555;
    margin-bottom: 15px;
}

.step-subtitle {
    font-size: 1.5em;
    color: #2980b9;
    margin-top: 20px;
}

.step-details {
    margin-left: 20px;
}

.vpc-list {
    list-style-type: none;
    padding-left: 0;
}

.vpc-list li {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #ecf0f1;
    border-left: 5px solid #3498db;
}

.step-image {
    width: 100%;
    height: auto;
    border-radius: 5px;
    margin-top: 15px;
}

/* Tag Configurations */
.step-section h3.step-subtitle {
    margin-top: 20px;
    color: #2980b9; /* Color for subtitle */
}

.step-section p.step-description {
    margin-bottom: 10px;
}

.step-section strong {
    color: #333; /* Emphasizing important text */
}
