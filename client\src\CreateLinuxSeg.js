import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import 'bootstrap/dist/css/bootstrap.min.css';
import { faCog, faHdd, faNetworkWired, faTags, faCheckCircle ,faUser,faCubes} from '@fortawesome/free-solid-svg-icons';
import { FaXmark } from "react-icons/fa6";
import { FaPlus } from "react-icons/fa6"; 
import { FaCubes } from "react-icons/fa6";

import './Create.css';
import Select from 'react-select';
import HIDlogo from './assets/hidLogo.png';
import Ulogo from './assets/Ulogo.png';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import { MdInfo  } from "react-icons/md";
import Loading from './assets/Rocket.gif'
import Navbar from './Navbar';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
const CreateWindows = () => {
  const [step, setStep] = useState(1);
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    InstanceName: '',
    InstanceType: '',
    AMI: '',
    securityGroupIds: [],
    subnetId: '',
    UseBlockDeviceMappings: '',
    CreateIAMRole:'no',
    IAMRoleName: '' ,
   
    CostCenter: '',
    CostCenterDescription: '',
    SupportTier: '',
    SupportTierDescription: '',
    InstanceSource: 'NEW',
    ProvisioningEntity: '',
    ProvisioningJustification: '',
    BusinessArea: '',
    BusinessContact: '',
    BusinessContactEmail: '',
    BusinessSegment: '',
    BusinessSegmentDescription: '',
    TechnicalContact: '',
    TechnicalContactEmail: '',
    Environment: '',
    NetworkLocation: 'INTERNAL',
    FunctionalArea: '',
    ProvisioningEngineer: '',
    BackupPlan: '',
    accountId:"",
    DeviceName : '/dev/xvda',
    useIAMRole: '' ,
    createOptionalVolume: 'no',
    volume1: { ebsVolumeSize: '',  ebsVolumeType: 'gp3', DeviceName: '/dev/sda1' },
    volume2: null,
    volume3: null,
    volume4: null,
    volume5: null ,
    volumes: {}, // Initialize as an empty object
    selectedVolume: 'volume1',
    volumeCount: 1,
  });
 
  const [message, setMessage] = useState('');
  const [alertMessage, setAlertMessage] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [accounts, setAccounts] = useState([]);
  const [vpcs, setVpcs] = useState([]);
  const [subnets, setSubnets] = useState([]);
  const [regions, setRegions] = useState([]);
  const [securityGroups, setSecurityGroups] = useState([]);
  const[messagestatus, setMessagestatus] = useState();
  const [instances, setInstances] = useState([]);
  const [filteredAMIs, setFilteredAMIs] = useState([]);
  const [selectedVCPU, setSelectedVCPU] = useState('');
  const [selectedMemory, setSelectedMemory] = useState('');
  const [compatibleInstancesOptions, setCompatibleInstancesOptions] = useState([]);
  const [VCPUoptions, setVCPUoptions] = useState([]);
  const [Memoryoptions, setMemoryoptions] = useState([]);
  const [compatibleInstances, setCompatibleInstances] = useState([]);
  const [showPopup, setShowPopup] = useState(false); // State to toggle popup visibility

  const togglePopup = () => {
    setShowPopup(!showPopup); // Toggle popup visibility
  };
  const [selectedSecurityGroups, setSelectedSecurityGroups] = useState([]); // State for selected Security Groups

  const [selectedBusinessArea, setSelectedBusinessArea] = useState('');
  const [selectedBusinessSegment, setSelectedBusinessSegment] = useState('');
  const [selectedCostCenter, setSelectedCostCenter] = useState('');
  const [selectedBusinessSegmentDescription, setSelectedBusinessSegmentDescription] = useState('');
  const [selectedCostCenterDescription, setSelectedCostCenterDescription] = useState('');
  const [businessSegments, setBusinessSegments] = useState([]);
   const [costCenters, setCostCenters] = useState([]);
   const [businessDescriptions, setbusinessDescriptions] = useState([]);
   const [selectedFunctionArea, setSelectedFunctionArea] = useState('');
  const [email, setEmail] = useState('');
  const [backupPlanLabel, setBackupPlanLabel] = useState('');
  const [envPlanLabel, setEnvPlanLabel] = useState('');
  const [data1, setData1] = useState({
    uniqueBusinessAreas: [],
    businessSegmentPairs: {},
    costCenterPairs: {},
 
  });
  
  const [user, setUser] = useState(
    {
      email: '<EMAIL>',
      displayName: 'test displayname',
      firstName: 'test firstname'
    });
  const [systemTags, setSystemTags] = useState({
    supportTiers: [],
    supportTierPairs: [],
    instanceSources: []
  });
  const [platform, setPlatform] = useState("linux");
  const [archi, setarchi] = useState("x86_64");

  const [selectedSupportTier, setSelectedSupportTier] = useState('');
  const [accountId,setAccountId]=useState([]);
  const [supportTierDescription, setSupportTierDescription] = useState('');
  const [selectedInstanceSource, setSelectedInstanceSource] = useState('');
  const [manualAmi, setManualAmi] = useState(false);
  const infoIcon = document.getElementById('info-icon');

  const [additionalInfo, setAdditionalInfo] = useState({
    _BusinessContact: user.firstName,
    _BusinessContactEmail: user.email,
    _TechnicalContact:  user.firstName,
    _TechnicalContactMail: user.email,
    map:'migSITGOMR8R2',
    _Environment: '',
    _Region: '',
    _NetworkLocation: 'INTERNAL',
    _BackupPlan: '',
    _ProvisionContact: '',
    _ProvisionEntity:'HID Global',
    _ProvisionJustification:'',
    ProvisioningEngineer: user.email,
  });
  const [expandedSections, setExpandedSections] = useState({
    configuration: true,
    storage: true,
    network: true,
    tags: true,
    additionalInfo: true,
  });
  const regionMapping = {
    "us-east-1": "UE1",
    "eu-central-1": "EC1",
    "ap-south-1": "AS1",
    // Add more mappings
  };
  
  
  
  const osMapping = {
    "windows": "MS",
    "linux": "LX",
    
  };
  const [appCode, setAppCode] = useState(""); 
  useEffect(() => {
    // Log the current values for debugging
    console.log("Region from formData:", formData.Region);
    console.log("AZ from formData:", formData.az);
    console.log("Environment from formData:",formData._Environment);
    console.log("Platform:", platform);
    console.log("AppCode:", appCode);
  
    // Safely extract AZ code
    const azCode = formData.az ? formData.az.slice(-1).toUpperCase() : ""; // Default to empty string if undefined
    console.log("Extracted AZ Code:", azCode);
  
    const regionCode = regionMapping[formData.Region] || "";
    console.log("Mapped Region Code:", regionCode);
  
    const environmentCode = additionalInfo._Environment ?formData._Environment : "";
    console.log("Mapped Environment Code:", environmentCode);
  
    const osCode = osMapping[platform] || "";
    console.log("Mapped OS Code:", osCode);
  
    // Check if all values are valid before generating the instance name
    if (regionCode && azCode && environmentCode && appCode.length === 5) {
      const generatedName = `A${regionCode}${azCode}${environmentCode}${osCode}${appCode}`;
      console.log("Generated Instance Name:", generatedName);
      setFormData((prevData) => ({
        ...prevData,
        InstanceName: generatedName, // Save the instance name in formData
      }));
    } else {
      console.log("Instance Name not generated: Missing or invalid inputs.");
    }
    console.log("Environment from formData:",additionalInfo._Environment);
  }, [formData.Region, formData.az,formData._Environment,  platform, appCode]);
  // Toggle function for sections
  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };
  const handleAdditionalInfoChange = (e) => {
    const { name, value } = e.target;
    setAdditionalInfo(prevState => ({
        ...prevState,
        [name]: value
    }));
};


const handleBackupPlanChange = (e) => {
    const value = e.target.value;
    setAdditionalInfo(prevState => ({
        ...prevState,
        _BackupPlan: value
    }));
 
    let label = '';
    if (value === 'GOLD') {
        label = 'Back Up will Done Every Day';
    } else if (value === 'SILVER') {
        label = 'Back Up will be run Weekly';
    } else if (value === 'BRONZE') {
        label = 'Back Up will be run Monthly';
    } else if (value === 'PLATINUM') {
      label = 'Multiple Backups Every Day';
    } else if (value === 'N/A') {
      label = 'No Backup';
  }
    // console.log(backupPlanLabel);
    setBackupPlanLabel(label);
};
 
const handleEnvPlanChange = (e) => {
  const value = e.target.value;
  setAdditionalInfo(prevState => ({
      ...prevState,
      _Environment: value
  }));
  setFormData(prevState => ({
    ...prevState,
    _Environment: value
}));
  let label = '';
  if (value === 'production') {
      label = 'Resource will be deployed in Production';
  } else if (value === 'non-production') {
      label = 'Resource will be deployed in Non-Production';
  } else if (value === 'shared') {
      label = 'Resource will be deployed in Shared';
  }
  setEnvPlanLabel(label);
};
 
 
const handleBusinessAreaChange = (e) => {
  const selectedArea = e.target.value;
  setSelectedBusinessArea(selectedArea);
 
  // Reset selections
  //setSelectedBusinessSegment('');
  //setSelectedBusinessSegmentDescription('');
  const businessSegments = data1.businessSegmentPairs
      .filter(pair => pair.BusinessArea === selectedArea)
      .map(pair => ({
          segment: pair._BusinessSegment,
          description: pair._BusinessSegmentDescription
      }));
      // // console.log(businessSegments);
 
  // Update state with new business segments
  setBusinessSegments(businessSegments);
  const businessDescriptions = data1.businessSegmentPairs
      .filter(pair => pair.BusinessArea === selectedArea)
      .map(pair => ({
          segment: pair._BusinessSegment,
          description: pair._BusinessSegmentDescription
      }));
      // // console.log(businessSegments);
 
  // Update state with new business segments
  setbusinessDescriptions(businessDescriptions);
  const filteredCostCenters = data1.costCenterPairs
  .map(pair => ({
      center: pair.CostCenter,
      description: pair.Description
  }));
 
// Update state with new cost centers
setCostCenters(filteredCostCenters);
    //// console.log(filteredCostCenters);
};
 
 
const handleBusinessSegmentChange = (e) => {
    const selectedSegment = e.target.value;
    setSelectedBusinessSegment(selectedSegment);
    let description = '';
 
    // Find the segment data manually
    for (let i = 0; i < businessSegments.length; i++) {
        if (businessSegments[i].segment === selectedSegment) {
            description = businessSegments[i].description;
            break;
        }
    }
 
    // Set the description
    setSelectedBusinessSegmentDescription(description);
 
};
const handleBusinessSegmentDescriptionChange = (e) => {
  const selectedDescription = e.target.value;
  setSelectedBusinessSegmentDescription(selectedDescription);
  let segment;
 
  // Find the segment data manually
  for (let i = 0; i < businessSegments.length; i++) {
      if (businessSegments[i].description === selectedDescription) {
          segment = businessSegments[i].segment;
          break;
      }
  }
 
  // Set the description
  setSelectedBusinessSegment(segment);
   // console.log(segment);
};
 
const handleCostCenterChange = (e) => {
  const selectedCenter = e.target.value;
  setSelectedCostCenter(selectedCenter);
 
  const matchingPair =data1.costCenterPairs.find(pair => pair.CostCenter === (selectedCenter));
 
  setSelectedCostCenterDescription(matchingPair.Description);
  setSelectedFunctionArea(matchingPair.FunctionArea);
 
};
 
const handleCostCenterDescriptionChange = (e) => {
  const selectedCenterdescription = e.target.value;
  setSelectedCostCenterDescription(selectedCenterdescription);
 
 
  const matchingPair = data1.costCenterPairs.find(pair => pair.Description === selectedCenterdescription);
  setSelectedFunctionArea(matchingPair.FunctionArea);  setSelectedCostCenter(matchingPair.CostCenter);
};
useEffect(() => {
  async function checkAuth() {
    try {
      const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
      setUser(response.data.user);
      setFormData({ ...formData, ProvisioningEngineer: response.data.user.email });
      setAdditionalInfo({
        ...additionalInfo, _BusinessContact: response.data.user.firstName,
        _BusinessContactEmail: response.data.user.email,
        _TechnicalContact: response.data.user.firstName,
        _TechnicalContactMail: response.data.user.email,
      });
    } catch (error) {
    
       // Set user to null in case of an error
    }
    
  }
  checkAuth();
},[navigate]);
useEffect(() => {
  let x=[];
  axios.get('https://umanage.dev.hidglobal.com/api/user')
    .then(response => {
      const fetchedData = response.data;
      // console.log(user);
      // console.log(fetchedData); 
       //console.log('Fetched user data:', fetchedData);
     // console.log(user);
      const userEntry = fetchedData.find(entry => entry.user === user.email);
      //console.log('User entry:', userEntry);

      if (userEntry) {
        const accountIds = userEntry.accounts.split(',').map(account => account.trim());
        //console.log(accountIds);
        x=accountIds;
        // console.log('Parsed account IDs:', accountIds);
        setAccountId(accountIds);
      } else {
        setAccountId([]);
      }
    })
    .catch(error => {
      // console.error('Error fetching user accounts:', error);
    });
    axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
    .then((response) => {
      const uniqueAccounts = Array.from(new Set(response.data.map(item => item.CreateAccountId)))
         .map(accountId => response.data.find(item => item.CreateAccountId === accountId));
         let fetchedData = response.data;
        
        fetchedData = uniqueAccounts.filter(item => x.includes(item.CreateAccountId));
           //console.log('Filtered S3 data:', fetchedData);
  
          //const uniqueAccounts = [...new Set(fetchedData.map(item => item.CreateAccountName))];
           //console.log('Unique account names:', uniqueAccounts);
      setAccounts(fetchedData); // Store the entire account object
      //console.log("account"+accounts);
    })
    .catch((error) => {
      // console.error('Error fetching accounts:', error);
    });
}, [user]);
 
const handleAccountChange = (selectedOption) => {
  const selectedAccountId = selectedOption.value;
  setFormData({ ...formData, accountId: selectedAccountId });
  setVpcs([]); // Reset VPCs
  setSubnets([]);
  setSecurityGroups([]); // Reset Subnets
 
  // Fetch VPCs based on the selected account
  axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
    .then((response) => {
      const uniqueVPCs = Array.from(new Set(response.data
        .filter(vpc => vpc.CreateAccountId === selectedAccountId)
        .map(vpc => JSON.stringify(vpc))) // Convert to string for uniqueness
      ).map(vpc => JSON.parse(vpc)); // Convert back to object
 
      setVpcs(uniqueVPCs);

       // Set the filtered Security Groups
      
    })
    .catch((error) => {
      // console.error('Error fetching VPCs:', error);
    });
};
  // The platform is a dependency, so this re-runs if platform changes

useEffect(() => {
  axios.get('https://umanage.dev.hidglobal.com/api/ec2-price')
    .then((response) => {
        // Set only instances matching the platform
      setVCPUoptions(response.data.results);  // Set all regions
      setMemoryoptions(response.data.results);  // Set only instances matching the platform
      if (selectedVCPU || selectedMemory) {
        const filteredOptions = response.data.results.filter(option => {
          const matchesVCPU = selectedVCPU ? option.vCPU === selectedVCPU : true;
          const matchesMemory = selectedMemory ? option.memory === selectedMemory : true;
          return matchesVCPU && matchesMemory;
        });
        setCompatibleInstancesOptions(filteredOptions);
      } else {
        setCompatibleInstancesOptions(response.data.results);
      }
    })
    .catch((error) => {
      // console.error("There was an error fetching the AMI data!", error);
    });
},[] );  // The platform is a dependency, so this re-runs if platform changes

// Handle region change and filter AMIs based on the region
const handleRegionChange = (e) => {
  const selectedRegion = e.target.value.toLowerCase();
  setFormData({ ...formData, Region: selectedRegion });

  // Filter AMIs for the selected region
  const filtered = regions.filter(inst =>
    inst.Region.toLowerCase() === selectedRegion &&
    inst.Platform.toLowerCase() === platform
  );

  // console.log("Filtered AMIs for selected region:", filtered);
  setFilteredAMIs(filtered);  // Update the filtered AMIs based on the region
};
const handleInstanceNameChange = (e) => {
  setFormData({ ...formData, InstanceName: e.target.value });
};
// Handle architecture change and further filter AMIs based on architecture
const handleArchitectureChange = (e) => {
  const selectedArchitecture = e.target.value;
  setFormData({ ...formData, Architecture: selectedArchitecture });

  // Filter AMIs based on both the selected region and architecture
  const filtered = regions
    .filter(ami => ami.Region.toLowerCase() === formData.Region && ami.Architecture === archi &&
    ami.Platform.toLowerCase() === platform );

  // console.log("Filtered AMIs for selected architecture:", filtered);
  setFilteredAMIs(filtered);  // Update filtered AMIs based on architecture
};

// Handle AMI selection and find compatible instances
const handleAMIChange = (e) => {
  const selectedAMI = e.target.value;
  const amiData = instances.find(amiObject => amiObject.ami && amiObject.ami === selectedAMI);

  // console.log('Selected AMI:', selectedAMI);
  // console.log('AMI Data:', amiData);

  setFormData({ ...formData, AMI: selectedAMI });

  // Check if amiData exists and has compatible instances
  if (amiData && amiData.instance && amiData.instance.length > 0) {
    const compatibleInstancesArray = amiData.instance;
    // console.log('Compatible Instances Array:', compatibleInstancesArray);
    setCompatibleInstances(compatibleInstancesArray);  // Set compatible instances based on selected AMI
  } else {
    setCompatibleInstances([]);  // Clear if no compatible instances are found
    // console.log('No Compatible Instances found.');
  }
};

  const handleCustomAMIInstanceTypeChange = (e) => {
    // console.log(e.target.value );
     setFormData({ ...formData, InstanceType: e.target.value });
   };
 
  // Function to handle Compatible Instance selection
  const handleInstanceTypeChange = (selectedOption) => {
   // console.log(selectedOption);
    setFormData({ ...formData, InstanceType: selectedOption.value });
  };
 
  // Map compatibleInstances to options
  const compatibleInstanceOptions = compatibleInstances.map((instance, index) => ({
    value: instance,
    label: instance,
  }));
 
  const handleUseIAMRoleChange = (e) => {
    setFormData({
      ...formData,
      CreateIAMRole: e.target.value,  // Update the selection
    });
  };

  const handleIAMRoleNameChange = (e) => {
    setFormData({ ...formData, IAMRoleName: e.target.value });
  };
  // const [user, setUser] = useState(null);
    
  

  useEffect(() => {
    if (formData.Region && formData.accountId) {
      axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
        .then((response) => {
          // console.log('Original VPC Data:', response.data);
 
          // Filter VPCs based on Region and AccountId
          const filteredVPCs = response.data.filter(vpc => {
            return (
              vpc.VpcRegion.toLowerCase() === formData.Region.toLowerCase() &&
              vpc.CreateAccountId.toLowerCase() === formData.accountId.toLowerCase()
            );
          });
 
          // Create a map to store unique VPCs by VpcId
          const uniqueVPCsMap = {};
 
          filteredVPCs.forEach(vpc => {
            // Using VpcId as the key for uniqueness
            uniqueVPCsMap[vpc.VpcId] = vpc;
          });
 
          // Convert the map values back to an array
          const uniqueVPCs = Object.values(uniqueVPCsMap);
 
          // console.log('Filtered Unique VPCs:', uniqueVPCs);
          setVpcs(uniqueVPCs);
        })
        .catch((error) => {
          // console.error('Error fetching VPCs:', error);
        });
    }
  }, [formData.Region, formData.accountId]);
  
 
 
  // Handle VPC selection and fetch unique subnets
  const handleVpcChange = (selectedVpcId) => {
    setFormData({ ...formData, vpcId: selectedVpcId });
 
    // Fetch subnets based on the selected VPC
    axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
      .then((response) => {
        const filteredSubnets = response.data.filter(item => item.VpcId === selectedVpcId);
        const filterCIDR = response.data.find(item => item.VpcId === selectedVpcId);
        setFormData({
          ...formData,
          CIDR:filterCIDR.CIDR,
        });
        // Use a Set to ensure uniqueness by SubnetId
        const uniqueSubnets = Array.from(new Set(filteredSubnets.map(subnet => subnet.SubnetId)))
          .map(id => filteredSubnets.find(subnet => subnet.SubnetId === id));
 
        setSubnets(uniqueSubnets); // Store the unique subnets
      })
      .catch((error) => {
        // console.error('Error fetching subnets:', error);
      });
  };
 
  
  // Handle Subnet selection and fetch security groups
  const handleSubnetChange = (selectedSubnetId) => {
    setFormData({ ...formData, subnetId: selectedSubnetId });
    // Update form data with selected subnet
  console.log(formData);
    // Fetch security groups based on the selected subnet
    axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
    .then((response) => {
      

       
      const filteredSecuritygroup = response.data.filter(item => item.SubnetId === selectedSubnetId);
      const filterAz = response.data.find(item => item.SubnetId === selectedSubnetId);
      
        // Update formData with AZ and Available IPs
        setFormData({
          ...formData,
          subnetId:selectedSubnetId,
          az: filterAz.AZ,
          availableIps: filterAz.AvailableIPs,
        });
       
      
      const handlemapmigratedChange = (selectedSubnetId) => {
        setFormData({ ...formData, subnetId: selectedSubnetId }); }
      // Use a Set to ensure uniqueness by SubnetId
      const uniqueSecurityGroup = Array.from(new Set(filteredSecuritygroup.map(sec => sec.SecurityGroupId)))
        .map(id => filteredSecuritygroup.find(sec => sec.SecurityGroupId === id));
      
      setSecurityGroups(uniqueSecurityGroup); // Store the unique subnets
      
      // Automatically select the first subnet if available
      // if (uniqueSecurityGroup.length > 0) {
      //   const firstSubnetId = uniqueSecurityGroup[0].SecurityGroupId;
      //   setFormData(prev => ({ ...prev, subnetId: firstSubnetId })); // Update form data with selected subnet
      //   handleSubnetChange(firstSubnetId); // Fetch security groups for the first subnet
      // }
    })
    .catch((error) => {
      // console.error('Error fetching subnets:', error);
    });
  };
  const handleSecurityGroupChange = (selectedOptions) => {
    setSelectedSecurityGroups(selectedOptions);
    setFormData({ ...formData, securityGroupIds: selectedOptions.map(option => option.value) });
  };
  // Load data from S3 on component mount
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/get-from-s3')
      .then((response) => {
        const allRegions = response.data.results;
        const allInstances = response.data.instances;
  
        // Filter instances based on the platform (e.g., "windows")
        
  
        setRegions(allRegions);  // Set all regions
        setInstances(allInstances);  // Set only instances matching the platform
        
      })
      .catch((error) => {
        // console.error("There was an error fetching the AMI data!", error);
      });
  },[] );

  useEffect(() => {
      console.log("flag: seg");
      axios.get('https://umanage.dev.hidglobal.com/api/vpc-subnet')
        .then((response) => {
          // console.log('Original VPC Data:', response.data);
 
          // Filter VPCs based on Region and AccountId
          const filteredVPCs = response.data.filter(vpc => {
            return (
              vpc.VpcRegion.toLowerCase() === formData.Region.toLowerCase() &&
              vpc.CreateAccountId.toLowerCase() === formData.accountId.toLowerCase()
            );
          });
          
          // Create a map to store unique VPCs by VpcId
          const uniqueVPCsMap = {};
 
          filteredVPCs.forEach(vpc => {
            // Using VpcId as the key for uniqueness
            uniqueVPCsMap[vpc.VpcId] = vpc;
          });
 
          // Convert the map values back to an array
          const uniqueVPCs = Object.values(uniqueVPCsMap);
 
          // console.log('Filtered Unique VPCs:', uniqueVPCs);
          setVpcs(uniqueVPCs);
        })
        .catch((error) => {
          // console.error('Error fetching VPCs:', error);
        });
  }, [formData.Region, formData.accountId]);
  
  

  console.log("VPCs",vpcs);
  
 
 



  
 
  const accountOptions = accounts.map(account => ({
    value: account.CreateAccountId,
    label: account.CreateAccountName,
  }));
 
  // Find the currently selected account in options for the value prop
  const selectedAccount = accountOptions.find(option => option.value === formData.accountId);
 
  const vpcOptions = vpcs.map(vpc => ({
    label: vpc.VpcName,
    value: vpc.VpcId
  }));
 
 
  // Map Subnets to react-select options
  const subnetOptions = subnets.map((subnet) => ({
    value: subnet.SubnetId,
    label: `${subnet.SubnetName} - ${subnet.SubnetId}`, // Customize display text as needed
  }));
 
  const businessSegmentOptions = businessSegments.map(({ segment }) => ({
    value: segment,
    label: segment,
  }));
 
  // Map Business Segment Descriptions to react-select options
  const businessDescriptionOptions = businessDescriptions.map(({ description }) => ({
    value: description,
    label: description,
  }));
 
  const costCenterOptions = costCenters.map(({ center }) => ({
    value: center,
    label: center,
  }));
 
  // Map Cost Center Descriptions to react-select options
  const costCenterDescriptionOptions = costCenters.map(({ description }) => ({
    value: description,
    label: description,
  }));
 
  const instanceSourceOptions = systemTags.instanceSources.map((source) => ({
    value: source,
    label: source,
  }));
 
 
  useEffect(() => {
   
 
    const fetchData = async () => {
      try {
        const response1 = await fetch('https://umanage.dev.hidglobal.com/api/tags', {
          method: 'GET',
        });
        const data1 = await response1.json();
        // console.log(data1);
       
       
        setData1({
          uniqueBusinessAreas: data1.uniqueBusinessAreas || [],
          businessSegmentPairs: data1.businessSegmentPairs || {},
          costCenterPairs: data1.costCenterPairs || {},
        });
        // console.log(data1);
 
       
 
      } catch (error) {
        // console.error('Error fetching data:', error);
      }
    };
 
    fetchData();
 
  }, [step]);
 
  // Handle changes for form inputs
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
 
  // Progress bar width calculation
  const calculateProgressBarWidth = () => `${(step - 1) * 16.6}%`;
 
  const goToNextStep = () => {
    if (step < 7) setStep(step + 1);
  };
 
  const goToPreviousStep = () => {
    if (step > 1) setStep(step - 1);
  };

  const goToStep = (step) => {
    setStep(step); // Assuming you have a state like currentStep
  };
  
  
  
  const defaultDeviceNames = platform === 'windows' 
  ? ['/dev/sda1', 'xvdb', 'xvdc', 'xvdd', 'xdve'] // Values for Windows platform
  : ['/dev/sda1', '/dev/xvdb', '/dev/xvdc', '/dev/xvdd', '/dev/xdve'];
  
  const addVolume = () => {
    if (formData.volumeCount < 5) {
      const newVolumeKey = `volume${formData.volumeCount + 1}`; // Create a new volume key
      const defaultDeviceName = defaultDeviceNames[formData.volumeCount]; // Get the default device name based on volume count
  
      setFormData((prevData) => ({
        ...prevData,
        [newVolumeKey]: { ebsVolumeSize: '1', ebsVolumeType: 'gp3', DeviceName: defaultDeviceName }, // Set default device name
        volumeCount: prevData.volumeCount + 1,
      }));
    }
  };
  
  const handleVolumeChange = (e, volumeNumber) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [`volume${volumeNumber}`]: {
        ...prevData[`volume${volumeNumber}`],
        [name]: value,
      },
    }));
  };
  
  const removeVolume = (volumeNumber) => {
    const newData = { ...formData };
    delete newData[`volume${volumeNumber}`];
  
    for (let i = volumeNumber + 1; i <= formData.volumeCount; i++) {
      newData[`volume${i - 1}`] = newData[`volume${i}`];
      delete newData[`volume${i}`];
    }
  
    setFormData({ ...newData, volumeCount: Math.max(volumeNumber - 1, 1) });
    if (selectedVolume >= volumeNumber) {
      setSelectedVolume((prevSelected) => Math.max(prevSelected - 1, 1)); // Set to the previous volume or 1
    }
  };
  
  const [selectedVolume, setSelectedVolume] = useState(1);

  const handleSubmit = async (e) => {
    setIsProcessing(true);
    console.log("isProcessing"+isProcessing);
    setAlertMessage("Awaiting Process Completion !");
    // console.warn(formData);
    
  
    // console.log("formdata"+formData);
    // console.log(additionalInfo);
    const updatedFormData = {
      ...formData,
      CostCenter: selectedCostCenter,
  CostCenterDescription: selectedCostCenterDescription,
  BusinessArea: selectedBusinessArea,
  SupportTier: selectedSupportTier,
  SupportTierDescription:supportTierDescription ,
  InstanceSource: selectedInstanceSource,
  mapMigrated:additionalInfo.map,
 BusinessContactEmail: additionalInfo._BusinessContactEmail,
  FunctionalArea: selectedFunctionArea,
  BusinessSegment: selectedBusinessSegment,
  BusinessSegmentDescription: selectedBusinessSegmentDescription,
      BusinessContact: additionalInfo._BusinessContact,
      Environment: additionalInfo._Environment,
      NetworkLocation: additionalInfo._NetworkLocation,
      BackupPlan: additionalInfo._BackupPlan,
      TechnicalContact: additionalInfo._TechnicalContact,
      TechnicalContactEmail: additionalInfo._TechnicalContactMail,
      ProvisioningEntity: additionalInfo._ProvisionEntity,
      ProvisioningJustification: additionalInfo._ProvisionJustification,
      from:'without',
      platform:platform ,
      isolated:'false',
      adminUser:adminUser,
      adminGroup:adminGroup,
    };

    let hasEmptyValues = false;
  console.log(updatedFormData);

// Loop through updatedFormData and check for empty values, ignoring specified keys

  if ( updatedFormData.CostCenter === '' ||
    updatedFormData.CostCenterDescription === '' ||
    updatedFormData.BusinessArea === '' ||
    updatedFormData.SupportTier === '' ||
    updatedFormData.InstanceType === '' ||
    updatedFormData.BusinessContactEmail === '' ||
    updatedFormData.FunctionalArea === '' ||

    updatedFormData.BusinessSegment === '' ||
    updatedFormData.BusinessContact === '' ||
    updatedFormData.Environment === '' ||
    
    updatedFormData.BackupPlan === '' ||
    updatedFormData.TechnicalContact === '' ||
    updatedFormData.ProvisioningEntity === '' ||
  
    updatedFormData.InstanceName===''||
    updatedFormData.securityGroupIds.length === 0) {
    
      
      hasEmptyValues = true;
      // Exit the loop if any non-ignored value is empty
    
  }


if (hasEmptyValues) {
  // console.log(updatedFormData.securityGroupIds);
  // console.log(updatedFormData.InstanceName);
  setMessagestatus(false);
  setMessage('Error: Please fill in all required fields . please refresh the page ');
  return; // Exit early if there are empty fields
}



// Stream the response in chunks

    
    try {
      const response = await fetch('/api/create-pipeline', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedFormData),
      });
      const reader = response.body.getReader();
const decoder = new TextDecoder();
let receivedText = "";

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  receivedText = decoder.decode(value, { stream: true });
  console.log(receivedText);
  setAlertMessage(receivedText); // Append new chunks to message
  if(receivedText.substring(0,4)=="Succ")
    { 
      setAlertMessage("");
      
      setMessagestatus(true);
    }
  if(receivedText.substring(0,5)=="Error"){
    setAlertMessage("");
    //setMessage(data.message);
    setMessagestatus(false);
  }

}
     
    } catch (err) {
      setMessage(`Error: ${err.message}`);
    }
  };
  useEffect(() => {
    console.log("isProcessing changed:", isProcessing);
  }, [isProcessing]);
  const handleToggleChange = (e) => {
    setManualAmi(e.target.value === 'yes');
    // Reset AMI when switching between input and dropdown
    setFormData({ ...formData, AMI: '' });
  };
  console.log("Status"+messagestatus);
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/tags')
      .then(response => {
        const { systemTags } = response.data;
        setSystemTags(systemTags);
      })
      .catch(error => {
        // console.error("There was an error fetching system tags!", error);
      });
  }, []);
 
  // Handle SupportTier change
  const handleSupportTierChange = (e) => {
    const selectedTier = e.target.value;
    setSelectedSupportTier(selectedTier);
 
    // Find and set the description based on the selected SupportTier
    const tierPair = systemTags.supportTierPairs.find(pair => pair._SupportTier === selectedTier);
    if (tierPair) {
      setSupportTierDescription(tierPair._SupportTierDescription);
    } else {
      setSupportTierDescription('');
    }
  };
 
  // Handle InstanceSource change
  const handleInstanceSourceChange = (e) => {
    setSelectedInstanceSource(e.target.value);
  };
  const [showTooltip, setShowTooltip] = useState(false); // Tooltip for Instance Name
  const [showAMITooltip, setShowAMITooltip] = useState(false);
  const [showInstanceTooltip, setShowInstanceTooltip] = useState(false);
  const [showIAMTooltip, setShowIAMTooltip] = useState(false); 
  const [showVolumeTooltip, setshowVolumeTooltip] = useState(false); 
  const [showTicketTooltip, setshowTicketTooltip] = useState(false); 
  const [showBackupTooltip, setshowBackupTooltip] = useState(false); 
  const [showVPCTooltip, setShowVPCTooltip] = useState(false); 
  const [showSubnetTooltip, setShowSubnetTooltip] = useState(false); 
  const [showSGTooltip, setShowSGTooltip] = useState(false); 
  const renderStep = () => {
    switch (step) {
      case 1: // Select Account
        return (
          <div className="form-section">
            <div className="form-section">
            <label>Select Account:</label>
      <Select
        className="re-select"
        value={selectedAccount || null} // Display the selected account
        onChange={handleAccountChange} // Handle account change
        options={accountOptions}// Map accounts to options
        isSearchable={true} // Enable search functionality
        placeholder="Search or select an account"
      />
</div>
 
          </div>
        );
      case 2: // Configuration (VPC, AMI, Instance Type, Key Pair)
        return (
          <div className="form-section">
  {/* Instance Name Input */}
  <div className="row">
  <div className="col-md-6 form-group">
    <label htmlFor="instanceName" className="label-with-icon">Enter Application Code:</label>
     

    <p className="form-text text-muted">
    Use three characters for segmented servers (e.g., "JEN") 
    <a href="https://umanage.dev.hidglobal.com/help/AboutCreate" target="_blank" rel="noopener noreferrer">Know more</a>.
    </p>
      <input
        id="instanceName"
        type="text"
        placeholder="Instance Name"
        value={appCode}
        onChange={(e) => setAppCode(e.target.value.toUpperCase().slice(0, 5))} 
      />
    </div>
    <div className="col-md-6 form-group">
      <label htmlFor="region"className="break">Select Region:</label>


      <p className="form-text text-muted">Choose the AWS region where the instance will be deployed.</p>
      <select
        id="region"
        value={formData.Region}
        className="form-control"
        onChange={handleRegionChange}
      >
    <option value="">Select a Region</option>
    <option value="us-east-1">us-east-1 - N. Virginia</option>
    <option value="ap-south-1">ap-south-1 - Mumbai</option>
    <option value="eu-central-1">eu-central-1 - Frankfurt</option>
      </select>
    </div>
  </div>
  {/* Region and Architecture in the same row */}
  

  {/* AMI Manual Toggle */}
  <div className="row">
    <div className="col-md-12 form-group">
      <label>Do you want to enter the AMI ID manually?</label>
      
      <div class="switch">
    <input name="amiToggle" id="yes" type="radio" value="yes" checked={manualAmi} onChange={handleToggleChange} />
    <input name="amiToggle" id="no" type="radio" value="no" checked={!manualAmi} onChange={handleToggleChange} />
    
    <div class="switch__indicator">
        <span class="tick"></span>
        <span class="cross"></span>
    </div>
    <label for="yes" class="switch__label_yes"> yes</label> 
    <label for="no" class="switch__label_no">no</label>
</div>


    </div>
  </div>

  {/* AMI and Instance dropdowns (conditionally rendered based on manualAmi) */}
  {manualAmi ? (
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="customAmi" className="label-with-icon">Enter Custom AMI ID:</label>
        
          
          <p className="form-text text-muted">Manually provide the AMI ID for the instance from AWS Marketplace (e.g., ami-xxxxxxxxxxxxxx).</p>
        <input
          type="text"
          id="customAmi"
          className="form-control"
          placeholder="Enter AMI ID"
          value={formData.AMI}
          onChange={handleAMIChange}
        />
      </div>

      <div className="col-md-6 form-group">
        <label htmlFor="compatibleInstance" className="label-with-icon">Select Compatible Instance:</label>
        
      <p>Instance types are based on vCPUs and memory. <a href="https://umanage.dev.hidglobal.com/help/InstanceType" target="_blank" rel="noopener noreferrer">Know more</a>.</p>
        <input
          type="text"
          id="customInstance"
          placeholder="Enter Instance ID"
          value={formData.InstanceType} // Use the same formData.AMI
          onChange={handleCustomAMIInstanceTypeChange} // Same handler for input field
        />
      </div>
    </div>
  ) : 
    
    (
    
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="ami" className="label-with-icon">Select AMI:</label>
        
        <p className="form-text text-muted">Choose an AMI from the list of available options.</p>
        <select
          id="ami"
          value={formData.AMI}
          className="form-control"
          onChange={handleAMIChange}
        >
          <option value="">Select an AMI</option>
          {filteredAMIs.map((ami, index) => (
            <option key={index} value={ami.ImageId}>
              {ami.Name}
            </option>
          ))}
        </select>
      </div>

      <div className="col-md-6 form-group">
        <label htmlFor="compatibleInstance" className="label-with-icon">Select Compatible Instance:</label>
       
      <p>Instance types are based on vCPUs and memory. <a href="https://umanage.dev.hidglobal.com/help/InstanceType" target="_blank" rel="noopener noreferrer">Know more</a>.</p>
        <Select
          id="compatibleInstance"
          className="re-select"
          value={compatibleInstanceOptions.find(option => option.value === formData.InstanceType)}
          // Use formData.InstanceType to set the selected value
          options={compatibleInstanceOptions}
          onChange={handleInstanceTypeChange}
          isSearchable={true}
          placeholder="Search or select a compatible instance"
        />
      </div>
    </div>
  )}

  {/* IAM Role selection */}
 
</div>


        );

        // Admin User and Groups 
      case 3: // Admin
      return(
        <div className="form-section">
  {/* Admin Users Section */}
  <div className="col-md-12">
  <h2 className="admin-users-heading">ADMIN USERS</h2>
  <p className="admin-users-description">Enter your AD Username. For example: prasri.</p>
    <form>
      <div className="row">
        <div className="col-md-8 form-group">
          <label htmlFor="adminUser1" className="label-with-icon">Admin User 1:</label>
          <input
            type="text"
            id="adminUser1"
            name="adminUser1"
            className="form-control"
            value={adminUser.adminUser1}
            onChange={(e) => handleAdminUserChange('adminUser1', e.target.value)}
          />
        </div>
        <div className="col-md-4 form-group">
          {adminUser.adminUser4 === null ? (
            <button
              type="button"
              className="btn btn-add"
              onClick={() => handleAddAdminUser('adminUser2')}
            >
              <FaPlus />
            </button>
          ) : (
            <div></div>
          )}
        </div>
      </div>

      {adminUser.adminUser2 !== null && (
        <div className="row">
          <div className="col-md-8 form-group">
            <label htmlFor="adminUser2" className="label-with-icon">Admin User 2:</label>
            <input
              type="text"
              id="adminUser2"
              name="adminUser2"
              className="form-control"
              value={adminUser.adminUser2}
              onChange={(e) => handleAdminUserChange('adminUser2', e.target.value)}
            />
          </div>
          <div className="col-md-4 form-group">
            {adminUser.adminUser2 !== null && (
              <button
                type="button"
                className="btn btn-remove"
                onClick={() => handleRemoveAdminUser('adminUser2')}
              >
                <FaXmark />
              </button>
            )}
          </div>
        </div>
      )}

      {adminUser.adminUser3 !== null && (
        <div className="row">
          <div className="col-md-8 form-group">
            <label htmlFor="adminUser3" className="label-with-icon">Admin User 3:</label>
            <input
              type="text"
              id="adminUser3"
              name="adminUser3"
              className="form-control"
              value={adminUser.adminUser3}
              onChange={(e) => handleAdminUserChange('adminUser3', e.target.value)}
            />
          </div>
          <div className="col-md-4 form-group">
          {adminUser.adminUser3 !== null && (
              <button
                type="button"
                className="btn btn-remove"
                onClick={() => handleRemoveAdminUser('adminUser3')}
              >
                <FaXmark />
              </button>
            )}
          </div>
        </div>
      )}

      {adminUser.adminUser4 !== null && (
        <div className="row">
          <div className="col-md-8 form-group">
            <label htmlFor="adminUser4" className="label-with-icon">Admin User 4:</label>
            <input
              type="text"
              id="adminUser4"
              name="adminUser4"
              className="form-control"
              value={adminUser.adminUser4}
              onChange={(e) => handleAdminUserChange('adminUser4', e.target.value)}
            />
          </div>
          <div className="col-md-4 form-group">
            <button
              type="button"
              className="btn btn-remove"
              onClick={() => handleRemoveAdminUser('adminUser4')}
            >
              <FaXmark />
            </button>
          </div>
        </div>
      )}
    </form>
  </div>

  {/* Admin Groups Section */}
  <div className="col-md-12">
  <h2 className="admin-users-heading">ADMIN GROUP</h2>
  <p className="admin-users-description">Enter your Group Name. Example: G-HID-EIT-AWS-GDAIS-AD-Admins</p>
    <form>
      <div className="row">
        <div className="col-md-8 form-group">
          <label htmlFor="adminGroup1" className="label-with-icon">Admin Group 1:</label>
          <input
            type="text"
            id="adminGroup1"
            name="adminGroup1"
            className="form-control"
            value={adminGroup.adminGroup1}
            onChange={(e) => handleAdminGroupChange('adminGroup1', e.target.value)}
          />
        </div>
        <div className="col-md-4 form-group">
          {adminGroup.adminGroup2 === null ? (
            <button
              type="button"
              className="btn btn-add"
              onClick={() => handleAddAdminGroup('adminGroup2')}
            >
              <FaPlus />
            </button>
          ) : (
           <></>
          )}
        </div>
      </div>

      {adminGroup.adminGroup2 !== null && (
        <div className="row">
          <div className="col-md-8 form-group">
            <label htmlFor="adminGroup2" className="label-with-icon">Admin Group 2:</label>
            <input
              type="text"
              id="adminGroup2"
              name="adminGroup2"
              className="form-control"
              value={adminGroup.adminGroup2}
              onChange={(e) => handleAdminGroupChange('adminGroup2', e.target.value)}
            />
          </div>
          <div className="col-md-4 form-group">
            <button
              type="button"
              className="btn btn-remove"
              onClick={() => handleRemoveAdminGroup('adminGroup2')}
            >
              <FaXmark />
            </button>
          </div>
        </div>
      )}
    </form>
  </div>
</div>
      );
      case 4: // Storage
        return (
          <div className="form-section">
  {formData.createOptionalVolume === 'no' && (
    <div className="disclaimer-root-volume-config">
    <ul>
      <li>
        <strong>Default Root Volume Configuration for New Provisioned Instances:</strong>
      </li>
      <ul>
        <li>
          <strong>Size:</strong> 50 GiB
        </li>
        <li>
          <strong>Volume Type:</strong> General Purpose SSD (gp3)
        </li>
        <li>
          <strong>Mount Point:</strong> /dev/sda1
        </li>
      </ul>
      <li>
        You can customize the default settings by overriding the size, type, and mount point for Volume 1, allowing for flexibility to meet your specific storage requirements.
      </li>
    </ul>
  </div>
  )}    
  <label className="label-with-icon">Do you want to add storage?</label>
 
    
      
  <div className="switch">
    <input 
        name="createOptionalVolume" 
        id="createOptionalVolumeYes" 
        type="radio" 
        value="yes" 
        checked={formData.createOptionalVolume === 'yes'} 
        onChange={() => setFormData({ ...formData, createOptionalVolume: 'yes' })} 
    />
    <input 
        name="createOptionalVolume" 
        id="createOptionalVolumeNo" 
        type="radio" 
        value="no" 
        checked={formData.createOptionalVolume === 'no'} 
        onChange={() => setFormData({ ...formData, createOptionalVolume: 'no' })} 
    />
    
    <div className="switch__indicator">
        <span className="tick"></span>
        <span className="cross"></span>
    </div>
    <label htmlFor="createOptionalVolumeYes" className="switch__label_yes">Yes</label>
    <label htmlFor="createOptionalVolumeNo" className="switch__label_no">No</label>
</div>


  {formData.createOptionalVolume === 'yes' && (
    <div>
      {/* Volume Tabs */}
      <div className="tab-bar">
        {Array.from({ length: formData.volumeCount }).map((_, index) => (
          <div
            key={index}
            className={`tab ${selectedVolume === index + 1 ? 'active' : ''}`}
            onClick={() => setSelectedVolume(index + 1)}
          >
            {`Volume ${index + 1}`}
            {index > 0 && (
              <span
                className="remove-tab"
                onClick={(e) => {
                  e.stopPropagation(); // Prevent tab click event
                  removeVolume(index + 1); // Use index + 1 to match volume number
                }}
              >
                ×
              </span>
            )}
          </div>
        ))}
        {formData.volumeCount < 5 && (
          <div className="tab add-volume" onClick={addVolume}>
            +
          </div>
        )}
      </div>

      <div className="storage-options">
        {/* Volume Configuration for the selected volume */}
        <div className="volume-config">
          <h4>{`Volume ${selectedVolume}`}</h4>
          <label>EBS Volume Size:</label>
          <p className="form-text text-muted">
      Specify the size of the EBS volume in GiB (Gigabytes). The default minimum size is 1 GiB.
    </p>
    <input
      type="number"
      name="ebsVolumeSize"
      className="form-control"
      value={formData[`volume${selectedVolume}`].ebsVolumeSize || ''}
      onChange={(e) => handleVolumeChange(e, selectedVolume)}
      placeholder="Enter size"
      min="1"
      step="1"
    />
   
         <label>EBS Volume Type:</label>
<p className="form-text text-muted">
  Choose the type of EBS volume. For general purposes, 'gp3' are recommended.
</p>
<select
  name="ebsVolumeType"
  className="form-control"
  value={formData[`volume${selectedVolume}`].ebsVolumeType || 'gp3'} // Default to gp3
  onChange={(e) => handleVolumeChange(e, selectedVolume)}
>
  {[
    { value: 'gp2', description: 'General Purpose SSD (gp2)' },
    { value: 'io1', description: 'Provisioned IOPS SSD (io1)' },
    { value: 'gp3', description: 'General Purpose SSD (gp3)' },
    { value: 'io2', description: 'Provisioned IOPS SSD (io2)' },
    { value: 'st1', description: 'Throughput Optimized HDD (st1)' },
    { value: 'sc1', description: 'Cold HDD (sc1)' },
    { value: 'magnetic', description: 'Magnetic' },
  ].map((type, index) => (
    <option key={index} value={type.value}>
      {type.description}
    </option>
  ))}
</select>


          <label>Device Name:</label>
          <p className="form-text text-muted">
      Specify the device name for this volume 
    </p>
    <input
  type="text"
  name="DeviceName"
  className="form-control"
  value={
    formData[`volume${selectedVolume}`].DeviceName !== ''
      ? formData[`volume${selectedVolume}`].DeviceName
      : defaultDeviceNames[selectedVolume - 1]
  }
  onChange={(e) => handleVolumeChange(e, selectedVolume)}
  placeholder="e.g., /dev/sda1"
/>
        </div>
      </div>
    </div>
  )}
</div>

        );
      case 5: // Network and Security
        return (
          <div className="form-section">
  {/* VPC Selection */}
  


  <div>
    <div className="row">
    <div className="col-md-6 form-group">
    <label className="label-with-icon">Select VPC:</label>
    
  <p className="form-text text-muted">
    Select a VPC to define your network boundary. <strong>[BusinessArea]-[Region]-segmented-vpc</strong> allows only approved traffic.
  </p>
    <Select
      name="vpcId"
      className="re-select"
      value={vpcOptions.find(option => option.value === formData.vpcId)} // Find the selected VPC
      onChange={(selectedOption) => {
        handleVpcChange(selectedOption.value); // Call handleVpcChange with selected VPC ID
      }}
      options={vpcOptions}
      isSearchable={true}
      placeholder="Search or select a VPC"
    />
    </div>
    <div className="col-md-6 form-group">
          <label>CIDR Block:</label>
          <p className="form-text text-muted">Shows the respective CIDR Block.</p>
          <input type="text" className="form-control" value={formData.CIDR} readOnly disabled />
          </div>
          </div>
  
    
    <label className="label-with-icon">Select Subnet:</label>
    
    <p className="form-text text-muted">
    Select a subnet within the chosen VPC based on the number of <strong>available IPs </strong>and its associated <strong>Availability Zone (AZ)</strong> to ensure proper allocation for your instance.
  </p>
    <Select
      name="subnetId"
      className="re-select"
      value={subnetOptions.find(option => option.value === formData.subnetId)} // Find the selected Subnet
      onChange={(selectedOption) => handleSubnetChange(selectedOption.value)} // Handle Subnet change
      options={subnetOptions}
      isSearchable={true}
      placeholder="Search or select a Subnet"
    />
    <div className="row">
    <div className="col-md-6 form-group">
          <label>Availability Zone:</label>
          <input type="text" className="form-control" value={formData.az} readOnly disabled />
          </div>
          <div className="col-md-6 form-group">
          <label className="mt-2">Available IPs:</label>
          <input type="text" className="form-control" value={formData.availableIps} readOnly disabled />
          </div>
        </div>
  
    
    <label className="label-with-icon">Select Security Group:</label>
   
  <p className="form-text text-muted">
    Select one or more security groups to control traffic to your instance. The preferred security groups are <strong>application-sg</strong> and <strong>management-sg</strong>. If none of these apply, use the <strong>default security group</strong>.
  </p>
  <Select
        name="securityGroupIds"
        className="re-select"
        value={selectedSecurityGroups} // Track selected Security Groups
        onChange={handleSecurityGroupChange} // Handle selection
        options={securityGroups.map(sg => ({
          label: `${sg.SecurityGroupName} (${sg.SecurityGroupId})`, // Show both name and ID
          value: sg.SecurityGroupId
        }))} // Map Security Group data to options
        isMulti // Enable multi-selection
        isSearchable={true}
        placeholder="Search or select Security Groups"
      />
    </div>

</div>

        );
      case 6: //Tags
        return(
          <div className="form-container">
             <div className="scrollable-tags-container">
  {/* Business Section */}
  <div className="form-section business-info">
    <h5><strong>Business Tags</strong></h5>
    <div className="row">
      <div className="col-md- form-group">
      <label htmlFor="businessArea">Business Area <span className="required">*</span></label>
      <p className="form-text text-muted">
          Select the business area that corresponds to this project.
        </p>
        <select
          id="businessArea"
          value={selectedBusinessArea}
          className="form-control"
          onChange={handleBusinessAreaChange}
        >
          <option value="">Select Business Area</option>
          {data1.uniqueBusinessAreas.map(area => (
            <option key={area} value={area}>{area}</option>
          ))}
        </select>
      </div>
    </div>
 
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="businessSegment">Business Segment <span className="required">*</span></label>
        <p className="form-text text-muted">
          Choose the relevant business segment for this task.
        </p>
        <Select
        id="businessSegment"
        className="re-select"
        value={businessSegmentOptions.find(option => option.value === selectedBusinessSegment)} // Find the selected segment
        onChange={(selectedOption) => handleBusinessSegmentChange({ target: { value: selectedOption.value }})} // Handle change
        options={businessSegmentOptions}
        isSearchable={true}
        placeholder="Search or select Business Segment"
       
      />
      </div>
 
      <div className="col-md-6 form-group">
        <label htmlFor="businessSegmentDescription">Business Segment Description <span className="required">*</span></label>
        <p className="form-text text-muted">
          Provide a description of the selected business segment.
        </p>
        <Select
        id="businessSegmentDescription"
        className="re-select"
        value={businessDescriptionOptions.find(option => option.value === selectedBusinessSegmentDescription)} // Find the selected description
        onChange={(selectedOption) => handleBusinessSegmentDescriptionChange({ target: { value: selectedOption.value }})} // Handle change
        options={businessDescriptionOptions}
        isSearchable={true}
        placeholder="Search or select Business Segment Description"
       
      />
      </div>
      </div>    
         
  </div>
 
  {/* Cost Center Section */}
  <div className="form-section cost-center-info">
  <h5><strong>Cost Tags</strong></h5>
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="costCenter">Cost Center <span className="required">*</span></label>
        <p className="form-text text-muted">
          Select the cost center related to this project.
        </p>
        <Select
        id="costCenter"
        className="re-select"
        value={costCenterOptions.find(option => option.value === selectedCostCenter)} // Find the selected cost center
        onChange={(selectedOption) => handleCostCenterChange({ target: { value: selectedOption.value }})} // Handle change
        options={costCenterOptions}
        isSearchable={true}
        placeholder="Search or select Cost Center"
       
      />
      </div>
 
      <div className="col-md-6 form-group">
        <label htmlFor="costCenterDescription">Cost Center Description <span className="required">*</span></label>
        <p className="form-text text-muted">
          Provide a description for the selected cost center.
        </p>
        <Select
        id="costCenterDescription"
        className="re-select"
        value={costCenterDescriptionOptions.find(option => option.value === selectedCostCenterDescription)} // Find the selected cost center description
        onChange={(selectedOption) => handleCostCenterDescriptionChange({ target: { value: selectedOption.value }})} // Handle change
        options={costCenterDescriptionOptions}
        isSearchable={true}
        placeholder="Search or select Cost Center Description"
       
      />
      </div>
    </div>
 
    {/* <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="functionArea">Function Area <span className="required">*</span></label>
        <p className="form-text text-muted">
          The functional area for the selected cost center.
        </p>
        <input
          type="text"
          id="functionArea"
          className="form-control"
          value={selectedFunctionArea}
          readOnly
        />
      </div>
    </div> */}
  </div>
 
  {/* Region & Environment Section */}
  <div className="form-section region-environment-info">
  <h5><strong>System Tags</strong></h5>
    <div className="row">
     
 
      <div className="col-md-6 form-group">
        <label htmlFor="environment" className="break">Environment <span className="required">*</span></label>
        <p className="form-text text-muted">
          Choose the environment for this deployment.
        </p>
        <select
          id="environment"
          name="_Environment"
          value={additionalInfo._Environment}
          className="form-control"
          onChange={handleEnvPlanChange}
        >
          <option value="">Select Environment</option>
          <option value="SBX">Sandbox</option>
          <option value="DEV">Development</option>
          <option value="SUS">Sustaining</option>
          <option value="SUP">Support</option>
          <option value="AQ1">Acquisitions 1</option>
          <option value="AQ2">Acquisitions 2</option>
          <option value="CIN">Customer Integration</option>
          <option value="UAT">User Acceptance Testing</option>
          <option value="LIV">Live (Production)</option>

        </select>
        {envPlanLabel && <span className="backup-plan-label">{envPlanLabel}</span>}
      </div>
 
      <div className="col-md-6 form-group">
      <label htmlFor="backupPlan" className="label-with-icon">
        Backup Plan <span className="required">*</span>
        
      </label>
      <p className="form-text text-muted">
        Select a backup plan for data protection. <a href="#" onClick={togglePopup}>Know more</a>
      </p>

      {/* Popup Content */}
      {showPopup && (
        <div className="popup-overlay">
          <div className="popup-content">
          <button className="btn btn-close" onClick={togglePopup}></button>
            <h4>What are the Backups SLA details? </h4>
            <p>
              Below is the SLA frequency and retention policy for different service tiers:
            </p>
            <table border="1" cellpadding="8" cellspacing="0" style={{ borderCollapse: 'collapse', width: '100%' }}>
              <thead>
                <tr>
                  <th>Backupplan Name</th>
                  <th>Frequency</th>
                  <th>Retention</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Platinum</td>
                  <td>
                    Daily (4 times/day)<br />
                    Monthly (1 time/month)<br />
                    Yearly (1 time/year)
                  </td>
                  <td>
                    10 days<br />
                    12 months<br />
                    3 years
                  </td>
                </tr>
                <tr>
                  <td>Gold</td>
                  <td>
                    Daily (1 time/day)<br />
                    Weekly (every week)<br />
                    Monthly (1 time/month)
                  </td>
                  <td>
                    6 days<br />
                    1 month<br />
                    2 months
                  </td>
                </tr>
                <tr>
                  <td>Silver</td>
                  <td>
                    Daily (1 time/day)<br />
                    Weekly (every week)
                  </td>
                  <td>
                    6 days<br />
                    2 weeks
                  </td>
                </tr>
                <tr>
                  <td>Bronze</td>
                  <td>
                    Weekly (every week)
                  </td>
                  <td>
                    2 weeks
                  </td>
                </tr>
              </tbody>
            </table>
            
          </div>
        </div>
      )}
        <select
          id="backupPlan"
          name="_BackupPlan"
          value={additionalInfo._BackupPlan}
          className="form-control"
          onChange={handleBackupPlanChange}
        >
          <option value="">Select Backup Plan</option>
          <option value="PLATINUM">PLATINUM</option>
          <option value="GOLD">GOLD</option>
          <option value="SILVER">SILVER</option>
          <option value="BRONZE">BRONZE</option>
          <option value="N/A">N/A</option>
        </select>
       
      </div>
 
    </div>
    <div className="row">
      <div className="col-md-6 form-group">
      <label>Support Tier <span className="required">*</span></label>
      <p className="form-text text-muted">
          Choose the appropriate support tier for this system.
        </p>
        <select value={selectedSupportTier} onChange={handleSupportTierChange} className="form-control">
          <option value="">Select Support Tier</option>
          {systemTags.supportTiers.map((tier, index) => (
            <option key={index} value={tier}>{tier}</option>
          ))}
        </select>
      </div>
 
      <div className="col-md-6 form-group">
     
        <div>
          <label htmlFor="functionArea">Support Tier Description</label>
          <p className="form-text text-muted">
          Description of the selected support tier.
        </p>
          <input
            type="text"
            id="functionArea"
            className="form-control"
            value={supportTierDescription}
            readOnly
          />
         
        </div>
     
      </div>
    </div>   
    {/* <div className="row">
     
 
     <div className="col-md-6 form-group">
       <label htmlFor="mapmigrated" className="break">Map-Migrated <span className="required">*</span></label>
       <p className="form-text text-muted">
         Choose the map migration for this deployment.
       </p>
       <input
          type="text"
          id="mapmigrated"
          name="map"
          value="migSITGOMR8R2"
          className="form-control"
          readOnly
    />
       
     </div>
     </div>    */}
   
  </div>
 
  {/* Contact Information Section */}
  <div className="form-section contact-info">
  <h5><strong>Contact Tags</strong></h5>
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="businessContact">Business Contact</label>
        <p className="form-text text-muted">
          Enter the name of the business contact for this request.
        </p>
        <input
          id="businessContact"
          name="_BusinessContact"
          type="text"
          className="form-control"
          value={additionalInfo._BusinessContact}
          onChange={handleAdditionalInfoChange}
          placeholder="Ex : Prasana Srinivasan"
        />
      </div>
 
      <div className="col-md-6 form-group">
        <label htmlFor="BusinessContactMail">Business Contact Email</label>
        <p className="form-text text-muted">
          Enter the email of the business contact.
        </p>
        <input
          id="BusinessContactMail"
          name="_BusinessContactEmail"
          type="text"
          value={additionalInfo._BusinessContactEmail}
          className="form-control"
          onChange={handleAdditionalInfoChange}
          required
          placeholder="Ex : <EMAIL>"
        />
      </div>
    </div>
 
    <div className="row">
      <div className="col-md-6 form-group">
        <label htmlFor="TechnicalContact">Technical Contact</label>
        <p className="form-text text-muted">
        Enter the name of the technical contact for this request.
        </p>
        <input
          id="TechnicalContact"
          name="_TechnicalContact"
          type="text"
          className="form-control"
          value={additionalInfo._TechnicalContact}
          onChange={handleAdditionalInfoChange}
          placeholder="Ex : Abhiram Pabbisetty"
        />
      </div>
 
      <div className="col-md-6 form-group">
        <label htmlFor="TechnicalContactMail">Technical Contact Mail</label>
        <p className="form-text text-muted">
          Enter the email of the technical contact.
        </p>
        <input
          id="TechnicalContactMail"
          name="_TechnicalContactMail"
          type="text"
          className="form-control"
          value={additionalInfo._TechnicalContactMail}
          onChange={handleAdditionalInfoChange}
          placeholder="Ex : <EMAIL>"
        />
      </div>
    </div>
  </div>
 
   
  </div>
  </div>
 
                   
        );
      case 7: // Summary
        return (
          <div className="summary-section">
            <div className="scrollable-tags-container">
      <h3>Summary</h3>
      <div className="summary-box">
        {/* Account Information */}
        

        {/* Config Information */}
        <h4 onClick={() => toggleSection('configuration')} className="summary-heading">
          Configuration {expandedSections.configuration ? '▲' : '▼'}
        </h4>
        {expandedSections.configuration && (
          <div className="summary-content"  onClick={() => goToStep(2)}>
            <div className="summary-row">
              <div className="summary-item">
                <p><strong>Account ID:</strong> {formData.accountId}</p>
              </div>
              <div className="summary-item">
                <p><strong>Region :</strong> {formData.Region}</p>
              </div>
            </div>
            <div className="summary-row">
              <div className="summary-item">
                <p><strong>Instance Name:</strong> {formData.InstanceName}</p>
              </div>
              <div className="summary-item">
                <p><strong>AMI:</strong> {formData.AMI}</p>
              </div>
            </div>
            <div className="summary-row">
              <div className="summary-item">
                <p><strong>Instance Type:</strong> {formData.InstanceType}</p>
              </div>
              
            </div>
          </div>
        )}
        {formData.createOptionalVolume === 'yes'  && (
        <div>
        {/* Storage Information */}
        <h4 onClick={() => toggleSection('storage')} className="summary-heading">
  Storage {expandedSections.storage ? '▲' : '▼'}
</h4>
{ expandedSections.storage && (
  <div className="summary-content" onClick={() => goToStep(3)}>
    
      

      {/* Volume 1 should always be shown if createOptionalVolume is 'yes' */}
      { formData.volume1 && (
        <div className="summary-row">
          <div className="summary-item">
            <p><strong>Volume 1 Size:</strong> {formData.volume1.ebsVolumeSize}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 1 Type:</strong> {formData.volume1.ebsVolumeType}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 1 Device Name:</strong> {formData.volume1.DeviceName}</p>
          </div>
          </div>
        
      )}

      {/* Conditionally show Volume 2 if it is not null */}
      { formData.volume2 && (
        <>
         <div className="summary-row">
          <div className="summary-item">
            <p><strong>Volume 2 Size:</strong> {formData.volume2.ebsVolumeSize}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 2 Type:</strong> {formData.volume2.ebsVolumeType}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 2 Device Name:</strong> {formData.volume2.DeviceName}</p>
          </div>
          </div>
        </>
      )}

      {/* Conditionally show Volume 3 if it is not null */}
      { formData.volume3 && (
        <>
         <div className="summary-row">
          <div className="summary-item">
            <p><strong>Volume 3 Size:</strong> {formData.volume3.ebsVolumeSize}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 3 Type:</strong> {formData.volume3.ebsVolumeType}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 3 Device Name:</strong> {formData.volume3.DeviceName}</p>
          </div>
          </div>
        </>
      )}

      {/* Conditionally show Volume 4 if it is not null */}
      {formData.volume4 && (
        <>
         <div className="summary-row">
          <div className="summary-item">
            <p><strong>Volume 4 Size:</strong> {formData.volume4.ebsVolumeSize}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 4 Type:</strong> {formData.volume4.ebsVolumeType}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 4 Device Name:</strong> {formData.volume4.DeviceName}</p>
          </div>
          </div>
        </>
      )}

      {/* Conditionally show Volume 5 if it is not null */}
      { formData.volume5 && (
        <>
         <div className="summary-row">
          <div className="summary-item">
            <p><strong>Volume 5 Size:</strong> {formData.volume5.ebsVolumeSize}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 5 Type:</strong> {formData.volume5.ebsVolumeType}</p>
          </div>
          <div className="summary-item">
            <p><strong>Volume 5 Device Name:</strong> {formData.volume5.DeviceName}</p>
          </div>
          </div>
        </>
      )}
    </div>
 
)}

</div> )}


        {/* Network & Security Information */}
        <h4 onClick={() => toggleSection('network')} className="summary-heading">
          Network & Security {expandedSections.network ? '▲' : '▼'}
        </h4>
        {expandedSections.network && (
          <div className="summary-content"  onClick={() => goToStep(4)}>
            
            <div className="summary-row">
              
             
              <div className="summary-item">
                <p><strong>Subnet ID:</strong> {formData.subnetId}</p>
              </div>
              <div className="summary-item">
                <p><strong>Security Group Id:</strong> {formData.securityGroupIds}</p>
              </div>
            </div>
            
            
          </div>
        )}

        {/* Tags Information */}
        <h4 onClick={() => toggleSection('tags')} className="summary-heading">
          Tags {expandedSections.tags ? '▲' : '▼'}
        </h4>
        {expandedSections.tags && (
          <div className="summary-content"  onClick={() => goToStep(5)}>
            
            <div className="summary-section">
 
  <div className="summary-box">
    {/* Business Tags Section */}
    <h4>Business Tags</h4>
    <div className="summary-row">
      <div className="summary-item">
        <p><strong>Business Area:</strong> {selectedBusinessArea}</p>
      </div>
      <div className="summary-item">
        <p><strong>Business Segment:</strong> {selectedBusinessSegment}</p>
      </div>
    </div>
    <div className="summary-row">
      <div className="summary-item">
        <p><strong>Business Segment Description:</strong> {selectedBusinessSegmentDescription}</p>
      </div>
      <div className="summary-item">
        <p><strong>Cost Center:</strong> {selectedCostCenter}</p>
      </div>
    </div>
    <div className="summary-row">
      <div className="summary-item">
        <p><strong>Cost Center Description:</strong> {selectedCostCenterDescription}</p>
      </div>
      
    </div>

    {/* System Tags Section */}
    <h4>System Tags</h4>
    <div className="summary-row">
      <div className="summary-item">
        <p><strong>Environment:</strong> {additionalInfo._Environment}</p>
      </div>
      <div className="summary-item">
        <p><strong>Backup Plan:</strong> {additionalInfo._BackupPlan}</p>
      </div>
    </div>
    <div className="summary-row">
      <div className="summary-item">
        <p><strong>Network Location:</strong> {additionalInfo._NetworkLocation}</p>
      </div>
      <div className="summary-item">
        <p><strong>Instance Source:</strong> NEW</p>
      </div>
    </div>
    <div className="summary-row">
      <div className="summary-item">
        <p><strong>Support Tier:</strong> {selectedSupportTier}</p>
      </div>
      <div className="summary-item">
        <p><strong>Support Tier Description:</strong> {supportTierDescription}</p>
      </div>
    </div>

    {/* Contact Tags Section */}
    <h4>Contact Tags</h4>
    <div className="summary-row">
      <div className="summary-item">
        <p><strong>Business Contact:</strong> {additionalInfo._BusinessContact}</p>
      </div>
      <div className="summary-item">
        <p><strong>Business Contact Email:</strong> {additionalInfo._BusinessContactEmail}</p>
      </div>
    </div>
    <div className="summary-row">
      <div className="summary-item">
        <p><strong>Technical Contact:</strong> {additionalInfo._TechnicalContact}</p>
      </div>
      <div className="summary-item">
        <p><strong>Technical Contact Mail:</strong> {additionalInfo._TechnicalContactMail}</p>
      </div>
    </div>

   
    
  </div>
</div>

          </div>
        )}

        {/* Additional Information */}
       
      </div>
    </div>
    </div>
        );
     
      default:
        return null;
    }
  };
  console.log("Formdata",formData);
  const stepNames = ['Account', 'Config','Admin', 'Storage', 'Network & Security', 'Tags', 'Summary'];
  const icons = [faCubes, faCog,faUser, faHdd, faNetworkWired, faTags, faCheckCircle];
  // const stepNames = ['Account', 'Config', 'Storage', 'Network & Security', 'Tags', 'Summary'];
  // const icons = [faCubes, faCog, faHdd, faNetworkWired, faTags, faCheckCircle];
  const [adminUser, setAdminUser] = useState({
    adminUser1: '',
    adminUser2: null,
    adminUser3: null,
    adminUser4: null
  });
  
  const [adminGroup, setAdminGroup] = useState({
    adminGroup1: '',
    adminGroup2: null
  });
  const handleAdminUserChange = (key, value) => {
    setAdminUser((prevAdminUser) => ({
      ...prevAdminUser,
      [key]: value
    }));
  };
  
  const handleAddAdminUser = () => {
    setAdminUser((prevAdminUser) => {
      if (!prevAdminUser.adminUser2) {
        return { ...prevAdminUser, adminUser2: '' }; // Add adminUser2 if not set
      } else if (!prevAdminUser.adminUser3) {
        return { ...prevAdminUser, adminUser3: '' }; // Add adminUser3 if not set
      } else if (!prevAdminUser.adminUser4) {
        return { ...prevAdminUser, adminUser4: '' }; // Add adminUser4 if not set
      }
      return prevAdminUser; // If all users are already set, return the current state
    });
  };
  
  const handleRemoveAdminUser = (key) => {
    setAdminUser((prevAdminUser) => ({
      ...prevAdminUser,
      [key]: null // Clear the admin user input
    }));
  };
  
  const handleAdminGroupChange = (key, value) => {
    setAdminGroup((prevAdminGroup) => ({
      ...prevAdminGroup,
      [key]: value
    }));
  };
  
  const handleAddAdminGroup = (key) => {
    setAdminGroup((prevAdminGroup) => ({
      ...prevAdminGroup,
      [key]: '' // Add the next admin group input
    }));
  };
  
  const handleRemoveAdminGroup = (key) => {
    setAdminGroup((prevAdminGroup) => ({
      ...prevAdminGroup,
      [key]: null // Clear the admin group input
    }));
  };
  return (
    <div className="create-class">
     <Navbar />
      <div className="create-instance">
      {(message || alertMessage) && (
  <div className="notification-overlay">
    <div className="notification-container1">
      {alertMessage && !message && (
        <div className="alert-card1">
          <div className="alert-header1">
            <div className="loading-icon1">
              <img src={Loading} alt="Loading" className="loading-gif" />
            </div>
            <p className="alert-message1">{alertMessage}</p>
            <button className="close-button1" onClick={() => setAlertMessage(null)}>
              <IoIosClose />
            </button>
          </div>
        </div>
      )}

      {message && (
        <div className={`status-card1 ${messagestatus ? 'success' : 'error'}`}>
          <div className={`status-icon1 ${messagestatus ? 'pop-animation1' : 'shake-animation1'}`}>
          {messagestatus ? <FaCheckCircle size={30} /> : <FaTimesCircle size={30} />}
          </div>
          <p >{message}</p>
          <button
            className="close-button1"
            onClick={() => {
              setMessage(null);
              setAlertMessage(null);
            }}
          >
            <IoIosClose />
          </button>
        </div>
      )}
    </div>
    <div className="additional-notification">
      <p>You can close this page. A mail will be sent to you with further details.</p>
    </div>
  </div>
)}

        <div className="progress-container">
          <div className="progress-line">
            <div className="progress-line-fill" style={{ width: calculateProgressBarWidth() }}></div>
          </div>
          {icons.map((icon, index) => (
            <div key={index} className="step-icon-container" onClick={() => setStep(index + 1)}>
              <div className={`step-icon ${step > index ? 'active' : ''}`}>
                <FontAwesomeIcon icon={icon} />
              </div>
              <p className="step-name">{stepNames[index]}</p>
            </div>
          ))}
        </div>
        <div>
          {renderStep()}
          <div className="form-buttons">
            <button type="button" onClick={goToPreviousStep} disabled={step === 1}>Previous</button>
            {step !== 7 && <button type="button" onClick={goToNextStep}>Next</button>}
            
            {/* {step === 6 && <button type="submit"onClick={handleSubmit}>CreateInstance</button>} */}
            {step === 7 && <button type="submit"onClick={handleSubmit} >CreateInstance</button>}
            
        </div></div>
       
      </div>
    </div>
  );
};
 
export default CreateWindows;


