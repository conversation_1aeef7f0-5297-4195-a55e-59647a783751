const express = require('express');
const csvParser = require('csv-parser');
const router = express.Router();
 
module.exports = (s3, Readable) => {
  router.get('/', async (req, res) => {
    try {
      const params = {
        Bucket: 'server-provision-application',
        Key: 'Data/ebs_volume.csv'
      };
      console.log(params);
 
      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }
 
        const stream = Readable.from(data.Body);
        const results = [];
        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
                InstanceName: row[Object.keys(row)[20]],
              InstanceId: row[Object.keys(row)[0]],
                device: row[Object.keys(row)[1]],
                availabilityzone: row[Object.keys(row)[2]],
                state: row[Object.keys(row)[8]],
                kmskeyid: row[Object.keys(row)[5]],
                volumeid: row[Object.keys(row)[9]],
                accountId: row[Object.keys(row)[13]],
                Region: row[Object.keys(row)[14]],
                AccountName: row[Object.keys(row)[43]],
              BusinessArea: row[Object.keys(row)[22]],
              CostCenter: row[Object.keys(row)[23]],
              CostCenterDescription:row[Object.keys(row)[24]],
              BusinessSegment: row[Object.keys(row)[25]],
              BusinessSegmentDescription: row[Object.keys(row)[26]],
              BusinessContactEmail:row[Object.keys(row)[28]],
              BusinessContact:row[Object.keys(row)[27]],
              Environment:row[Object.keys(row)[30]],
              FunctionalArea:row[Object.keys(row)[41]],
              //ProvisioningEntity:row[Object.keys(row)[4]],
              SupportTier: row[Object.keys(row)[31]],
              SupportTierDescription: row[Object.keys(row)[32]],
              ProvisioningEngineer: row[Object.keys(row)[34]],
              ProvisioningEntity: row[Object.keys(row)[33]],
            //   ProvisioningJustification: row[Object.keys(row)[22]],
              TechnicalContact:row[Object.keys(row)[37]],
              TechnicalContactEmail:row[Object.keys(row)[38]],
            //   FunctionalArea:row[Object.keys(row)[32]],
              BackupPlan: row[Object.keys(row)[39]],
            //   Environment: row[Object.keys(row)[23]],
            //   state:row[Object.keys(row)[12]]
            });
          })
          .on('end', () => {
             console.log(results);
            res.json(results);
          })
          .on('error', (err) => {
            res.status(500).send(err.message);
          });
      });
     
    } catch (err) {
      res.status(500).send(err.message);
    }
  });
 
  return router;
};