import React, { useState } from 'react';
import 'bootstrap/dist/css/bootstrap.min.css';
import './FeedbackPage.css';
import axios from 'axios';
const FeedbackPage = () => {
  const [submitted, setSubmitted] = useState(false);

  const handleSubmit = (e) => {
    axios.post('https://umanage.dev.hidglobal.com/api/feedback/submit', {
      email: '',
      feedback: '',
      
    })
    .then(response => {
      
      })
    .catch(error => {
      
    });
    e.preventDefault();
    setSubmitted(true);
  };

  if (submitted) {
    return (
      <div className="d-flex flex-column align-items-center justify-content-center vh-100 bg-light">
        <h1 className="text-success display-4">Thanks for the Feedback!</h1>
        <p className="text-muted">We appreciate your time and effort.</p>
      </div>
    );
  }

  return (
    <div className="d-flex flex-column align-items-center justify-content-center vh-100 bg-light">
      <div className="card shadow-lg p-4" style={{ maxWidth: '500px', width: '100%' }}>
        <h1 className="card-title text-center text-primary">Feedback Form</h1>
        <p className="text-center text-secondary">We value your feedback. Let us know how we did!</p>

        {/* Emoji Star Rating */}
        <div className="d-flex justify-content-center my-3">
          {['😞', '😐', '😊'].map((emoji, index) => (
            <button
              key={index}
              className="btn btn-light mx-2 fs-3 border rounded-circle shadow-sm"
            >
              {emoji}
            </button>
          ))}
        </div>

        {/* Feedback Textbox */}
        <form onSubmit={handleSubmit}>
          <div className="mb-3">
            <textarea
              className="form-control"
              placeholder="Write your feedback here..."
              rows="4"
              required
            ></textarea>
          </div>

          {/* Submit Button */}
          <div className="text-center">
            <button
              type="submit"
              className="btn btn-primary w-100 py-2 fw-bold"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default FeedbackPage;
