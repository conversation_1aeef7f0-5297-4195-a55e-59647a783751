const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');
const fetch = require('node-fetch');
const { parseStringPromise } = require('xml2js');
// Export the function to set up the router with necessary utilities
module.exports = (storeDataInS3ForTermination,generateTicketNumbertermination) => {
  let originalCredentials = AWS.config.credentials;

  // Function to assume an IAM role and return temporary credentials
  const assumeRole = async (accountId, firstname) => {
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    console.log(params);
    try {
      const data = await sts.assumeRole(params).promise();
      return data.Credentials;
    } catch (error) {
      AWS.config.update({ credentials: null });
        console.error('Error assuming role:', error.code, error.message);
        if (error.code === 'AccessDenied') {
          const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay)); 
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
            // Handle AccessDenied specifically, maybe log the accounts involved
        
      
    }else{
      console.error('Error assuming role:', error);
      throw error;
    }}
  };

  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };

  // Function to configure AWS services (SSM, S3, EC2, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      ec2: new AWS.EC2(),
      cloudFormation: new AWS.CloudFormation()
    };
  };
  const pollAutomationExecutionStatus1 = async (ssm, executionId, interval = 5000) => {
    return new Promise((resolve, reject) => {
      const intervalId = setInterval(async () => {
        try {
          const status = await getAutomationExecutionStatus(ssm, executionId);
          console.log('Current status:', status);

          if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
            clearInterval(intervalId);
            resolve(status);
          }
        } catch (error) {
          clearInterval(intervalId);
          reject(error);
        }
      }, interval);
    });
  };
  const pollExecutionStatus = async (ssm, req, executionID, interval) => {
    const intervalId = setInterval(async () => {
      try {
        const executionDetails = await getAutomationExecutionDetails(ssm, executionID);
        const status = executionDetails.AutomationExecutionStatus;
        console.log(`Current status of execution ID ${executionID}: ${status}`);

        if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
          clearInterval(intervalId);
          console.log(`Final status of execution ID ${executionID}: ${status}`);
        }
      } catch (error) {
        console.error('Error fetching execution status:', error);
        clearInterval(intervalId);
      }
    }, interval);
  };
  // Function to get the status of an SSM Automation execution
  const getAutomationExecutionStatus = async (ssm, executionId) => {
    try {
      const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      return status;
    } catch (error) {
      console.error('Error fetching execution status:', error);
      throw error;
    }
  };

  // Function to fetch detailed results of an SSM Automation execution
  const getAutomationExecutionDetails = async (ssm, executionId) => {
    try {
      const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      return data.AutomationExecution;
    } catch (error) {
      console.error('Error fetching execution details:', error);
      throw error;
    }
  };
  

const handleSSMRequest = async (req, res, documentName) => {
  const { instanceId, region, businesscontact, email, accountId, accountname, instancename,servicenow } = req.body;
 let ticket=req.body.ticket;
  console.log(ticket);
let firstname=email;
let x=firstname.replace(/\s+/g,'');
let result=x.substring(0,8);
let randomValue = Math.floor(Math.random() * 9000) + 1000;
console.log(servicenow);
// if (servicenow === 'NO') {
//   console.log('in ssm servicenow');
//   try {
//     if (accountId != ************) {
//       const credentials = await assumeRole(************, result);
//       await initializeAWS(credentials, 'us-east-1');
//   } else {
//       AWS.config.update({ region: 'us-east-1' });
//   }
//  // const startTime = new Date();
//   const { ssm } = configureAWS();
//     //const ssm = new AWS.SSM();
//     const params = {
//       DocumentName: 'servicenowticket', // Replace with your SSM Automation Document name
//       Parameters: {
//         Description: [`Termination of ec2 Instance${instancename} using  
//                          done by umange `],
//         ContactingCustomer:[email],
//         AffectingUser:[email], // Pass the email as a parameter
//         ShortDescription: [`${instancename} Termination`] // Pass the description as a parameter
//       },
//        };

//     // Trigger the SSM Automation Document
//     const data = await ssm.startAutomationExecution(params).promise();
//     console.log('SSM servicenow Automation triggered successfully:', data.AutomationExecutionId);
    
//     // Optionally fetch ticket number or status from the Automation execution output
//     const executionId = data.AutomationExecutionId;
//     const executionDetails = await getAutomationExecutionDetails(ssm, executionId);
//     console.log(executionDetails.Outputs);
//   //  const status = await pollAutomationExecutionStatus(ssm,  executionId);
//    //const executionId = data.AutomationExecutionId;
//    const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));
//    await delay(50000);
//   // await delay(180000);
//    //const status = await pollAutomationExecutionStatus(ssm,  executionId);
//     const executionDetailsnumber = await getAutomationExecutionDetails(ssm, executionId);
//     console.log(executionDetailsnumber.Outputs);
//      ticket= executionDetailsnumber.Outputs?.['RetrieveRITM.RITM'] || 'DefaultTicketNumber';
//     console.log('Ticket Number from SSM:', ticket);
//   } catch (error) {
//     console.error('Error triggering SSM Automation:', error);
//     throw error; // Handle error appropriately
//   }
// }

// // if(servicenow=='NO'){

// //   const servicenowResponse = await fetch(
// //     'https://integration.nonprod.hidglobal.com/uat/ws/rest/ServiceNow/Incident/',
// //     {
// //       method: 'POST',
// //       headers: {
// //         'Content-Type': 'application/xml',
// //         Authorization: 'Basic ' + btoa('servicenowuser-uat:servicenowuser-uat@hidglobal$yd786'),
// //         'x-api-key': 'd9a45eed-eeb2-4e76-840b-e6c98c97a9d6',
// //       },
// //       body: `
// //         <Incident>
// //             <user>${email}</user>
// //             <short_description>${instancename} Termination</short_description>
// //             <detailed_description>for terminating </detailed_description>
// //         </Incident>
// //       `,
// //     }
// //   );

// //   const servicenowResponseText = await servicenowResponse.text();

// //   if (!servicenowResponse.ok) {
// //     throw new Error(`ServiceNow Error: ${servicenowResponse.status} ${servicenowResponseText}`);
// //   }
// //   const parsedResponse = await parseStringPromise(servicenowResponseText);
// //       ticket = parsedResponse.Response.IncidentNumber[0];
// //   console.log(parsedResponse.Response.IncidentNumber[0]);
// //   console.log(parsedResponse.Response);  
// //   // res.status(200).json({
// //   //   message: 'SSM Automation Document triggered successfully!',
// //   //   executionID: executionID,
// //   //   ticketNumber: ticketNumber,
// //   //   servicenowResponse: servicenowResponseText,
// //   // });
// // } 
// // Concatenate the random value to the string
let y = result + randomValue.toString();
  const params = {
      DocumentName: documentName,
      Parameters: {
        Description: [`Termination of ec2 Instance${instancename} id ${instanceId}Accoint ID ${accountId}  Region ${region}using done by umanage `],
                  ContactingCustomer:[email],
                  AffectingUser:[email], // Pass the email as a parameter
                  ShortDescription: [` Termination of ec2 instance `],
                  RequestedFor: [email],
          Id: [instanceId],
          InstanceId: [ ],
          
          //ResourceARNs: [`arn:aws:ec2:${region}:${accountId}:instance/${instanceId}`],
        AutomationAssumeRole: [`arn:aws:iam::${accountId}:role/CrossAccountAccessRole`]
      },
  };

  try {
      if (accountId != ************) {
          const credentials = await assumeRole(accountId, y);
          await initializeAWS(credentials, region);
      } else {
          AWS.config.update({ region: region });
      }
      const startTime = new Date();
      const { ssm } = configureAWS();
      const data = await ssm.startAutomationExecution(params).promise();
      const endTime = new Date();
      const executionID = data.AutomationExecutionId;
      AWS.config.update({ credentials: originalCredentials });
      // Immediately respond with the execution ID
     

      // Fetch execution details after a short delay to ensure execution has started
      const executionDetails = await getAutomationExecutionDetails(ssm, executionID);
      const ticketNumber = generateTicketNumbertermination();// Adjust as needed
      //const servicenownumber = 0; // Adjust as needed
      console.log(executionDetails);
      // const startTime = new Date(executionDetails.ExecutionStartTime * 1000).toISOString();
      // const endTime = new Date(executionDetails.ExecutionEndTime * 1000).toISOString();
      
    //   await storeDataInS3ForTermination(
    //     ticketNumber,
    //     executionID,
    //     instanceId,
    //     instancename,
    //     accountId,
    //     executionDetails.DocumentName,
    //     startTime,
    //     endTime,
    //    "",
    //     executionDetails.Parameters.InstanceId,
    //     businesscontact,
    //     email,
    //     region,
    //     ticket
    // );
    AWS.config.update({ credentials: originalCredentials });
    console.log("before returnining to frontend"+ticket);
    res.status(200).json({
      message: 'SSM Automation Document triggered successfully!',
      executionID: executionID,
      ticketNumber: ticket,
  });
      // Start polling for status in a separate function
      pollAutomationExecutionStatus(ssm, req, executionID, 5000);
  } catch (err) {AWS.config.update({ credentials: originalCredentials });
      console.error('Error in handleSSMRequest:', err);
      // Check if a response has already been sent before sending an error response
      if (!res.headersSent) {
          res.status(500).json({ error: err.message });
      }
  }
};


// Polling function to check execution status and steps
const pollAutomationExecutionStatus = async (ssm,req, executionID, interval) => {
  //const { instanceId, region, businesscontact, email, accountId, accountname, instancename, ticket } = req.body;

    const intervalId = setInterval(async () => {
        try {
            const executionDetails = await getAutomationExecutionDetails(ssm, executionID);
            const status = executionDetails.AutomationExecutionStatus;
            const steps = executionDetails.StepExecutions || [];
            const successfulSteps = steps.filter(step => step.Status === 'Success');
            const ticketNumber=0;
            const servicenownumber=0;
            console.log(`Current status of execution ID ${executionID}: ${status}`);

            // Check if at least three steps have succeeded
            if (successfulSteps.length >= 2) {
                console.log(`First three steps succeeded for execution ID: ${executionID}`);
                console.log("in trrmination");  
      // await storeDataInS3ForTermination(
      //   ticketNumber,
      //   executionID, 
      //   accountId, 
      //   executionDetails.DocumentName, 
      //   executionDetails.ExecutionStartTime, 
      //   executionDetails.ExecutionEndTime, 
      //   executionDetails.AutomationExecutionStatus, 
      //   executionDetails.Parameters.InstanceId, 
      //   businesscontact, 
      //   email, 
      //   accountname, 
      //   "in poll",
      //   servicenownumber
      // );
                // You can perform additional actions here (e.g., notify users, store data)
            }

            // Stop polling if the execution is complete
            if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                clearInterval(intervalId);
                console.log("in trrmination");  
                // await storeDataInS3ForTermination(
                //   ticketNumber,
                //   executionID, 
                //   accountId, 
                //   executionDetails.DocumentName, 
                //   executionDetails.ExecutionStartTime, 
                //   executionDetails.ExecutionEndTime, 
                //   executionDetails.AutomationExecutionStatus, 
                //   executionDetails.Parameters.InstanceId, 
                //   businesscontact, 
                //   email, 
                //   accountname, 
                  
                //   servicenownumber
                // );
                console.log(`Final status of execution ID ${executionID}: ${status}`);
            }
            
        } catch (error) {
            console.error('Error fetching execution status:', error);
            clearInterval(intervalId); // Stop polling on error
        }
    }, interval);
};

  // Function to handle SSM requests (start/stop EC2 instances)
  

  // Route to stop an EC2 instance
  router.post('/', (req, res) => {
    handleSSMRequest(req, res, 'Terminate_instance');
  });

  return router;
};
