const express = require('express');
const router = express.Router();
const { STSClient, AssumeRoleCommand } = require('@aws-sdk/client-sts');
const AWS = require('aws-sdk');

module.exports = (storeDataInS3forCreate) => {
  let originalCredentials = AWS.config.credentials;

  const deployCloudFormationStack = async (params) => {
    try {
      const assumeRole = async (accountId, params) => {
        const stsClient = new STSClient({ region: params.Region });
        const assumeRoleCommand = new AssumeRoleCommand({
          RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
          RoleSessionName: 'mySessionName',
        });

        try {
          const data = await stsClient.send(assumeRoleCommand);
          return data.Credentials;
        } catch (error) {
          console.error('Error assuming role:', error);
          throw error;
        }
      };

      // Initialize AWS with temporary credentials
      const initializeAWS = async (accountId, params) => {
        console.log(accountId);
        const credentials = await assumeRole(accountId, params);
        return new AWS.ServiceCatalog({
          region: params.Region,
          credentials: {
            accessKeyId: credentials.AccessKeyId,
            secretAccessKey: credentials.SecretAccessKey,
            sessionToken: credentials.SessionToken,
          },
        });
      };

      console.log("params are", params);
      const serviceCatalog = await initializeAWS(params.accountId, params);
     
      const serviceCatalogParams = [
        { Key: 'InstanceName', Value: params.InstanceName },
        { Key: 'InstanceType', Value: params.InstanceType },
        { Key: 'AMI', Value: params.AMI },
        { Key: 'SubnetId', Value: params.subnetId },
        { Key: 'IAMRoleName', Value: params.IAMRoleName || '' },
        { Key: 'CostCenter', Value: params.CostCenter },
        { Key: 'CostCenterDescription', Value: params.CostCenterDescription },
        { Key: 'SupportTier', Value: params.SupportTier },
        { Key: 'SupportTierDescription', Value: params.SupportTierDescription },
        { Key: 'InstanceSource', Value: params.InstanceName },
        { Key: 'ProvisioningEntity', Value: params.ProvisioningEntity },
        { Key: 'ProvisioningJustification', Value: params.ProvisioningJustification },
        { Key: 'BusinessArea', Value: params.BusinessArea },
        { Key: 'BusinessContact', Value: params.BusinessContact },
        { Key: 'BusinessContactEmail', Value: params.BusinessContactEmail },
        { Key: 'BusinessSegment', Value: params.BusinessSegment },
        { Key: 'BusinessSegmentDescription', Value: params.BusinessSegmentDescription },
        { Key: 'TechnicalContact', Value: params.TechnicalContact },
        { Key: 'TechnicalContactEmail', Value: params.TechnicalContactEmail },
        { Key: 'Environment', Value: params.Environment },
        { Key: 'NetworkLocation', Value: params.NetworkLocation },
        { Key: 'FunctionalArea', Value: params.FunctionalArea },
        { Key: 'ProvisioningEngineer', Value: params.ProvisioningEngineer },
        { Key: 'BackupPlan', Value: params.BackupPlan || '' },
        { Key: `SecurityGroupIds`, Value: params.securityGroupIds }
      ];

      // Spread the security group IDs
     // Assuming params is an object that contains the parsed YAML data
// const securityGroupIds = Array.isArray(params.securityGroupIds) 
// ? params.securityGroupIds 
// : [params.securityGroupIds]; // Convert to array if it's a single value

// securityGroupIds.forEach((sgId, index) => {
// serviceCatalogParams.push({ Key: `SecurityGroupIds.${index}`, Value: sgId });
// });

      async function provisionProduct(productId, artifactId, productName, serviceCatalogParams) {
        const params = {
          ProductId: productId,
          ProvisioningArtifactId: artifactId,
          ProvisionedProductName: productName,
          ProvisioningParameters: serviceCatalogParams,
        };

        try {
          const data = await serviceCatalog.provisionProduct(params).promise();
          console.log('Provisioned Product:', data);
          return data;
        } catch (error) {
          console.error('Error provisioning product:', error);
          throw error;
        }
      }

      const result = await provisionProduct('prod-v7wibcjfb2oj4', 'pa-jhhqmlea6cmqc', 'ec2', serviceCatalogParams);

      // Store data in S3 after successful provisioning
      await storeDataInS3forCreate(params, result);
      AWS.config.update({ credentials: originalCredentials });

      return result;
    } catch (error) {
      AWS.config.update({ credentials: originalCredentials });
      console.error('Error deploying stack:', error);
      throw error;
    }
  };

  router.post('/', async (req, res) => {
    try {
      const params = req.body;
      console.log(req.body);
      await deployCloudFormationStack(params);
      res.status(200).send('Deployment initiated');
    } catch (error) {
      res.status(500).send('Error initiating deployment');
    }
  });

  return router;
};
