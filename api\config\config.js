const { Certificate } = require('crypto');
require('dotenv').config();
const fs= require('fs')
module.exports = {
  development: {
    app: {
      name: 'Passport SAML strategy example',
      port: process.env.PORT || 443
    },
    passport: {
      strategy: 'saml',
      saml: {
        callbackUrl:"https://umanage.dev.hidglobal.com",
        entryPoint: process.env.SAML_ENTRY_POINT || 'https://portal.sso.us-east-1.amazonaws.com/saml/assertion/ODQ3NDI4MjQxNDg3X2lucy05ZWY2YTc0NGE5YjZhMWQ4',
        issuer: 'https://portal.sso.us-east-1.amazonaws.com/saml/assertion/ODQ3NDI4MjQxNDg3X2lucy02MjJiNjY3MWIzOWEzNzZi',
        cert: process.env.SAML_CERT || fs.readFileSync('certificate.pem','utf-8')
      }
    }
  }
};
