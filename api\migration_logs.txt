
  Step 1: Loop
  Status: Success
  Outputs: {
  "CurrentIteration": [
    "2"
  ],
  "CurrentIteratorValue": [
    "vol-05b48dc0b6e77e01c"
  ],
  "OutputPayload": [
    "{\"CurrentIteration\":2,\"CurrentIteratorValue\":\"vol-05b48dc0b6e77e01c\"}"
  ]
}
  


  Step 2: Branch_4
  Status: Pending
  Outputs: undefined
  


  Step 3: Volume_Snapshot
  Status: Pending
  Outputs: undefined
  


  Step 4: Verify_Volume_Snapshot
  Status: Pending
  Outputs: undefined
  


  Step 5: Share_Volume_Snapshot
  Status: Pending
  Outputs: undefined
  


  Step 6: DescribeSnapshots
  Status: Pending
  Outputs: undefined
  


  Step 7: Volume_Creation
  Status: Pending
  Outputs: undefined
  


  Step 8: Verify_Volume
  Status: Pending
  Outputs: undefined
  


  Step 9: Get_Ec2_Tags_EBS
  Status: Pending
  Outputs: undefined
  


  Step 10: Copy_Tags_EBS_SharedAccount
  Status: Pending
  Outputs: undefined
  


  Step 11: Output
  Status: Pending
  Outputs: undefined
  


  Step 12: Volume_Snapshot_No_KMS
  Status: Pending
  Outputs: undefined
  


  Step 13: Verify_Volume_Snapshot_No_KMS
  Status: Pending
  Outputs: undefined
  


  Step 14: Volume_Snapshot_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 15: Verify_Volume_Snapshot_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 16: Share_Volume_Snapshot_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 17: Volume_Creation_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 18: Verify_Volume_Creation_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 19: Get_Ec2_Tags_EBS_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 20: Copy_Tags_EBS_KMS_KEY_SharedAccount
  Status: Pending
  Outputs: undefined
  


  Step 21: Output_1
  Status: Pending
  Outputs: undefined
  


  Step 22: AMI_Snapshot_No_KMS
  Status: Pending
  Outputs: undefined
  


  Step 23: Verify_AMI_Snapshot_No_KMS
  Status: Pending
  Outputs: undefined
  


  Step 24: AMI_Snapshot_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 25: Verify_AMI_Snapshot_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 26: Create_AMI_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 27: Get_Ec2_Tags_AMI_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 28: Set_Tags_AMI_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 29: Share_AMI_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 30: Copy_Tags_AMI_KMS_KEY_SharedAccount
  Status: Pending
  Outputs: undefined
  


  Step 31: Output_3
  Status: Pending
  Outputs: undefined
  


  Step 32: RunScript
  Status: Success
  Outputs: {
  "OutputPayload": [
    "{\"ExecutionLog\":\"Received event: {'i': 1, 'devicename': ['/dev/sda1', '/dev/xvdbb'], 'EventData': 'vol-0c6f77afa0d7fe8b5', 'Key': ['arn:aws:kms:us-east-1:************:key/mrk-0246d83eba0e44e498a39578f32de091', 'arn:aws:kms:us-east-1:************:key/mrk-0246d83eba0e44e498a39578f32de091']}\\nScript output: message=vol-0c6f77afa0d7fe8b5, devicename=/dev/sda1, key=arn:aws:kms:us-east-1:************:key/mrk-0246d83eba0e44e498a39578f32de091\\n\",\"Payload\":{\"message\":\"vol-0c6f77afa0d7fe8b5\",\"devicename\":\"/dev/sda1\",\"key\":\"arn:aws:kms:us-east-1:************:key/mrk-0246d83eba0e44e498a39578f32de091\"}}"
  ],
  "ScriptExecutionId": [
    "198f9f85-f36a-4160-bf4c-2fd5196f80c9"
  ],
  "curr-id": [
    "vol-0c6f77afa0d7fe8b5"
  ],
  "devicename": [
    "/dev/sda1"
  ],
  "key": [
    "arn:aws:kms:us-east-1:************:key/mrk-0246d83eba0e44e498a39578f32de091"
  ]
}
  


  Step 33: Branch_1
  Status: Success
  Outputs: {
  "NextStep": [
    "Branch_3"
  ],
  "OutputPayload": [
    "{\"NextStep\":\"Branch_3\"}"
  ]
}
  


  Step 34: Branch_3
  Status: Success
  Outputs: {
  "NextStep": [
    "AMI_Snapshot"
  ],
  "OutputPayload": [
    "{\"NextStep\":\"AMI_Snapshot\"}"
  ]
}
  


  Step 35: AMI_Snapshot
  Status: Success
  Outputs: {
  "SnapshotId": [
    "snap-0c8d4bf9d188dd258"
  ]
}
  


  Step 36: Verify_AMI_Snapshot
  Status: Success
  Outputs: {}
  


  Step 37: Create_AMI
  Status: Success
  Outputs: {
  "ImageId": [
    "ami-01637e40c48c76212"
  ]
}
  


  Step 38: Get_Ec2_Tags_AMI
  Status: Success
  Outputs: {
  "EC2Tags": [
    "{\"_SupportTierDescription\":\"SLA: P3 *\",\"_TechnicalContact\":\"Prasana\",\"_CostCenterDescription\":\"Global Infrastructure and NOC\",\"_FunctionalArea\":\"IT\",\"_ProvisioningEntity\":\"HID Global\",\"_NetworkLocation\":\"INTERNAL\",\"_TechnicalContactEmail\":\"\",\"_BusinessArea\":\"OtherBA\",\"_SupportTier\":\"TIER3\",\"_BackupPlan\":\"N/A\",\"_CostCenter\":\"6420\",\"_mapmigrated\":\"migSITGOMR8R2\",\"_ProvisioningEngineer\":\"<EMAIL>\",\"_Environment\":\"SBX\",\"_ProvisioningJustification\":\"RITM0136745\",\"_InstanceSource\":\"NEW\",\"_BusinessSegmentDescription\":\"HID Global\",\"Name\":\"AUE1BSBXLXAZZ94\",\"_BusinessContact\":\"Prasana\",\"_BusinessSegment\":\"9000\",\"_BusinessContactEmail\":\"Prasana\"}"
  ],
  "OutputPayload": [
    "{\"Payload\":{\"EC2Tags\":{\"_SupportTierDescription\":\"SLA: P3 *\",\"_TechnicalContact\":\"Prasana\",\"_CostCenterDescription\":\"Global Infrastructure and NOC\",\"_FunctionalArea\":\"IT\",\"_ProvisioningEntity\":\"HID Global\",\"_NetworkLocation\":\"INTERNAL\",\"_TechnicalContactEmail\":\"\",\"_BusinessArea\":\"OtherBA\",\"_SupportTier\":\"TIER3\",\"_BackupPlan\":\"N/A\",\"_CostCenter\":\"6420\",\"_mapmigrated\":\"migSITGOMR8R2\",\"_ProvisioningEngineer\":\"<EMAIL>\",\"_Environment\":\"SBX\",\"_ProvisioningJustification\":\"RITM0136745\",\"_InstanceSource\":\"NEW\",\"_BusinessSegmentDescription\":\"HID Global\",\"Name\":\"AUE1BSBXLXAZZ94\",\"_BusinessContact\":\"Prasana\",\"_BusinessSegment\":\"9000\",\"_BusinessContactEmail\":\"Prasana\"}}}"
  ],
  "ScriptExecutionId": [
    "f5fb9868-e974-42ba-8c67-51b30d73fccb"
  ]
}
  


  Step 39: Set_Tags_AMI
  Status: Success
  Outputs: {
  "OutputPayload": [
    "{\"Payload\":{\"Status\":\"Tags added to AMI\"}}"
  ],
  "ScriptExecutionId": [
    "65654f63-0c8c-4bca-ad52-55dfaafec69e"
  ],
  "TaggingStatus": [
    "Tags added to AMI"
  ]
}
  


  Step 40: Share_AMI
  Status: Success
  Outputs: {}
  


  Step 41: Copy_Tags_AMI_SharedAccount
  Status: Success
  Outputs: {
  "OutputPayload": [
    "{\"Payload\":{\"Status\":\"Tags copied to shared account\"}}"
  ],
  "ScriptExecutionId": [
    "6afcc10d-323b-4181-8ee2-b073150777f3"
  ],
  "TaggingStatus": [
    "Tags copied to shared account"
  ]
}
  


  Step 42: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-01637e40c48c76212"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-01637e40c48c76212\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "5571dd6c-c053-4bdc-b775-915ba1ea7fdb"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Step 43: Volume_Snapshot_No_KMS
  Status: Pending
  Outputs: undefined
  


  Step 44: Verify_Volume_Snapshot_No_KMS
  Status: Pending
  Outputs: undefined
  


  Step 45: Volume_Snapshot_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 46: Verify_Volume_Snapshot_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 47: Share_Volume_Snapshot_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 48: Volume_Creation_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 49: Verify_Volume_Creation_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Step 50: Get_Ec2_Tags_EBS_KMS_KEY
  Status: Pending
  Outputs: undefined
  


  Final Outputs: {}
  


  Step 11: Output
  Status: Pending
  Outputs: undefined
  


  Step 21: Output_1
  Status: Pending
  Outputs: undefined
  


  Step 31: Output_3
  Status: Pending
  Outputs: undefined
  


  Step 42: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-047e99e55c62f51d7"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-047e99e55c62f51d7\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "350e21a9-1c22-4ea5-854b-707e5cf3ba78"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Step 52: Output_1
  Status: Pending
  Outputs: undefined
  


  Step 61: Output_2
  Status: Pending
  Outputs: undefined
  


  Step 71: Output_3
  Status: Pending
  Outputs: undefined
  


  Step 83: Output
  Status: Success
  Outputs: {
  "Output1": [
    "vol-0d112133fc954d3e9"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"vol-0d112133fc954d3e9\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "ded4220f-8758-4d10-817e-d1ab4c45546b"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Final Outputs: {}
  


  Step 42: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0c8796b53f2d6ac5b"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0c8796b53f2d6ac5b\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "d56ef682-b207-4cb6-8d33-572c6496f3da"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Step 83: Output
  Status: Success
  Outputs: {
  "Output1": [
    "vol-078e6d777b8858db6"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"vol-078e6d777b8858db6\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "85926214-b747-4c00-95bc-256a581038ec"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Final Outputs: {}
  


  Step 42: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-07d3107610baf3624"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-07d3107610baf3624\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "7d09567b-cee4-46bc-a4f1-bd5c66edabf4"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Step 83: Output
  Status: Success
  Outputs: {
  "Output1": [
    "vol-0a1c64dec600cdc21"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"vol-0a1c64dec600cdc21\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "6777cf44-947b-4db9-8cc3-05431ab55269"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Final Outputs: {}
    useroutput: undefined"ami-07d3107610baf3624""vol-0a1c64dec600cdc21"
    tags: "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  


  Step 42: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-019bab4cdc2b545c4"
  ],
  "Output2": [
    "{'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-019bab4cdc2b545c4\",\"output2\":\"{'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}\",\"tags\":\"{'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}\"}}"
  ],
  "ScriptExecutionId": [
    "509eaac5-0985-468f-922c-eb8e33516ade"
  ],
  "tags": [
    "{'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-019bab4cdc2b545c4"
    tags: {'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}
  


  Step 42: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-036f96b99a9b97b1b"
  ],
  "Output2": [
    "{'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-036f96b99a9b97b1b\",\"output2\":\"{'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}\",\"tags\":\"{'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}\"}}"
  ],
  "ScriptExecutionId": [
    "f9fb4e3c-38ec-44ea-af25-e822a5ce2cc5"
  ],
  "tags": [
    "{'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-036f96b99a9b97b1b"
    tags: {'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}
  


  Step 42: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0e0d2099e607766a7"
  ],
  "Output2": [
    "{'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0e0d2099e607766a7\",\"output2\":\"{'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}\",\"tags\":\"{'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}\"}}"
  ],
  "ScriptExecutionId": [
    "e25d9d75-28c3-4929-8ae7-75c981bfd86b"
  ],
  "tags": [
    "{'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0e0d2099e607766a7"
    tags: {'_ProvisioningJustification': 'PIR-563', '_Environment': 'PRODUCTION', '_BusinessArea': 'OtherBA', '_Application': 'FORTIGATE', '_BusinessContactEmail': '<EMAIL>', '_BackupPlan': 'GOLD', '_SupportTier': 'TIER1', 'Name': 'fgt_vm2', 'map-migrated': 'migV3IGSCYWB3', 'Terraform': 'true', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_BusinessAreaDescription': 'Other', '_FunctionalArea': 'R and D', '_SupportContact': 'Paul Rudin', '_CostCenterDescription': 'Global Infrastructure and NOC', '_BusinessSegmentDescription': 'HID Global', '_Function': 'FIREWALL', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Paramjeet Singh', '_InstanceId': 'i-0341c7fe04ab9d2e3', '_BusinessSegment': '9000', '_TechnicalContact': 'Jose Contreras'}
  


  Step 42: Output
  Status: Success
  Outputs: {
  "Output1": [
    "vol-0b5e797b5a9f5bd76"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"vol-0b5e797b5a9f5bd76\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "06ea8ff2-760b-4ca5-ab4f-8e3825b11810"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Step 83: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0eefcdc7587080708"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0eefcdc7587080708\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "87041eb9-42e7-4d36-8565-ea8a82fc13d1"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "vol-0b5e797b5a9f5bd76""ami-0eefcdc7587080708"
    tags: {'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Step 41: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0dcbed57936df60b4"
  ],
  "Output2": [
    "{'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0dcbed57936df60b4\",\"output2\":\"{'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}\",\"output3\":\"['snap-08743b4b250097e5e']\",\"tags\":\"{'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}\"}}"
  ],
  "ScriptExecutionId": [
    "7897072d-6997-4ed2-8e91-f88c0b65c893"
  ],
  "tags": [
    "{'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0dcbed57936df60b4"
    tags: {'_ProvisioningJustification': 'PIR-714', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', 'mgn.amazonaws.com-job': 'mgnjob-ed67ca069659526cd', '_BackupPlan': 'SILVER', '_BusinessContactEmail': '<EMAIL>', '_SupportTier': 'TIER2', '_InstanceSource': 'INEMB', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', 'Name': 'PMIVIMDEV', 'mgn.amazonaws.com-source-server': 's-efbe347b8e7021cff', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', 'AWSApplicationMigrationServiceSourceServerID': 's-efbe347b8e7021cff', '_CostCenter': '2106', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Professional Services', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Mike Wallis', '_BusinessSegment': '1070'}
  


  Step 41: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-01f38a897df979d7c"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-01f38a897df979d7c\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"output3\":\"['snap-03a6fdd23ecf314da', 'snap-0a7ebb72161a69ec8']\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "9b551e05-f852-4d28-8e99-b6c003db79d0"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Step 81: Output
  Status: Success
  Outputs: {
  "Output1": [
    "vol-0d59ac48296006841"
  ],
  "Output2": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"vol-0d59ac48296006841\",\"output2\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\",\"tags\":\"{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}\"}}"
  ],
  "ScriptExecutionId": [
    "981b7d8b-c7b4-4586-ba47-db22041c084c"
  ],
  "tags": [
    "{'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-01f38a897df979d7c""vol-0d59ac48296006841"
    tags: {'_mapmigrated': 'migSITGOMR8R2', '_ProvisioningJustification': 'RITM0136745', '_Environment': 'SBX', '_BusinessArea': 'OtherBA', '_TechnicalContactEmail': '', '_BackupPlan': 'N/A', '_BusinessContactEmail': 'Prasana', '_SupportTierDescription': 'SLA: P3 *', '_SupportTier': 'TIER3', '_InstanceSource': 'NEW', 'Name': 'AUE1BSBXLXAZZ94', '_NetworkLocation': 'INTERNAL', '_CostCenter': '6420', '_CostCenterDescription': 'Global Infrastructure and NOC', '_FunctionalArea': 'IT', '_BusinessSegmentDescription': 'HID Global', '_ProvisioningEntity': 'HID Global', '_BusinessContact': 'Prasana', '_BusinessSegment': '9000', '_TechnicalContact': 'Prasana', '_ProvisioningEngineer': '<EMAIL>'}
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Step 33: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-03e719c486f9911b3"
  ],
  "Output2": [
    "{'_Environment': 'NON-PRODUCTION', 'mgn.amazonaws.com-job': 'mgnjob-ea50330a1d189fb90', '_BackupPlan': 'SILVER', '_SupportTier': 'TIER2', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', '_InstanceSource': 'INEMB', 'Name': 'BASEUPGSQLHCQA', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceSourceServerID': 's-e56b47fe7438f1f84', '_CostCenter': '4600', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_Function': 'APPLICATION', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Bhramarish (Gravity)', '_ProvisioningJustification': 'PIR-714', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'mgn.amazonaws.com-source-server': 's-e56b47fe7438f1f84', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Bhramarish (Gravity)', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Lucas Vieira'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-03e719c486f9911b3\",\"output2\":\"{'_Environment': 'NON-PRODUCTION', 'mgn.amazonaws.com-job': 'mgnjob-ea50330a1d189fb90', '_BackupPlan': 'SILVER', '_SupportTier': 'TIER2', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', '_InstanceSource': 'INEMB', 'Name': 'BASEUPGSQLHCQA', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceSourceServerID': 's-e56b47fe7438f1f84', '_CostCenter': '4600', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_Function': 'APPLICATION', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Bhramarish (Gravity)', '_ProvisioningJustification': 'PIR-714', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'mgn.amazonaws.com-source-server': 's-e56b47fe7438f1f84', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Bhramarish (Gravity)', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Lucas Vieira'}\",\"output3\":\"['snap-0c6197fc073fef413']\",\"tags\":\"{'_Environment': 'NON-PRODUCTION', 'mgn.amazonaws.com-job': 'mgnjob-ea50330a1d189fb90', '_BackupPlan': 'SILVER', '_SupportTier': 'TIER2', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', '_InstanceSource': 'INEMB', 'Name': 'BASEUPGSQLHCQA', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceSourceServerID': 's-e56b47fe7438f1f84', '_CostCenter': '4600', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_Function': 'APPLICATION', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Bhramarish (Gravity)', '_ProvisioningJustification': 'PIR-714', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'mgn.amazonaws.com-source-server': 's-e56b47fe7438f1f84', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Bhramarish (Gravity)', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Lucas Vieira'}\"}}"
  ],
  "ScriptExecutionId": [
    "1e1af101-4bf4-4d35-b8ab-f75295cade05"
  ],
  "tags": [
    "{'_Environment': 'NON-PRODUCTION', 'mgn.amazonaws.com-job': 'mgnjob-ea50330a1d189fb90', '_BackupPlan': 'SILVER', '_SupportTier': 'TIER2', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', '_InstanceSource': 'INEMB', 'Name': 'BASEUPGSQLHCQA', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceSourceServerID': 's-e56b47fe7438f1f84', '_CostCenter': '4600', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_Function': 'APPLICATION', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Bhramarish (Gravity)', '_ProvisioningJustification': 'PIR-714', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'mgn.amazonaws.com-source-server': 's-e56b47fe7438f1f84', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Bhramarish (Gravity)', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Lucas Vieira'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-03e719c486f9911b3"
    tags: {'_Environment': 'NON-PRODUCTION', 'mgn.amazonaws.com-job': 'mgnjob-ea50330a1d189fb90', '_BackupPlan': 'SILVER', '_SupportTier': 'TIER2', '_SupportTierDescription': 'ON-DEMAND (automatic first backup)', '_InstanceSource': 'INEMB', 'Name': 'BASEUPGSQLHCQA', 'map-migrated': 'migC5H22Y5OCL', 'AWSApplicationMigrationServiceSourceServerID': 's-e56b47fe7438f1f84', '_CostCenter': '4600', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_Function': 'APPLICATION', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Bhramarish (Gravity)', '_ProvisioningJustification': 'PIR-714', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'mgn.amazonaws.com-source-server': 's-e56b47fe7438f1f84', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Bhramarish (Gravity)', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Lucas Vieira'}
  


  Step 33: Output_2
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0797fe02e33019031"
  ],
  "Output2": [
    "{'_ProvisioningJustification': 'RITM0125510', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'AAS1BQAMSB519HC', '_InstanceSource': 'NEW', '_SupportTier': 'TIER3', 'map-migrated': 'migC5H22Y5OCL', 'Terraform': 'true', '_CostCenter': '4600', '_NetworkLocation': 'INTERNAL', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'HID Engineer', '_BusinessContact': 'Chakraborty Debarshi', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Sathyanarayanan'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0797fe02e33019031\",\"output2\":\"{'_ProvisioningJustification': 'RITM0125510', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'AAS1BQAMSB519HC', '_InstanceSource': 'NEW', '_SupportTier': 'TIER3', 'map-migrated': 'migC5H22Y5OCL', 'Terraform': 'true', '_CostCenter': '4600', '_NetworkLocation': 'INTERNAL', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'HID Engineer', '_BusinessContact': 'Chakraborty Debarshi', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Sathyanarayanan'}\",\"output3\":\"['snap-04ef4b41fcfb55906']\",\"tags\":\"{'_ProvisioningJustification': 'RITM0125510', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'AAS1BQAMSB519HC', '_InstanceSource': 'NEW', '_SupportTier': 'TIER3', 'map-migrated': 'migC5H22Y5OCL', 'Terraform': 'true', '_CostCenter': '4600', '_NetworkLocation': 'INTERNAL', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'HID Engineer', '_BusinessContact': 'Chakraborty Debarshi', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Sathyanarayanan'}\"}}"
  ],
  "ScriptExecutionId": [
    "5b116f7f-b035-4cea-9c46-599e5c267fa2"
  ],
  "tags": [
    "{'_ProvisioningJustification': 'RITM0125510', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'AAS1BQAMSB519HC', '_InstanceSource': 'NEW', '_SupportTier': 'TIER3', 'map-migrated': 'migC5H22Y5OCL', 'Terraform': 'true', '_CostCenter': '4600', '_NetworkLocation': 'INTERNAL', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'HID Engineer', '_BusinessContact': 'Chakraborty Debarshi', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Sathyanarayanan'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0797fe02e33019031"
    tags: {'_ProvisioningJustification': 'RITM0125510', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'AAS1BQAMSB519HC', '_InstanceSource': 'NEW', '_SupportTier': 'TIER3', 'map-migrated': 'migC5H22Y5OCL', 'Terraform': 'true', '_CostCenter': '4600', '_NetworkLocation': 'INTERNAL', '_CostCenterDescription': 'Software Development', '_FunctionalArea': 'R and D', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'HID Engineer', '_BusinessContact': 'Chakraborty Debarshi', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'Sathyanarayanan'}
  


  Step 33: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0bf052cc5c9492805"
  ],
  "Output2": [
    "{'_Environment': 'NON-PRODUCTION', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-70JQC5KR77D', 'Name': 'WIN-70JQC5KR77D', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'RIGHTSIZED': 'TRUE', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-035a56b16e4e64f2f', '_BusinessSegment': '1070'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0bf052cc5c9492805\",\"output2\":\"{'_Environment': 'NON-PRODUCTION', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-70JQC5KR77D', 'Name': 'WIN-70JQC5KR77D', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'RIGHTSIZED': 'TRUE', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-035a56b16e4e64f2f', '_BusinessSegment': '1070'}\",\"tags\":\"{'_Environment': 'NON-PRODUCTION', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-70JQC5KR77D', 'Name': 'WIN-70JQC5KR77D', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'RIGHTSIZED': 'TRUE', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-035a56b16e4e64f2f', '_BusinessSegment': '1070'}\"}}"
  ],
  "ScriptExecutionId": [
    "65866137-21b4-481e-a773-a62a938d1c1a"
  ],
  "tags": [
    "{'_Environment': 'NON-PRODUCTION', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-70JQC5KR77D', 'Name': 'WIN-70JQC5KR77D', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'RIGHTSIZED': 'TRUE', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-035a56b16e4e64f2f', '_BusinessSegment': '1070'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0bf052cc5c9492805"
    tags: {'_Environment': 'NON-PRODUCTION', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-70JQC5KR77D', 'Name': 'WIN-70JQC5KR77D', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'RIGHTSIZED': 'TRUE', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-035a56b16e4e64f2f', '_BusinessSegment': '1070'}
  


  Step 33: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0b4cf1081d4ab99a0"
  ],
  "Output2": [
    "{'_ProvisioningJustification': 'PIR-598', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'WFID5XGESQL-2', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CorporateDivision': 'Crossmatch', '_CostCenter': '4100', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0faf92bf7ea0a452c', '_BusinessSegment': '1070'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0b4cf1081d4ab99a0\",\"output2\":\"{'_ProvisioningJustification': 'PIR-598', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'WFID5XGESQL-2', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CorporateDivision': 'Crossmatch', '_CostCenter': '4100', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0faf92bf7ea0a452c', '_BusinessSegment': '1070'}\",\"tags\":\"{'_ProvisioningJustification': 'PIR-598', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'WFID5XGESQL-2', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CorporateDivision': 'Crossmatch', '_CostCenter': '4100', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0faf92bf7ea0a452c', '_BusinessSegment': '1070'}\"}}"
  ],
  "ScriptExecutionId": [
    "7beefdf1-1629-4a96-bf37-be5f2ae3a27b"
  ],
  "tags": [
    "{'_ProvisioningJustification': 'PIR-598', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'WFID5XGESQL-2', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CorporateDivision': 'Crossmatch', '_CostCenter': '4100', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0faf92bf7ea0a452c', '_BusinessSegment': '1070'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0b4cf1081d4ab99a0"
    tags: {'_ProvisioningJustification': 'PIR-598', '_Environment': 'NON-PRODUCTION', '_BusinessArea': 'IAMS', '_BackupPlan': 'BRONZE', '_BusinessContactEmail': '<EMAIL>', 'Name': 'WFID5XGESQL-2', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'map-migrated': 'mig47460', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CorporateDivision': 'Crossmatch', '_CostCenter': '4100', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_BusinessSegmentDescription': 'PIAM', '_ProvisioningEntity': 'Caylent', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0faf92bf7ea0a452c', '_BusinessSegment': '1070'}
  


  Step 33: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-071ced3bffecec64b"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-ANTLGRAPVD5', 'Name': 'AUE1ADEVMSENG35', '_SupportTier': 'TIER3', '_InstanceSource': 'USFRE', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-30d7a158285071a6d', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-0ed6b46b70c8481ef', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-071ced3bffecec64b\",\"output2\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-ANTLGRAPVD5', 'Name': 'AUE1ADEVMSENG35', '_SupportTier': 'TIER3', '_InstanceSource': 'USFRE', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-30d7a158285071a6d', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-0ed6b46b70c8481ef', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-ANTLGRAPVD5', 'Name': 'AUE1ADEVMSENG35', '_SupportTier': 'TIER3', '_InstanceSource': 'USFRE', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-30d7a158285071a6d', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-0ed6b46b70c8481ef', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "0e2a6932-4c10-4885-a8f2-74dad7c946a8"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-ANTLGRAPVD5', 'Name': 'AUE1ADEVMSENG35', '_SupportTier': 'TIER3', '_InstanceSource': 'USFRE', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-30d7a158285071a6d', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-0ed6b46b70c8481ef', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-071ced3bffecec64b"
    tags: {'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-ANTLGRAPVD5', 'Name': 'AUE1ADEVMSENG35', '_SupportTier': 'TIER3', '_InstanceSource': 'USFRE', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-30d7a158285071a6d', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Golden Eagles', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Golden Eagles', '_InstanceId': 'i-0ed6b46b70c8481ef', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Step 33: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0fd1a3e82470d9a48"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0fd1a3e82470d9a48\",\"output2\":\"{'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "9e677d78-be69-43c9-9585-8b107f473b59"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0fd1a3e82470d9a48"
    tags: {'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Step 19: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-090bfcf1aa64009e8"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-090bfcf1aa64009e8\",\"output2\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "964f051b-7d95-4352-93b4-a28704b46616"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-090bfcf1aa64009e8"
    tags: {'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Step 23: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0de619683dcc570ca"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0de619683dcc570ca\",\"output2\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "bec6d59a-7a56-4f21-a83c-6dc230ee1f01"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0de619683dcc570ca"
    tags: {'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Step 23: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0c7c9fd2e66650898"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0c7c9fd2e66650898\",\"output2\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "d3827e50-d61e-412c-87c3-15e7083424ae"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0c7c9fd2e66650898"
    tags: {'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Step 19: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-077a0a0a733566504"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-077a0a0a733566504\",\"output2\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "5063bc03-951a-4c7e-93f3-5c65569fbb6a"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-077a0a0a733566504"
    tags: {'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Step 19: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-06fb85d74aba7c07f"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-06fb85d74aba7c07f\",\"output2\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "c78e6c8f-35f7-42ab-b22a-1d96bb1e99d9"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-06fb85d74aba7c07f"
    tags: {'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Step 22: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-053a47d8b8dfb47b7"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-053a47d8b8dfb47b7\",\"output2\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "8b69c897-e716-4fb9-b799-d0a99c6ec217"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-053a47d8b8dfb47b7"
    tags: {'_Environment': 'PRODUCTION', '_DatabaseType': '', '_BackupPlan': 'BRONZE', 'Old Name': 'WIN-3VCHDJ99C69', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER3', 'Name': 'AUE1ADEVMSENG36', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3d5e70b6756b41f28', '_OperatingSystemName': 'WINDOWS SERVER 2016', 'Terraform': '', '_CostCenter': '4100', '_CorporateDivision': 'HID Global', '_CostCenterDescription': 'Product Development', '_FunctionalArea': 'R and D', '_SupportContact': '', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': 'Orca', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_TechnicalContactEmail': '<EMAIL>', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Orca', '_InstanceId': 'i-0c46ef32dac9d6f8c', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Final Outputs: {}
    useroutput: 
    tags: 
  


  Step 23: Output_3
  Status: Success
  Outputs: {
  "Output1": [
    "ami-0f4d20caa663b5a37"
  ],
  "Output2": [
    "{'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ],
  "OutputPayload": [
    "{\"ExecutionLog\":\"hello world\\n\",\"Payload\":{\"output1\":\"ami-0f4d20caa663b5a37\",\"output2\":\"{'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\",\"tags\":\"{'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}\"}}"
  ],
  "ScriptExecutionId": [
    "66b37fca-07ee-4a12-87ee-f06d6aa722d0"
  ],
  "tags": [
    "{'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}"
  ]
}
  


  Final Outputs: {}
    useroutput: "ami-0f4d20caa663b5a37"
    tags: {'_Environment': 'PRODUCTION', '_BackupPlan': 'GOLD', '_InstanceSource': 'USFRE', '_SupportTier': 'TIER1', 'Name': 'WFID518GESQL', 'map-migrated': 'mig47460', 'AWSApplicationMigrationServiceSourceServerID': 's-3a1aa98f2b45c7f73', '_OperatingSystemName': 'WINDOWS SERVER 2016', '_CostCenter': '4100', '_CorporateDivision': 'Crossmatch', '_SupportContact': '', '_FunctionalArea': 'R and D', '_CostCenterDescription': 'Product Development', '_ProvisioningEntity': 'Caylent', '_TechnicalContact': '', '_ProvisioningJustification': 'PIR-598', '_BusinessArea': 'IAMS', '_BusinessContactEmail': '<EMAIL>', 'AWSApplicationMigrationServiceManaged': 'mgn.amazonaws.com', '_NetworkLocation': 'INTERNAL', '_BusinessAreaDescription': 'IAMS - Workforce ID', '_BusinessSegmentDescription': 'PIAM', '_BusinessContact': 'Daljit Bahrey', '_InstanceId': 'i-0d4b7b5b4deed4843', '_BusinessSegment': '1070', '_ProvisioningEngineer': 'TBD'}
  


  Final Outputs: {
  "Create_AMI.imageid": [
    "No output available yet because the step is not successfully executed"
  ],
  "Create_AMI_1.ImageId": [
    "ami-089a86e6ccce5498d"
  ],
  "Get_Ec2_Tags_AMI.EC2Tags": [
    "{\"_BusinessSegment\":\"9000\",\"_FunctionalArea\":\"IT\",\"_ProvisioningJustification\":\"RITM0000000\",\"_SupportTierDescription\":\"ON-DEMAND (automatic first backup)\",\"_InstanceSource\":\"new\",\"_SupportTier\":\"TIER3\",\"_BusinessArea\":\"OtherBA\",\"_ProvisioningEngineer\":\"Prasana Srinivasan\",\"_CostCenter\":\"6420\",\"Name\":\"AUE2ALIVMSAPP01\",\"_CostCenterDescription\":\"Global Infrastructure and NOC\",\"_BusinessContactEmail\":\"<EMAIL>\",\"_ProvisioningEntity\":\"HID Engineer\",\"_BusinessContact\":\"Prasana Srinivasan\",\"_BusinessSegmentDescription\":\"HID Global\",\"_TechnicalContact\":\"prasana srinivasan\",\"_TechnicalContactEmail\":\"<EMAIL>\",\"_BackupPlan\":\"BRONZE\",\"_CorporateDivision\":\"HID Global\",\"_Environment\":\"PRODUCTION\",\"map-migrated\":\"migV3IGSCYWB3\",\"_NetworkLocation\":\"INTERNAL\"}"
  ],
  "Get_Ec2_Tags_AMI_KMS_KEY.EC2Tags": [
    "No output available yet because the step is not successfully executed"
  ]
}
    useroutput: 
    tags: 
  

