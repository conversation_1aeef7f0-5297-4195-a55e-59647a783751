.migration {
  font-family: Arial, sans-serif;
  padding: 20px;
}

.migration-title {
  font-size: 45px; /* Extra large size */
  font-weight: 800; /* Maximum boldness */
  color: #00549B; /* Ensure it matches the color scheme */
  margin-bottom: 10px; /* Add space below */
  line-height: 1.1; /* Tighten line height */
  text-align: center; /* Force align to the left */
  text-transform: uppercase;/* Force all uppercase letters */
  letter-spacing: 0.8px;
  
}
.migration-loading {
  color: #007bff;
}

.migration-instance-names {
  margin-bottom: 20px;
}
.hover-card {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #fff;
  border: 1px solid #ccc;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  width: 50%;
  max-height: 80%;
  overflow-y: auto;
  z-index: 1000;
}
.loading-page {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f4f4f4;
  color: #333;
  font-family: Arial, sans-serif;
}

.hover-content {
  padding: 20px;
}

.close-button {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #f44336;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  width: 30px;
  height: 30px;
  font-size: 16px;
  font-weight: bold;
}

.close-button:hover {
  background: #d32f2f;
}
.migration-instance {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.migration-input {
  padding: 5px;
  margin-right: 10px;
  border: 1px solid #ccc;
  border-radius: 5px;
}

.migration-remove-btn,
.migration-add-btn,
.migration-fetch-btn,
.migration-submit-btn {
  padding: 5px 10px;
  margin-top: 10px;
  border: none;
  background-color: #007bff;
  color: white;
  border-radius: 5px;
  cursor: pointer;
}

.migration-remove-btn:hover,
.migration-add-btn:hover,
.migration-fetch-btn:hover,
.migration-submit-btn:hover {
  background-color: #0056b3;
}

.migration-form {
  margin-top: 20px;
}

.migration-form-group {
  margin-bottom: 10px;
}

.migration-label {
  display: block;
  margin-bottom: 5px;
}

.main-container {
  display: flex;
  transition: all 0.3s ease-in-out;
}
.form-button {
  background-color: #007bff; /* Primary Blue */
  color: #ffffff; /* White Text */
  font-size: 16px; /* Font Size */
  margin-top: 30px;
  font-weight: 600; /* Slightly Bold */
  padding: 12px 20px; /* Padding */
  border: none; /* Remove Border */
  border-radius: 5px; /* Rounded Corners */
  cursor: pointer; /* Pointer Cursor */
  transition: all 0.3s ease; /* Smooth Transition */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Subtle Shadow */
}

/* Hover Effect */
.form-button:hover {
  background-color: #0056b3; /* Darker Blue */
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15); /* Slightly Larger Shadow */
  transform: translateY(-2px); /* Lift Effect */
}

/* Active/Pressed Effect */
.form-button:active {
  background-color: #003f7f; /* Even Darker Blue */
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2); /* Smaller Shadow */
  transform: translateY(0); /* Return to Original Position */
}

/* Focus Effect */
.form-button:focus {
  outline: none; /* Remove Default Outline */
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5); /* Blue Glow */
}

/* Disabled State */
.form-button:disabled {
  background-color: #cccccc; /* Gray Background */
  color: #666666; /* Dimmed Text */
  cursor: not-allowed; /* Not-Allowed Cursor */
  box-shadow: none; /* Remove Shadow */
  transform: none; /* No Animation */
}
/* Table Container */
.custom-table {
width: 100% !important;
border-collapse: collapse !important;
font-family: Arial, sans-serif !important;
/* background-color: #ffffff !important; */
margin: 20px 0 !important;
box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}



.table-wrapper {
  min-height: 30vh;
  max-height: 80vh; /* Adjust based on requirement */
  overflow-y: auto; /* Enables vertical scrolling */
  border: 1px solid #ddd;
  position: relative;
  
}
.table-header th {
  position: sticky!important;
  top: 0 !important;
   /* Keeps header visible */
  z-index: 10!important;
  padding: 10px;
  text-align: left;
  border-bottom: 2px solid #ddd;
}

.table-body tr:nth-child(even) {
  background-color: #f9f9f9;
}

.table-row:hover {
  background-color: #f1f1f1;
}

.row-selected {
  background-color: #d3e0ff;
}

/* Table Header */
.table-header {
  position: sticky;
  top: 0;
  background: white;
  z-index: 2;
  box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2); /* Adds shadow effect */
}
tbody {
  overflow-y: auto;
}



.table-container {
  max-width: 100vw; /* Prevents page overflow */
  width: auto;
  overflow-x: auto;
  margin-left: 50px;
  position: relative; /* Required for 'left' to work */
  left:0;
  margin-right: 25px; /* Enables horizontal scrolling */
  transition: transform 0.3s ease-in-out, width 0.3s ease-in-out;
}
.table-container.shifted {
  max-width: calc(100vw - 370px); /* Prevents page overflow */
  width: auto;
  overflow-x: auto;
  margin-left: 25px;
  left:320px;
  margin-right: 25px ;
  transition: left 0.3s ease-in-out ;
}
.table-header th {
padding: 12px 15px !important;
border-bottom: 2px solid #ddd !important;
}

/* Table Body */
.table-body .table-row {
transition: background-color 0.3s ease !important;
}

.table-body .table-row:nth-child(even) {
background-color: #f9f9f9 !important;
}

.table-body .table-row:hover {
background-color: #f1f5ff !important;
}

/* Selected Row */
.table-body .table-row.row-selected {
background-color: #dbeafe !important;
}
.fixed-top-section {
  position: fixed;
  left: -300px;
  top: 0;
  height: 100vh;
  width: 320px; /* Adjust width as needed */
  background-color: rgb(255, 255, 255);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease-in-out;
  padding: 10px;
  padding-right: 35px;
  overflow-y: auto;
  z-index: 10;
  transition: left 0.1s ease-in-out, width 0.1s ease-in-out;
}
.fixed-top-section.expanded {
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 300px; /* Adjust width as needed */
  background-color: rgb(255, 255, 255);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease-in-out;
  
  overflow-y: auto;
  z-index: 10;
  transition: left 0.3s ease-in-out , width 0.3s ease-in-out;
}

.fixed-top-section:hover{
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  width: 300px; /* Adjust width as needed */
  background-color: rgb(251, 251, 251);
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
  
  
  overflow-y: auto;
  z-index: 10;
  transition: left 0.3s ease-in-out 0.5s, width 0.3s ease-in-out 0.5s;
}



  
/* Table Cells */
.table-row td {
padding: 10px 15px !important;
border-bottom: 1px solid #ddd !important;
font-size: 14px !important;
color: #333 !important;
}

/* Responsive Table */
@media screen and (max-width: 768px) {
.custom-table {
  font-size: 12px !important;
}

.table-header th,
.table-row td {
  padding: 8px 10px !important;
}
}
.migration-form-container {
  width: 100px;
  background-color: #fb0000;
  transition: width 0.3s ease-in-out;
  overflow: hidden;
  cursor: pointer;
}


.collapsible{
  position: absolute;
  left:0;
  z-index: 11;
  transition: left 0.1s ease-in-out, width 0.1s ease-in-out;
}

.collapsible.expanded{
  left: 280px;
  transition: left 0.3s ease-in-out , width 0.3s ease-in-out;
}
/* Collapsible header */
.collapsible.hover-effect {
  left: 280px;
  transition: left 0.3s ease-in-out 0.5s, width 0.3s ease-in-out 0.5s;
}
.hidden {
display: none;
}

/* Arrow rotation */
.collapsible-icon {
  
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ffffff;
  
  
  
}

/* Arrow Icon */
.arrow-icon {
  font-size: 40px;
  color: #007bff; 
  transition: transform 0.3s ease-in-out;
  
  z-index: 11;
}

.arrow-icon.rotated {
  transform: rotate(180deg);
  z-index: 11;
}

.poll-date {
  font-size: 12px;  /* Smaller font size */
  color: #333;      /* Dark color for contrast */
  position: fixed;  /* Fixed positioning */
  top: 10px;        /* 10px from the top */
  right: 20px;      /* 20px from the right */
  z-index: 999;     /* Ensure it's visible above other elements */
  font-weight: normal; /* Normal weight */
  background-color: #fff; /* Optional: background color to make text stand out */
  padding: 5px;      /* Optional: padding to create space around text */
  border-radius: 5px; /* Optional: rounded corners for better appearance */
}
