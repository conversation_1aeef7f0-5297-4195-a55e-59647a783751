import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import axios from 'axios';
import { useNavigate } from 'react-router-dom';
import Navbar from './Navbar'; // Add Navbar for consistency
import './AttachVolume.css'; // Updated CSS for better design

const AttachVolume = () => {
    const navigate = useNavigate();
    const [accounts, setAccounts] = useState([]);
    const [regions, setRegions] = useState([]);
    const [instances, setInstances] = useState([]);
    const [volumes, setVolumes] = useState([]);
    const [selectedAccount, setSelectedAccount] = useState('');
    const [selectedRegion, setSelectedRegion] = useState('');
    const [selectedInstance, setSelectedInstance] = useState('');
    const [volumeOption, setVolumeOption] = useState('existing');
    const [selectedVolume, setSelectedVolume] = useState('');
    const [newVolumeSize, setNewVolumeSize] = useState('');
    const [newVolumeType, setNewVolumeType] = useState('gp3');
    const [deviceName, setDeviceName] = useState('');
    const [message, setMessage] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [user, setUser] = useState({
        email: '<EMAIL>',
        displayName: 'Guest',
        firstName: 'Guest',
    });
    const [accountId, setAccountId] = useState([]);
    const [data, setData] = useState([]);
    const [accountNames, setAccountNames] = useState([]);
    const[existinginstanceData,setexistinginstanceData]=useState([]);
    const[tagdetails,settagdetails]=useState([]);
    const [selectedAZ, setSelectedAZ] = useState('');
    const [availabilityZones, setAvailabilityZones] = useState([]);
    const [platformDetails, setPlatformDetails] = useState([]);
    const[platform,setPlatform] = useState('');

    // Check authentication and fetch user profile
    useEffect(() => {
        async function checkAuth() {
          try {
            const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
            setUser(response.data.user);
            
          } catch (error) {
          
            setUser(null); // Set user to null in case of an error
          }
          
        }
        checkAuth();
      },[navigate]);

    // Fetch user data and account details
    useEffect(() => {
        axios.get('https://umanage.dev.hidglobal.com/api/user')
          .then(response => {
            const fetchedData = response.data;
            // console.log('Fetched user data:', fetchedData);
            // console.log(user);
            const userEntry = fetchedData.find(entry => entry.user === user.email);
            // console.log('User entry:', userEntry);
      
            if (userEntry) {
              const accountIds = userEntry.accounts.split(',').map(account => account.trim());
              // console.log('Parsed account IDs:', accountIds);
              setAccountId(accountIds);
            } else {
              setAccountId([]);
            }
          })
          .catch(error => {
            // console.error('Error fetching user accounts:', error);
          });
      }, [user]);

    // Fetch regions and instances based on account and region
    useEffect(() => {
        if (accountId.length > 0) {
            axios.get('https://umanage.dev.hidglobal.com/api/s3')
                .then(response => {
                    let fetchedData = response.data;
                    // console.log('Fetched S3 data:', fetchedData);
    
                    // Filter data based on accountId associated with the user
                    fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
                    // console.log('Filtered S3 data:', fetchedData);
    
                    // Create account options with label as AccountName and value as accountId
                    const accountOptions = Array.from(
                        new Map(fetchedData.map(item => [item.accountId, item])).values()
                    ).map(item => ({
                        value: item.accountId,
                        label: item.AccountName,
                    }));
    
                    // Set account options for the select dropdown
                    setAccountNames(accountOptions);
                    setPlatformDetails(fetchedData); // Save filtered data for further use
                })
                .catch(error => {
                    // console.error('Error fetching S3 data:', error);
                });
        }
    }, [accountId]);
      console.log("PlatformDetails",platformDetails);
        console.log("AccountNames",accountNames);
      useEffect(() => {
        if (accountId.length > 0) {
          axios.get('https://umanage.dev.hidglobal.com/api/ebs')   
            .then(response => {
              let fetchedData = response.data;
              // console.log('Fetched S3 data:', fetchedData);
      
              setData(fetchedData);
              setexistinginstanceData(fetchedData);
              tagdetails(fetchedData);
              // console.log('Filtered S3 data:', fetchedData);
      
              
            })
            .catch(error => {
              // console.error('Error fetching S3 data:', error);
            });
        }
      }, [accountId]);
      console.log("Data",data);
      // Ef
      const handleAccountChange = (selectedOption) => {
        setSelectedAccount(selectedOption.value);

        // Filter data for AZs based on the selected account
        const filteredData = data.filter(item => item.accountId === selectedOption.value);
        const uniqueAZs = [...new Set(filteredData.map(item => item.availabilityzone))];
        setAvailabilityZones(uniqueAZs.map(az => ({ value: az, label: az })));

        // Reset dependent fields
        setSelectedAZ('');
        setInstances([]);
        setVolumes([]);
    };

    // Handle AZ selection
    const handleAZChange = (selectedOption) => {
        setSelectedAZ(selectedOption.value);

        // Filter instances based on the selected account and AZ
        const filteredInstances = data.filter(item =>
            item.accountId === selectedAccount && item.availabilityzone === selectedOption.value
        );
        setInstances(filteredInstances.map(instance => ({
            value: instance,
            label: `${instance.InstanceName} (${instance.InstanceId})`,
        })));

        // Reset volumes
        setVolumes([]);
    };
    console.log("instances",instances);
   
    const handleInstanceChange = (selectedOption) => {
        setSelectedInstance(selectedOption.value);
    
        // Check the InstanceId in platformDetails
        const instanceDetails = platformDetails.find(item => item.InstanceId === selectedOption.value.InstanceId);
    
        setPlatform(instanceDetails);
            
    };
    console.log("instanceDetails",platform);
    console.log("Platform",platform.platform);
    console.log("selectedInstance",selectedInstance);
    // Handle volume option selection
    const handleVolumeOptionChange = (option) => {
        setVolumeOption(option);

        if (option === 'existing') {
            // Filter volumes based on the selected account, AZ, and state = available
            const filteredVolumes = existinginstanceData.filter(item =>
                item.accountId === selectedAccount &&
                item.availabilityzone === selectedAZ &&
                item.state === 'available'
            );
            setVolumes(filteredVolumes.map(volume => ({
                value: volume.volumeid,
                label: `${volume.volumeid} `,
            })));
        }
    };
    console.log(selectedInstance);
    const handleSubmit = () => {
        setIsProcessing(true);
    
        // Validate required fields
        if (!selectedAccount || !selectedAZ || !selectedInstance || (volumeOption === 'existing' && !selectedVolume)) {
            setMessage('Please fill in all required fields.');
            setIsProcessing(false);
            return;
        }
    
        // Extract instance details
        
        
    
        // Extract volume details if 'existing' volume option is selected
        const volumeDetails = volumes.find(volume => volume.value === selectedVolume);
    
        // Prepare payload with mapped details
        const payload = {
            firstname: user.firstName,
            email: user.email,
            accountname: platform.AccountName,
            instanceId: platform.InstanceId,
            InstanceName: platform.InstanceName,
            CostCenter: platform.CostCenter,
            CostCenterDescription: platform.CostCenterDescription,
            SupportTier: platform.SupportTier,
            SupportTierDescription: platform.SupportTierDescription,
            InstanceSource: platform.InstanceSource,
            ProvisioningEntity: platform.ProvisioningEntity,
            BusinessArea: platform.BusinessArea,
            BusinessContact: platform.BusinessContact,
            BusinessContactEmail: platform.BusinessContactEmail,
            BusinessSegment: platform.BusinessSegment,
            mapmigrated: platform.mapmigrated,
            BusinessSegmentDescription: platform.BusinessSegmentDescription,
            TechnicalContact: platform.TechnicalContact,
            TechnicalContactEmail: platform.TechnicalContactEmail,
            Environment: platform.Environment,
            NetworkLocation: platform.NetworkLocation,
            FunctionalArea: platform.FunctionalArea,
            ProvisioningEngineer: platform.ProvisioningEngineer,
            BackupPlan: platform.BackupPlan,
            ProvisionJustification: platform.ProvisioningJustification,
            
            volumesize: newVolumeSize ,
            DeviceName: deviceName ,
            volumetype: newVolumeType,
            accountId: selectedAccount,
            availabilityZone: selectedAZ,
            existing:volumeOption,
            volumeexistid: selectedVolume,
            region:platform.Region,
        };
    
        // Log the payload
        console.log("Payload:", payload);
    
        // Submit data to the backend
        axios.post('https://umanage.dev.hidglobal.com/api/ebs/ebs-attach', payload)
            .then(response => {
                setMessage('Volume attached successfully!');
            })
            .catch(error => {
                setMessage('Failed to attach volume. Please try again.');
                console.error('Error attaching volume:', error);
            })
            .finally(() => setIsProcessing(false));
    };

    return (
       <div className="attach-volume-page">
            <Navbar />
            <div className="container mt-4">
                <h1 className="page-title text-center mb-4">Attach Volume</h1>

                <div className="row">
                    {/* Account Selection */}
                    <div className="col-md-4">
                        <label className="form-label">Select Account:</label>
                        <Select
                            options={accountNames}
                            onChange={handleAccountChange}
                            placeholder="Select an account"
                        />
                        <p className="form-description">Choose the account where the instance resides.</p>
                    </div>

                    {/* Region Selection */}
                    <div className="col-md-4">
                        <label className="form-label">Select AZ:</label>
                        <Select
                            options={availabilityZones}
                            onChange={handleAZChange}
                            placeholder="Select an AZ"
                            isDisabled={!selectedAccount}
                        />
                        <p className="form-description">Select the region of the instance.</p>
                    </div>

                    {/* Instance Selection */}
                    
                </div>
                <div className="col-md-8">
                        <label className="form-label">Select Instance:</label>
                        <Select
                            options={instances}
                            onChange={handleInstanceChange}
                            placeholder="Select an instance"
                            isDisabled={!selectedAZ}
                        />
                        <p className="form-description">Choose the instance to attach the volume to.</p>
                    </div>
                {/* Volume Option Toggle */}
                <div className="row mt-4">
                    <div className="col-md-12">
                        <label className="form-label">Attach Volume Type:</label>
                        <div className="radio-group d-flex gap-4">
                            <div className="radio-option">
                                <input
                                    type="radio"
                                    id="existingVolume"
                                    name="volumeOption"
                                    value="existing"
                                    checked={volumeOption === 'existing'}
                                    onChange={() => handleVolumeOptionChange('existing')}
                                />
                                <label htmlFor="existingVolume">Existing Volume</label>
                            </div>
                            <div className="radio-option">
                                <input
                                    type="radio"
                                    id="newVolume"
                                    name="volumeOption"
                                    value="new"
                                    checked={volumeOption === 'new'}
                                    onChange={() => handleVolumeOptionChange('new')}
                                />
                                <label htmlFor="newVolume">New Volume</label>
                            </div>
                        </div>
                        <p className="form-description">Choose whether to attach an existing volume or create a new one.</p>
                    </div>
                </div>

                {/* Existing Volume Selection */}
                {volumeOption === 'existing' && (
                    <div className="new-volume-config mt-4">
                        <h3 className="config-title">Existing Volume Configuration</h3>
                        <div className="row">
                        <div className="col-md-6">
                            <label className="form-label">Select Volume:</label>
                            <Select
                                options={volumes}
                                onChange={option => setSelectedVolume(option.value)}
                                placeholder="Select a volume"
                                isDisabled={!selectedAZ}
                            />
                            <p className="form-description">Select an existing volume to attach to the instance.</p>
                        </div>
                        <div className="col-md-6">
                            <label className="form-label">Device Name:</label>
                            <Select
                                options={
                                    platform.platform === "windows"
                                        ? [
                                            { value: "xvda", label: "xvda" },
                                            { value: "xvdb", label: "xvdb" },
                                            { value: "xvdc", label: "xvdc" },
                                            { value: "xvdd", label: "xvdd" },
                                        ]
                                        : [{ value: "/dev/sda", label: "/dev/sda" }, 
                                           { value: "/dev/sdb", label: "/dev/sdb" },
                                           {value: "/dev/sdc", label: "/dev/sdc" },
                                           {value: "/dev/sdd", label: "/dev/sdd" }]
                                }
                                onChange={(selectedOption) => setDeviceName(selectedOption.value)}
                                placeholder="Select a device name"
                            />
                            <p className="form-description">Specify the device name for the new volume.</p>
                        </div>
                        </div>
                        </div>
                    )}

                {/* New Volume Configuration */}
                {volumeOption === 'new' && (
                    <div className="new-volume-config mt-4">
                        <h3 className="config-title">New Volume Configuration</h3>
                        <div className="row">
                            <div className="col-md-4">
                                <label className="form-label">Volume Size (GiB):</label>
                                <input
                                    type="number"
                                    className="form-control"
                                    value={newVolumeSize}
                                    onChange={e => setNewVolumeSize(e.target.value)}
                                    placeholder="Enter size"
                                    min="1"
                                />
                                <p className="form-description">Specify the size of the new volume in GiB.</p>
                            </div>
                            <div className="col-md-4">
                                <label className="form-label">Volume Type:</label>
                                <select
                                    className="form-select"
                                    value={newVolumeType}
                                    onChange={e => setNewVolumeType(e.target.value)}
                                >
                                    <option value="gp3">General Purpose SSD (gp3)</option>
                                    <option value="gp2">General Purpose SSD (gp2)</option>
                                    <option value="io1">Provisioned IOPS SSD (io1)</option>
                                    <option value="io2">Provisioned IOPS SSD (io2)</option>
                                    <option value="st1">Throughput Optimized HDD (st1)</option>
                                    <option value="sc1">Cold HDD (sc1)</option>
                                    <option value="magnetic">Magnetic</option>
                                </select>
                                <p className="form-description">Choose the type of the new volume.</p>
                            </div>
                            <div className="col-md-4">
                            <label className="form-label">Device Name:</label>
                            <Select
                                options={
                                    platform.platform === "windows"
                                        ? [
                                            { value: "xvda", label: "xvda" },
                                            { value: "xvdb", label: "xvdb" },
                                            { value: "xvdc", label: "xvdc" },
                                            { value: "xvdd", label: "xvdd" },
                                        ]
                                        : [{ value: "/dev/sda", label: "/dev/sda" }, 
                                           { value: "/dev/sdb", label: "/dev/sdb" },
                                           {value: "/dev/sdc", label: "/dev/sdc" },
                                           {value: "/dev/sdd", label: "/dev/sdd" }]
                                }
                                onChange={(selectedOption) => setDeviceName(selectedOption.value)}
                                placeholder="Select a device name"
                            />
                            <p className="form-description">Specify the device name for the new volume.</p>
                        </div>
                        </div>
                    </div>
                )}

                {/* Submit Button */}
                <div className="row mt-4">
                    <div className="col-md-12 text-center">
                        <button
                            className="btn btn-primary submit-button"
                            onClick={handleSubmit}
                            
                        >
                            {isProcessing ? 'Processing...' : 'Attach Volume'}
                        </button>
                        {message && <p className="message mt-3">{message}</p>}
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AttachVolume;