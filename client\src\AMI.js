import React from 'react';

const AMI = () => (
  <div>
    <h2>Amazon Machine Images (AMIs)</h2>
    <p>
      An Amazon Machine Image (AMI) is a template that contains a software configuration (operating system, application server, applications) 
      required to launch an instance. AMIs are the starting point for creating instances and allow you to quickly replicate environments.
    </p>
    <h3>Common AMI Types</h3>
    <ul>
      <li>Amazon Linux AMI: Optimized for Amazon EC2, commonly used for web applications.</li>
      <li>Windows Server AMI: Supports Windows-based applications and services.</li>
      <li>Ubuntu AMI: Known for open-source applications and services.</li>
    </ul>
    <p>
      AMIs can be public, private, or shared, enabling flexible options for deploying instances in different environments.
      Choosing the correct AMI ensures compatibility and smooth integration with other services.
    </p>
  </div>
);

export default AMI;
