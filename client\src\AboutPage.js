// import React, { useState } from 'react';
// import NamingCriteria from './About/NamingCriteria';
// import InstanceType from './About/InstanceType';
// import AboutCreate from './About/AboutCreate';
// import AboutStart from './About/AboutStart';
// import AboutStop from './About/AboutStop';
// import AboutTerminate from './About/AboutTerminate';
// import AboutDomain from './About/AboutDomain';
// import AboutAbout from './About/AboutAbout';
// import FAQ from './About/FAQ'; // Import the new FAQ component
// import './about.css';
// import AboutBA from './About/AboutBA'; // Import the new
// import AboutCostCenter from './About/AboutCostCenter';
// import AboutAccount from './About/AboutAccount';
// import AboutResize from './About/AboutResize';
// import AboutScheduler from './About/AboutScheduler';
// import AboutVolumeAttach from './About/AboutVolumeAttach';
// import AboutVolumeDetach from './About/AboutVolumeDetach';
// import AboutRestart from './About/AboutRestart';

// const About = () => {
//   const [selectedSection, setSelectedSection] = useState('AboutAbout');
//   const [expandedCategory, setExpandedCategory] = useState(''); // Tracks expanded category

//   const toggleCategory = (category) => {
//     setExpandedCategory(expandedCategory === category ? '' : category);
//   };

//   const renderContent = () => {
//     switch (selectedSection) {
//       case 'NamingCriteria':
//         return <NamingCriteria />;
//       case 'InstanceType':
//         return <InstanceType />;
//         case 'AboutBA':
//           return <AboutBA />;
//           case 'AboutAccount':
//           return <AboutAccount />;
//           case 'AboutCostCenter':
//             return <AboutCostCenter />;
//           case 'AboutCreate':
//             return <AboutCreate />;
//       case 'AboutStart':
//         return <AboutStart />;
//       case 'AboutStop':
//         return <AboutStop />;
//       case 'AboutTerminate':
//         return <AboutTerminate />;
//         case 'AboutDomain':
//           return <AboutDomain />;
//       case 'AboutVolumeAttach':
//         return <AboutVolumeAttach />; 
//       case 'AboutVolumeDetach':
//         return <AboutVolumeDetach />;
//       case 'AboutScheduler':
//         return <AboutScheduler />;
//       case 'AboutRestart':
//         return <AboutRestart />;
//       case 'AboutResize':
//         return <AboutResize />;
//           case 'AboutAbout':
//             return <AboutAbout />;
//       case 'FAQ':
//         return <FAQ />; // Add a case for the FAQ page
//       default:
//         return <AboutAbout />;
//     }
//   };

//   return (
//     <div className="about-page">
//       {/* Sidebar */}
      
//       <div className="about-sidebar">
//         <h3>Help</h3>
//         <ul>
//           {/* Create Category */}
//           <li>
//             <div onClick={() => toggleCategory('Start')}>
//               <span>{expandedCategory === 'Start' ? '▼' : '►'}</span> Service Action
//             </div>
//             {expandedCategory === 'Start' && (
//               <ul>
//                 <li onClick={() => setSelectedSection('AboutStart')}>Start Instance</li>
//                 <li onClick={() => setSelectedSection('AboutStop')}>Stop Instance</li>
//                 <li onClick={() => setSelectedSection('AboutResize')}>Resize</li>
//                 <li onClick={() => setSelectedSection('AboutRestart')}>Restart</li>
//                 <li onClick={() => setSelectedSection('AboutTerminate')}>Terminate Instance</li>
//                 <li onClick={() => setSelectedSection('AboutDomain')}>Domain Checker</li>
//                 <li onClick={() => setSelectedSection('AboutScheduler')}>Scheduler</li>
//                 <li onClick={() => setSelectedSection('AboutVolumeAttach')}>Volume Attach</li>
//                 <li onClick={() => setSelectedSection('AboutVolumeDetach')}>Volume Detach</li>
                
//               </ul>
//             )}
//           </li>
//           <li>
//             <div onClick={() => toggleCategory('Provision Action')}>
//               <span>{expandedCategory === 'Provision Action' ? '▼' : '►'}</span> Provision Action
//             </div>
//             {expandedCategory === 'Provision Action' && (
//               <ul>
                
//                 <li onClick={() => setSelectedSection('AboutCreate')}>Creating Instance</li>

//               </ul>
//             )}
//           </li>

//           {/* Start Category */}
          

//           {/* Stop Category */}
          
//           {/* FAQ Category */}
//           <li onClick={() => setSelectedSection('FAQ')}>
//             FAQ
//           </li>
//           <li onClick={() => setSelectedSection('NamingCriteria')}>Naming Criteria</li>
//                 <li onClick={() => setSelectedSection('InstanceType')}>Instance Type</li>
//                 <li onClick={() => setSelectedSection('AboutCostCenter')}>Cost Centers</li>
//                 <li onClick={() => setSelectedSection('AboutBA')}>Business Segments</li>
//                 <li onClick={() => setSelectedSection('AboutAccount')}>AWS Accounts</li>
//           <li onClick={() => setSelectedSection('AboutAbout')}>
//             About
//           </li>
//         </ul>
//         <p className='version'>v1.11.24.0</p>
//       </div>

//       {/* Content Section */}
//       <div className="about-content">
//         {renderContent()}
//       </div>
//     </div>
//   );
// };

// export default About;

import React, { useEffect } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { Routes, Route, useSearchParams } from 'react-router-dom';

import NamingCriteria from './About/NamingCriteria';
import InstanceType from './About/InstanceType';
import AboutCreate from './About/AboutCreate';
import AboutStart from './About/AboutStart';
import AboutStop from './About/AboutStop';
import AboutTerminate from './About/AboutTerminate';
import AboutDomain from './About/AboutDomain';
import AboutAbout from './About/AboutAbout';
import FAQ from './About/FAQ';
import './about.css';
import AboutBA from './About/AboutBA';
import AboutCostCenter from './About/AboutCostCenter';
import AboutAccount from './About/AboutAccount';
import AboutResize from './About/AboutResize';
import AboutScheduler from './About/AboutScheduler';
import AboutVolumeAttach from './About/AboutVolumeAttach';
import AboutVolumeDetach from './About/AboutVolumeDetach';
import AboutRestart from './About/AboutRestart';

const About = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const sectionFromUrl = location.pathname.split('/').pop() || 'AboutAbout';

  const [expandedCategory, setExpandedCategory] = React.useState('');

  const toggleCategory = (category) => {
    setExpandedCategory(expandedCategory === category ? '' : category);
  };

  const renderContent = () => {
    switch (sectionFromUrl) {
      case 'NamingCriteria':
        return <NamingCriteria />;
      case 'InstanceType':
        return <InstanceType />;
      case 'AboutBA':
        return <AboutBA />;
      case 'AboutAccount':
        return <AboutAccount />;
      case 'AboutCostCenter':
        return <AboutCostCenter />;
      case 'AboutCreate':
        return <AboutCreate />;
      case 'AboutStart':
        return <AboutStart />;
      case 'AboutStop':
        return <AboutStop />;
      case 'AboutTerminate':
        return <AboutTerminate />;
      case 'AboutDomain':
        return <AboutDomain />;
      case 'AboutVolumeAttach':
        return <AboutVolumeAttach />;
      case 'AboutVolumeDetach':
        return <AboutVolumeDetach />;
      case 'AboutScheduler':
        return <AboutScheduler />;
      case 'AboutRestart':
        return <AboutRestart />;
      case 'AboutResize':
        return <AboutResize />;
      case 'FAQ':
        return <FAQ />;
      default:
        return <AboutAbout />;
    }
  };

  const handleNavigation = (section) => {
    navigate(`/help/${section}`);
  };

  return (
    <div className="about-page">
      <div className="about-sidebar">
        <h3>Help</h3>
        <ul>
          <li>
            <div onClick={() => toggleCategory('Start')}>
              <span>{expandedCategory === 'Start' ? '▼' : '►'}</span> Service Action
            </div>
            {expandedCategory === 'Start' && (
              <ul>
                <li onClick={() => handleNavigation('AboutStart')}>Start Instance</li>
                <li onClick={() => handleNavigation('AboutStop')}>Stop Instance</li>
                <li onClick={() => handleNavigation('AboutResize')}>Resize</li>
                <li onClick={() => handleNavigation('AboutRestart')}>Restart</li>
                <li onClick={() => handleNavigation('AboutTerminate')}>Terminate Instance</li>
                <li onClick={() => handleNavigation('AboutDomain')}>Domain Checker</li>
                <li onClick={() => handleNavigation('AboutScheduler')}>Scheduler</li>
                <li onClick={() => handleNavigation('AboutVolumeAttach')}>Volume Attach</li>
                <li onClick={() => handleNavigation('AboutVolumeDetach')}>Volume Detach</li>
              </ul>
            )}
          </li>
          <li>
            <div onClick={() => toggleCategory('Provision Action')}>
              <span>{expandedCategory === 'Provision Action' ? '▼' : '►'}</span> Provision Action
            </div>
            {expandedCategory === 'Provision Action' && (
              <ul>
                <li onClick={() => handleNavigation('AboutCreate')}>Creating Instance</li>
              </ul>
            )}
          </li>
          <li onClick={() => handleNavigation('FAQ')}>FAQ</li>
          {/* <li onClick={() => handleNavigation('NamingCriteria')}>Naming Criteria</li> */}
          <li onClick={() => handleNavigation('InstanceType')}>Instance Type</li>
          <li onClick={() => handleNavigation('AboutCostCenter')}>Cost Centers</li>
          <li onClick={() => handleNavigation('AboutBA')}>Business Segments</li>
          <li onClick={() => handleNavigation('AboutAccount')}>AWS Accounts</li>
          <li onClick={() => handleNavigation('AboutAbout')}>About</li>
        </ul>
        <p className='version'>v1.11.24.0</p>
      </div>
      <div className="about-content">
        {renderContent()}
      </div>
    </div>
  );
};

export default About;
