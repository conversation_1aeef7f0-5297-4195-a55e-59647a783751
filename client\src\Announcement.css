.new-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.178); /* Dark semi-transparent background */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.new-announcement-container {
    background: rgba(0, 0, 0, 0.593); /* Semi-transparent white background */
    backdrop-filter: blur(25px); /* Apply blur effect */
    border-radius: 20px; /* Rounded corners */
    padding: 30px;
    width: 800px; /* Wider container */
    height: auto; /* Adjust height dynamically */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5); /* Strong shadow for depth */
    font-family: 'Roboto', sans-serif;
    color: #f5f5f5; /* Light text color */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.new-announcement-header h1 {
    font-size: 32px; /* Bigger font size */
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
    color: #ffffff; /* White color for header */
    text-transform: uppercase; /* Make text uppercase */
    letter-spacing: 1px; /* Add spacing between letters */
}

.new-announcement-content {
    flex: 1;
    overflow-y: auto; /* Scrollable content if it exceeds container height */
    padding-right: 10px;
}

.new-announcement-content section {
    margin-bottom: 15px;
}

.new-announcement-content h2 {
    font-size: 18px; /* Larger section titles */
    font-weight: bold;
    color: #ffffff; /* White text for section titles */
    margin-bottom: 8px;
    text-transform: capitalize; /* Capitalize section titles */
}

.new-announcement-content p {
    font-size: 15px; /* Slightly larger text */
    margin-bottom: 8px;
    line-height: 1.6; /* Improve readability */
    color: #e0e0e0; /* Light gray text for paragraphs */
}

.new-announcement-content a {
    display: inline-block;
    font-size: 15px;
    color: #00bcd4; /* Light blue text for links */
    text-decoration: none;
    margin-bottom: 5px;
    padding: 5px 10px;
    border-radius: 5px;
    background-color: rgba(0, 188, 212, 0.2); /* Semi-transparent blue background for links */
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.new-announcement-content a:hover {
    background-color: #00bcd4; /* Solid blue background on hover */
    color: #ffffff; /* White text on hover */
    transform: scale(1.05); /* Slight zoom effect */
}

.new-important-note {
    background: rgba(255, 69, 58, 0.2); /* Semi-transparent red background for important note */
    border-left: 10px solid #ff453a; /* Solid red border */
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2); /* Add shadow for depth */
}

.new-important-note h2 {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: bold;
    color: #ff453a; /* Bright red text for important note */
    margin-bottom: 10px;
}

.new-important-icon {
    margin-right: 10px;
    font-size: 20px; /* Larger icon size */
}

.new-close-btn {
    display: block;
    margin: 0 auto;
    background: linear-gradient(135deg, #00bcd4, #008ba3); /* Gradient button background */
    color: #ffffff; /* White text */
    border: none;
    padding: 10px 20px;
    border-radius: 30px; /* Rounded button */
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); /* Add shadow for button */
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.new-close-btn:hover {
    background-color: #006f7a; /* Darker blue on hover */
    transform: scale(1.1); /* Slight zoom effect */
}

/* Provision Links Styling */
.provision-links {
    display: flex;
    gap: 20px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.provision-category {
    flex: 1;
    min-width: 250px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.provision-category h3 {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 10px;
    text-align: center;
    border-bottom: 2px solid #00bcd4;
    padding-bottom: 5px;
}

.provision-category a {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    text-align: center;
}

.admin-note {
    background: rgba(0, 188, 212, 0.1);
    border: 1px solid rgba(0, 188, 212, 0.3);
    border-radius: 8px;
    padding: 12px;
    margin-top: 15px;
    font-size: 14px;
    color: #e0e0e0;
}

.admin-note strong {
    color: #00bcd4;
}