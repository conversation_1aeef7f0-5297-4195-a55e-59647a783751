const express = require('express');
const csvParser = require('csv-parser');
const router = express.Router();

module.exports = (s3, Readable) => {
  router.get('/', async (req, res) => {
    try {
      const params = {
        Bucket: 'server-provision-application',
        Key: 'Data/Compatible-Instances.csv'
      };

      s3.getObject(params, (err, data) => {
        if (err) {
          return res.status(500).send(err.message);
        }

        const stream = Readable.from(data.Body);
        const results = [];

        stream.pipe(csvParser())
          .on('data', (row) => {
            results.push({
              instanceType: row[Object.keys(row)[0]],
             location : row[Object.keys(row)[1]],
              vCpu: row[Object.keys(row)[2]],
              Mem: row[Object.keys(row)[3]],
              onDemandlinuxhr: row[Object.keys(row)[4]],
              onDemandlinuxmonth: row[Object.keys(row)[5]],
              onDemadwindoshr: row[Object.keys(row)[8]],
              onDemandwindosmonth: row[Object.keys(row)[9]],
              
            });
          })
          .on('end', () => {
           //console.log(req.query);console.log(results);
        //     const price =results.find(iteam=>{
        //        return (iteam.instanceType===req.query.instanceType&&
        //        iteam.location===req.query.region)
        // });
        //console.log(price)
            

            // 3. Get pairs of Cost Center and its Description
           
            // Return the original results plus the new variables
            res.json({
              
              results
            });
          })
          .on('error', (err) => {
            res.status(500).send(err.message);
          });
      });
    } catch (err) {
      res.status(500).send(err.message);
    }
  });

  return router;
};
