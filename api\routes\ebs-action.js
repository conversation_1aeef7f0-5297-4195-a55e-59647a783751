const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');
const nodemailer = require('nodemailer');

const axios = require('axios');


// Export the function to set up the router with necessary utilities
module.exports = (generateTicketNumber, storeEBSLogsInS3) => {
  let originalCredentials = AWS.config.credentials;
 
  // Function to assume an IAM role and return temporary credentials
  const assumeRole = async (accountId,firstname) => {
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    console.log(firstname);
    console.log(params);
    try {
      const data = await sts.assumeRole(params).promise();
      return data.Credentials;
    } catch (error) {
      AWS.config.update({ credentials: null });
        console.error('Error assuming role:', error.code, error.message);
        if (error.code === 'AccessDenied') {
          const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
            // Handle AccessDenied specifically, maybe log the accounts involved
       
     
    }else{
      console.error('Error assuming role:', error);
      throw error;
    }}
  };
 
  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };
 
  // Function to configure AWS services (SSM, S3, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      cloudFormation: new AWS.CloudFormation()
    };
  };
 


const handleEBSSSMAttachRequest = async (req, res, documentName) => {
  const {  region,email, accountId,accountname, instancename } =  req.body;
  console.log(req.body);
  const {
    instanceId,
    CostCenter = "6420",
    CostCenterDescription = "Infrastructure and NOC",
    SupportTier = "TIER3",
    SupportTierDescription = "ON-DEMAND (automatic first backup)",
    InstanceSource = "INEMB",
    ProvisioningEntity = "HID Engineer",
    BusinessArea = "OtherBA",
    BusinessContact = "Paramjeet Singh",
    BusinessContactEmail = "<EMAIL>",
    BusinessSegment = "9000",
    mapmigrated = "MigC5H22Y5OCL",
    BusinessSegmentDescription = "HID Global",
    TechnicalContact = "Steve Hayter",
    TechnicalContactEmail = "<EMAIL>",
    Environment = "SANDBOX",
    NetworkLocation = "INTERNAL",
    FunctionalArea = "IT",
    ProvisioningEngineer = "Prasana Srinivasan",
    BackupPlan = "BRONZE",
   
    
    volumesize,
    DeviceName,
    existing,
    volumeexistid,
    volumetype
  } = req.body;
  
  const params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId], // AWS SSM expects arrays for parameters
      RoleArn: [`arn:aws:iam::${accountId}:role/CrossAccountAccessRole`],
      CostCenter: [CostCenter],
      CostCenterDescription: [CostCenterDescription],
      SupportTier: [SupportTier],
      SupportTierDescription: [SupportTierDescription],
      InstanceSource: [InstanceSource],
      ProvisioningEntity: [ProvisioningEntity],
      BusinessArea: [BusinessArea],
      BusinessContact: [BusinessContact],
      BusinessContactEmail: [BusinessContactEmail],
      BusinessSegment: [BusinessSegment],
      mapmigrated: [mapmigrated],
      BusinessSegmentDescription: [BusinessSegmentDescription],
      TechnicalContact: [TechnicalContact],
      TechnicalContactEmail: [TechnicalContactEmail],
      Environment: [Environment],
      NetworkLocation: [NetworkLocation],
      FunctionalArea: [FunctionalArea],
      ProvisioningEngineer: [ProvisioningEngineer],
      BackupPlan: [BackupPlan],
     
      volumesize: [volumesize],
      DeviceName: [DeviceName],
      existing:[existing],
      volumeexistid:[volumeexistid],
      volumetype:[volumetype]
    },
  };

console.log(params);
  let firstname= req.body.firstname;
  let x=firstname.replace(/\s+/g,'');
  let result=x.substring(0,8);
  let randomValue = Math.floor(Math.random() * 9000) + 1000;
  res.setHeader('Content-Type', 'text/plain');
  res.setHeader('Transfer-Encoding', 'chunked');

  const sendUpdate = (message) => {
    res.write(`${message }\n\n`);
  };
// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
  if (!instanceId || !region || !accountId) {
    console.log("some req var are missing");
    return res.status(400).json({ error: 'Missing required fields' });
  }

  

  try {
    // AWS.config.update({ credentials: originalCredentials });
    if(accountId!=************){
    const credentials = await assumeRole(accountId,y);
    await initializeAWS(credentials, region);}else{AWS.config.update({
      region: region
    });}
    const { ssm } = configureAWS();

    const ticketNumber = generateTicketNumber();
    let executionID;
    const startTime = new Date();
    console.log(startTime);
    let data;
    try{
     data = await ssm.startAutomationExecution(params).promise();}catch(err){
      console.log(err);
     }console.log("after trigger");
    executionID = data.AutomationExecutionId;
    console.log(originalCredentials);
    AWS.config.update({ credentials: originalCredentials });
    const getAutomationssmExecutionStatus1 = async (ssm, executionId) => {
      try {
        const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
        console.log('Automation execution data:', data); // Log entire response
        const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
        return data;
      } catch (error) {
        console.error('Error fetching execution status:', error);
        throw error;
      }
    };
    let servicenownumber;
      // Poll status and fetch detailed result
      const pollAutomationssmExecutionStatus1 = async (ssm, executionId, interval = 1000) => {
        return new Promise((resolve, reject) => {
          const intervalId = setInterval(async () => {
            try {
              const data = await getAutomationssmExecutionStatus1(ssm, executionId);
              const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
              console.log('Current status:', status);
             
              sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
              if (['Success', 'Failed', 'TimedOut', 'Cancelled','Pending'].includes(status)) {
                  if ([ 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                      sendUpdate(`Error: ${data.AutomationExecution.CurrentStepName} step is ${status}`);
                  }
                clearInterval(intervalId);
                resolve(status);
              }
            } catch (error) {
                
              clearInterval(intervalId);
              reject(error);
              throw error;
            }
          }, interval);
        });
      };
    // Poll status and fetch detailed result
    const status = await pollAutomationssmExecutionStatus1(ssm, executionID);
    const executionDetails = await getAutomationssmExecutionStatus1(ssm, executionID);
    // const data = await getAutomationssmExecutionStatus1(ssm, executionId);
    const status2= executionDetails.AutomationExecution ? executionDetails.AutomationExecution.AutomationExecutionStatus : 'Unknown';
    console.log(executionDetails);
    console.log("status of execution attach");
    console.log(status2);
  // const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} = req.body;
    const endTime =new Date();
    let createdvolume=volumeexistid;
    try{
    if(createdvolume==''){
        const outputs = executionDetails?.AutomationExecution?.Outputs || {};
        console.log(outputs);
       console.log(outputs["DescribeStackResource.Volumestackid"]?.[0]); 
        createdvolume=outputs["DescribeStackResource.Volumestackid"]?.[0];
    }}catch(err){
console.log("unamble to get volume id");
throw err;
    }

    await storeEBSLogsInS3(ticketNumber, executionID,req.body.InstanceName,accountId, documentName, startTime,endTime, status,instanceId,BusinessContactEmail,email,createdvolume,region,accountname,servicenownumber);
    
    sendUpdate('Execution Successfull Sending Email');
    console.log('Execution Successfull Sending Email');
    try{
    const transporter = nodemailer.createTransport({
      host: 'relay.assaabloy.net',
      port: 25,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: '',
      },
    });

    const mail = await transporter.sendMail({
      from: '<EMAIL>',
      to: `${email}`,
      cc: `${BusinessContact} `,
      subject: `${documentName} Notification - ${instanceId}`,
      html: `<p>Hi,</p>  

<p>We are pleased to inform you that the EBS is Attach to EC2 Instance. Below are the details of the operation:</p>  

<p><strong>Status:</strong>${status}</p>  
<p><strong>Operation:</strong> ${documentName}</p>  
<p><strong>Instance Name:</strong> ${req.body.InstanceName}</p>  
<p><strong>Account ID:</strong> ${accountId}</p>  
<p><strong>Execution Date:</strong> ${startTime}</p>  
<p><strong>Instance ID:</strong> ${instanceId}</p> 
<p><strong>Volume ID:</strong> ${createdvolume}</p>  
<p><strong>Region:</strong> ${region}</p>  

<p>For any inquiries, please reach out to:</p>  
<p><strong>Business Contact:</strong>${BusinessContact}</p>  
<p><strong>Service Initialization Engineer:</strong> ${email}</p>  
<p><strong>AWS Account:</strong>${accountname}</p>  

<p>You can access the AWS Portal here:  
<a href="https://hidglobal.awsapps.com/" style="color: blue; text-decoration: none;">Click here</a>.</p>  
<p>Thanks,<br>GDIAS Team</p>

<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</em></p>

  `
 
,
});}catch(err){
    console.log(err);
    throw err;
}
console.log('Execution Successfull  Email sent');
    sendUpdate('Successfull Executed Storing Logs');
    await new Promise(resolve => setTimeout(resolve, 10000));
   
    
   
    AWS.config.update({ credentials: originalCredentials });
    //sendUpdate('Successfull');
    res.end('Successfull');
   

  } catch (err) {
    AWS.config.update({ credentials: originalCredentials });
    const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
  
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${email}`,
        cc: `${BusinessContact},<EMAIL> `,
        subject: `${documentName} Notification - ${instanceId}`,
        html: `<p>Hi,</p>  
  
  <p>We are pleased to inform you that the EBS is Attach to EC2 Instance is Failed. Below are the details of the operation:</p>  
  
  <p><strong>Error:</strong>${err}</p>  
  <p><strong>Operation:</strong> ${documentName}</p>  
  <p><strong>Instance Name:</strong> ${instancename}</p>  
  <p><strong>Account ID:</strong> ${accountId}</p>  

  <p><strong>Instance ID:</strong> ${instanceId}</p> 
   
  <p><strong>Region:</strong> ${region}</p>  
  
  <p>For any inquiries, please reach out to:</p>  
  <p><strong>Business Contact:</strong>${BusinessContact}</p>  
  <p><strong>Service Initialization Engineer:</strong> ${email}</p>  
  <p><strong>AWS Account:</strong>${accountname}</p>  
  
  <p>You can access the AWS Portal here:  
  <a href="https://hidglobal.awsapps.com/" style="color: blue; text-decoration: none;">Click here</a>.</p>  
  <p>Thanks,<br>GDIAS Team</p>
  
  <p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
  <a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</em></p>
  
    `
   
  ,
  });
    sendUpdate(`Error: ${err}`);
   
    res.end(`Error: ${err}`);
  }
};
const getAutomationExecutionDetails = async (ssm, executionId) => {
    try {
      const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      return data.AutomationExecution;
    } catch (error) {
      console.error('Error fetching execution details:', error);
      throw error;
    }
  };

  // Function to poll the status of an SSM Automation execution
  const pollAutomationExecutionStatus = async (req,ssm, executionId, interval = 5000) => {
    return new Promise((resolve, reject) => {
      const intervalId = setInterval(async () => {
        try {
          let status = await getAutomationExecutionDetails(ssm, executionId);
            status=status.AutomationExecutionStatus;
          console.log('Current status:', status);

          if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
            clearInterval(intervalId);
            resolve(status);
          }
        } catch (error) {
          const transporter = nodemailer.createTransport({
            host: 'relay.assaabloy.net',
            port: 25,
            secure: false,
            auth: {
              user: '<EMAIL>',
              pass: '',
            },
          });
     
          const mail = await transporter.sendMail({
            from: '<EMAIL>',
            to: `${req.body.email}`,
            cc: `${req.body.businesscontact},<EMAIL> `,
            subject: `${req.body.documentName} Notification - ${req.body.instanceId}`,
            html: `<p>Hi,</p>  
     
    <p>We are pleased to inform you that the EC2 instance operation has been Failed. Some Thing went Wrong Below are the details of the operation:</p>  
     `
       
      ,
    });
          clearInterval(intervalId);
          reject(error);
        }
      }, interval);
    });
  };

const handleEBSSSMDetachRequest = async (req, res, documentName) => {
    const {instanceId,  region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} = req.body;

    let firstname=req.body.firstname;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
    if (  !region || !accountId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    let params;
 
  params = {
    DocumentName: documentName,
    Parameters: {
     
      VolumeId: [req.body.volumeid.value], // assuming you have a variable `instanceType`
    }
  };
console.log(params);
console.log(req.body);
    try {
      // AWS.config.update({ credentials: originalCredentials });
      if(accountId!=************){
      const credentials = await assumeRole(accountId,y);
      await initializeAWS(credentials, region);}else{AWS.config.update({
        region: region
      });}
      const { ssm } = configureAWS();

      const ticketNumber = generateTicketNumber();
      let executionID;
      const startTime = new Date();
      const data = await ssm.startAutomationExecution(params).promise();
      executionID = data.AutomationExecutionId;
      console.log(executionID);
      AWS.config.update({ credentials: originalCredentials });
      
      
      // Poll status and fetch detailed result
      const executionDetails = await pollAutomationExecutionStatus(req,ssm, executionID);
      //const  = await getAutomationExecutionDetails(ssm, executionID);
      console.log(executionID);
      let status = await getAutomationExecutionDetails(ssm, executionID);
      status=status.AutomationExecutionStatus;
      console.log('Execution details:', executionDetails);
      //const startTime = new Date(executionDetails.ExecutionStartTime * 1000).toISOString();
//const endTime = new Date(executionDetails.ExecutionEndTime * 1000).toISOString();
     // const startTime = executionDetails.ExecutionStartTime;
      const endTime =new Date();
      await storeEBSLogsInS3(ticketNumber, executionID,req.body.InstanceName,accountId,documentName,  startTime,endTime, status,instanceId,businesscontact,email,req.body.volumeid.value,region,accountname,servicenownumber);
      AWS.config.update({ credentials: originalCredentials });
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${email}`,
        cc: `${businesscontact} `,
        subject: `${documentName} Notification - ${instanceId}`,
        html: `<p>Hi,</p>  
 
<p>We are pleased to inform you that the Volume is Detached from EC2 instance. Below are the details of the operation:</p>  
 
<p><strong>Status:</strong>${status}</p>  
<p><strong>Operation:</strong> ${documentName}</p>  
<p><strong>Instance Name:</strong> ${req.body.InstanceName}</p>  
<p><strong>Account ID:</strong> ${accountId}</p>  
<p><strong>Execution Date:</strong> ${startTime}</p>  
<p><strong>Instance ID:</strong> ${instanceId}</p> 
<p><strong>Volume ID:</strong> ${req.body.volumeid.value }</p>  
<p><strong>Region:</strong> ${region}</p>  
 
<p>For any inquiries, please reach out to:</p>  
<p><strong>Business Contact:</strong>${businesscontact}</p>  
<p><strong>Service Initialization Engineer:</strong> ${email}</p>  
<p><strong>AWS Account:</strong>${accountname}</p>  
 
<p>You can access the AWS Portal here:  
<a href="https://hidglobal.awsapps.com/" style="color: blue; text-decoration: none;">Click here</a>.</p>  
<p>Thanks,<br>GDIAS Team</p>
 
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</em></p>
 
    `
   
  ,
});
      
        AWS.config.update({ credentials: originalCredentials });
      res.status(200).json({
        message: 'SSM Automation Document triggered successfully!',
        ticketNumber,
        executionId: executionID,
        status,
        executionDetails // Include details in the response
      });
    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
      
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `${req.body.email}`,
        cc: `${req.body.businesscontact},<EMAIL> `,
        subject: `${req.body.documentName} Notification - ${req.body.instanceId}`,
        html: `<p>Hi,</p>  
 
<p>We are pleased to inform you that the EC2 instance operation has been Failed. Below are the details of the operation:</p>  
 
<p><strong>Error:</strong>${err}</p>  
<p><strong>Operation:</strong> ${req.body.documentName}</p>  
<p><strong>Instance Name:</strong> ${req.body.instancename}</p>  
<p><strong>Account ID:</strong> ${req.body.accountId}</p>  
<p><strong>Instance ID:</strong> ${req.body.instanceId}</p>  
<p><strong>Region:</strong> ${req.body.region}</p>  
 
<p>For any inquiries, please reach out to:</p>  
<p><strong>Business Contact:</strong>${req.body.businesscontact}</p>  
<p><strong>Service Initialization Engineer:</strong> ${req.body.email}</p>  
<p><strong>AWS Account:</strong>${req.body.accountname}</p>  
 
<p>You can access the AWS Portal here:  
<a href="https://hidglobal.awsapps.com/" style="color: blue; text-decoration: none;">Click here</a>.</p>  
<p>Thanks,<br>GDIAS Team</p>
 
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</em></p>
 
    `
   
  ,
});
      res.status(500).json({ error: err.message });
    }
  };
 
 
router.post('/ebs-attach', (req, res) => handleEBSSSMAttachRequest(req, res, 'EBS-Attach'));
router.post('/ebs-detach', (req, res) => handleEBSSSMDetachRequest(req, res, 'AWS-DetachEBSVolume'));
 
 
 
 
  return router;
};
 