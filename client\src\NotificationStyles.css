/* Modern Notification System Styles */

/* Overlay */
.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Main notification container */
.notification-container1 {
  position: relative;
  max-width: 500px;
  width: 90%;
  margin-bottom: 20px;
}

/* Alert Card (Loading state) */
.alert-card1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 0;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
  overflow: hidden;
  animation: slideInFromTop 0.5s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.alert-header1 {
  display: flex;
  align-items: center;
  padding: 25px 30px;
  position: relative;
}

.loading-icon1 {
  margin-right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-gif {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.alert-message1 {
  flex: 1;
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Status Card (Success/Error) */
.status-card1 {
  border-radius: 20px;
  padding: 30px;
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  animation: slideInFromTop 0.5s ease-out;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-card1::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: currentColor;
}

.status-card1.success {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: #ffffff;
}

.status-card1.error {
  background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
  color: #ffffff;
}

.status-icon1 {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.status-card1 p {
  flex: 1;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Close button */
.close-button1 {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #ffffff;
  font-size: 24px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close-button1:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* Additional notification */
.additional-notification {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 15px;
  padding: 20px 25px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: slideInFromBottom 0.5s ease-out 0.2s both;
}

.additional-notification p {
  margin: 0;
  color: #333;
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.additional-notification p::before {
  content: "📧";
  font-size: 20px;
}

/* Animations */
@keyframes slideInFromTop {
  from {
    transform: translateY(-50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInFromBottom {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.pop-animation1 {
  animation: popIn 0.6s ease-out;
}

.shake-animation1 {
  animation: shake 0.6s ease-out;
}

@keyframes popIn {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .notification-container1 {
    width: 95%;
  }
  
  .alert-header1,
  .status-card1 {
    padding: 20px;
  }
  
  .alert-message1,
  .status-card1 p {
    font-size: 16px;
  }
  
  .loading-gif {
    width: 40px;
    height: 40px;
  }
  
  .status-icon1 {
    width: 50px;
    height: 50px;
  }
  
  .additional-notification {
    padding: 15px 20px;
  }
  
  .additional-notification p {
    font-size: 14px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .additional-notification {
    background: rgba(30, 30, 30, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .additional-notification p {
    color: #e0e0e0;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .notification-overlay,
  .alert-card1,
  .status-card1,
  .additional-notification,
  .pop-animation1,
  .shake-animation1 {
    animation: none;
  }
  
  .close-button1:hover {
    transform: none;
  }
}

/* Focus styles for accessibility */
.close-button1:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

/* Enhanced Alert Content */
.alert-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Progress Bar for Loading */
.progress-bar {
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #ffffff, rgba(255, 255, 255, 0.8));
  border-radius: 2px;
  animation: progressAnimation 2s ease-in-out infinite;
}

@keyframes progressAnimation {
  0% {
    width: 0%;
    transform: translateX(-100%);
  }
  50% {
    width: 100%;
    transform: translateX(0%);
  }
  100% {
    width: 100%;
    transform: translateX(100%);
  }
}

/* Enhanced Status Content */
.status-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.status-message {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  line-height: 1.4;
}

.status-label {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  opacity: 0.8;
}

/* Enhanced Email Notification */
.additional-notification {
  display: flex;
  align-items: center;
  gap: 15px;
  text-align: left;
}

.email-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.email-content {
  flex: 1;
}

.email-title {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.email-subtitle {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* Pulse animation for loading icon */
.loading-icon1 {
  position: relative;
}

.loading-icon1::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Hover effects for better interactivity */
.status-card1:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.alert-card1:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;
}

/* Mobile responsiveness for enhanced features */
@media (max-width: 768px) {
  .additional-notification {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .email-title {
    font-size: 14px;
  }

  .email-subtitle {
    font-size: 13px;
  }

  .status-message {
    font-size: 16px;
  }

  .status-label {
    font-size: 11px;
  }
}
