import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './Stop.css';
import HIDlogo from './assets/hidLogo.png';
import Select from 'react-select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser} from '@fortawesome/free-solid-svg-icons';
import { MdInfo  } from "react-icons/md";
import Ulogo from './assets/Ulogo.png';
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import Loading from './assets/Rocket.gif';
import Navbar from './Navbar';
import './CustomTimePicker.css';
import Radio from '@mui/material/Radio';
import RadioGroup from '@mui/material/RadioGroup';
import FormControlLabel from '@mui/material/FormControlLabel';
import FormControl from '@mui/material/FormControl';
import FormLabel from '@mui/material/FormLabel';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';
import { LocalizationProvider, MobileDatePicker } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import TextField from '@mui/material/TextField';
import 'bootstrap/dist/css/bootstrap.min.css';
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { Link } from 'react-router-dom';
import { DesktopDateTimePicker } from '@mui/x-date-pickers/DesktopDateTimePicker';
import dayjs from 'dayjs';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';
function Scheduler() {
  const [data, setData] = useState([]);
  const [data1, setData1] = useState([]);
  const navigate = useNavigate();
  const [accountNames, setAccountNames] = useState([]);
  const [accountId, setAccountId] = useState([]);
  const [regions, setRegions] = useState([]);
  const [instances, setInstances] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedAccountID, setSelectedAccountID] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedInstance, setSelectedInstance] = useState('');
  const [selectedBusinessContact, setSelectedBusinessContact] = useState('');
  const [message, setMessage] = useState('');
  // const [user, setUser] = useState('<EMAIL>');
  const [isAcknowledged, setIsAcknowledged] = useState(false);
  const [instanceDetails, setInstanceDetails] = useState({});
  const [firstName, setfirstname] = useState('');
  const [alertMessage, setAlertMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  // const [cronJobType, setCronJobType] = useState('one-time'); 
  const [cronJobType, setCronJobType] = useState('recurring');
  const [startDate, setStartDate] = useState(''); // State for start date
  const [endDate, setEndDate] = useState(''); // State for end date
 
  const [endTime, setEndTime] = useState(''); // State for end time
  const [recurringDays, setRecurringDays] = useState([]); 
  const [time, setTime] = useState(''); // State for time selection
    const [month, setMonth] = useState(''); // State for month selection
    const [date, setDate] = useState(''); // State for date selection
    const [dayOfWeek, setDayOfWeek] = useState(''); // State for day of the week selection
    const [cronJob, setCronJob] = useState(''); // State for generated cron job
    const [accounts, setAccounts] = useState([]);
  const [scheduleName, setScheduleName] = useState(''); // State for schedule name
    const [startTime, setStartTime] = useState(null);
    const [adjustedTime, setAdjustedTime] = useState(null);
  const [stopTime, setStopTime] = useState(null); // State for stop time
  const [startTimeZone, setStartTimeZone] = useState(''); // State for start time zone
  const [stopTimeZone, setStopTimeZone] = useState(''); // State for stop time zone
  const [startCronJob, setStartCronJob] = useState(''); // State for start cron job
  const [stopCronJob, setStopCronJob] = useState(''); // State for stop cron job
  const [selectedInstances, setSelectedInstances] = useState([""]);
  const [instanceIds, setInstanceIds] = useState(['']);
  const [instancestring, setInstanceString] = useState(''); // State for instance ID
  const [actualStartCronJob, setActualStartCronJob] = useState('');
  const [actualStopCronJob, setActualStopCronJob] = useState(''); // State for actual stop cron job
  const [startDateTime, setStartDateTime] = useState(null); 
  const [stopDateTime, setStopDateTime] = useState(null);
  const [ recurringType, setRecurringType] = useState('');
  const [weeklyDays, setWeeklyDays] = useState([]); // State for selected days
const [startDay, setStartDay] = useState(null); // State for the first day in the range
const [endDay, setEndDay] = useState(null); // State for the last day in the range

const handleDaySelection = (event, newDays) => {
  if (newDays.length === 0) {
    // If no days are selected, reset everything
    setWeeklyDays([]);
    setStartDay(null);
    setEndDay(null);
    return;
  }

  if (newDays.length === 1) {
    // If only one day is selected, set it as both startDay and endDay
    setWeeklyDays(newDays);
    setStartDay(newDays[0]);
    setEndDay(newDays[0]);
    return;
  }

  // If two or more days are selected, calculate the range
  const sortedDays = [...newDays].sort((a, b) => a - b); // Sort the selected days
  const range = Array.from({ length: sortedDays[sortedDays.length - 1] - sortedDays[0] + 1 }, (_, i) => sortedDays[0] + i);

  setWeeklyDays(range); // Set the full range of days
  setStartDay(sortedDays[0]); // Set the first day in the range
  setEndDay(sortedDays[sortedDays.length - 1]); // Set the last day in the range
};
 
   const [user, setUser] = useState(
  {
    email: '<EMAIL>',
    displayName: 'test displayname',
    firstName: 'test firstname'
  });
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
        setUser(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);
  
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/user')
      .then(response => {
        const fetchedData = response.data;
         //console.log('Fetched user data:', fetchedData);
       // console.log(user);
        const userEntry = fetchedData.find(entry => entry.user === user.email);
         //console.log('User entry:', userEntry);
  
        if (userEntry) {
          const accountIds = userEntry.accounts.split(',').map(account => account.trim());
          // console.log('Parsed account IDs:', accountIds);
          setAccountId(accountIds);
        } else {
          setAccountId([]);
        }
      })
      .catch(error => {
        // console.error('Error fetching user accounts:', error);
      });
  }, [user]);
  
  useEffect(() => {
    if (accountId.length > 0) {
      axios.get('https://umanage.dev.hidglobal.com/api/s3')
        .then(response => {
          let fetchedData = response.data;
           //console.log('Fetched S3 data:', fetchedData);
  
          fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
           //console.log('Filtered S3 data:', fetchedData);
  
          const uniqueAccounts = [...new Set(fetchedData.map(item => item.AccountName))];
           //console.log('Unique account names:', uniqueAccounts);
  
          setData(fetchedData);
          setAccountNames(uniqueAccounts);
        })
        .catch(error => {
          // console.error('Error fetching S3 data:', error);
        });
    }
  }, [accountId]);
  // Ef

  useEffect(() => {
    if (selectedAccount) {
      // Filter regions based on selected account
      const filteredData = data.filter(item => item.AccountName === selectedAccount);
      const uniqueRegions = [...new Set(filteredData.map(item => item.Region))];
      setRegions(uniqueRegions);
    }
  }, [selectedAccount, data]);

  useEffect(() => {
    if (selectedRegion && selectedAccount) {
      // Filter instances based on selected region and account
      const filteredData = data.filter(item => item.Region === selectedRegion && item.AccountName === selectedAccount);
      
      // Update state to include both id and InstanceName
      setInstances(filteredData.map(item => ({
        value: item.InstanceId,
        label: `${item.InstanceId} - ${item.InstanceName}`, // Combine InstanceId and InstanceName for display
        name: item.InstanceName
      })));
    }
  }, [selectedRegion, selectedAccount, data]);
  useEffect(() => {
    if (startTime) {
      // Subtract 5 minutes from the start time
      const timeMinusFive = dayjs(startTime).subtract(5, 'minute');
      setAdjustedTime(timeMinusFive.format('hh:mm a')); // Format as 12-hour time with am/pm
    }
  }, [startTime]);
  console.log('Adjusted Time:', adjustedTime);
  const formatSelectedInstances = (instances) => {
    return `${instances.map((instance) => `"${instance}"`).join(',')}`;
  };
  const handleInstanceChange = (selectedOptions) => {
    // Update the selected instances state with the array of selected options
    setInstanceIds(selectedOptions); // Store the selected options in state
    setSelectedInstances( selectedOptions.map(option => option.value)); // If no options are selected, set an empty array
    setInstanceString(formatSelectedInstances(selectedOptions.map(option => option.value))); // Store the selected options in state
    // Extract the IDs of the selected instances
    const selectedInstanceIds = (selectedOptions.map(option => option.value));
  
    // Find the corresponding instances in the data
    const selectedInstancesData = data.filter((inst) =>
      selectedInstanceIds.includes(inst.InstanceId)
    );
  
    // Update the account ID based on the first selected instance (if any)
    if (selectedInstancesData.length > 0) {
      setSelectedAccountID(selectedInstancesData[0].accountId);
    }
  
    // Additional logic for business contact or scrolling can be added here
  };
  console.log('Selected Instances:', instancestring);
  // const handleInstanceChange = (selectedOption) => {
  //   setSelectedInstance(selectedOption.id);

  //   // Find the selected instance using the selected instance ID
  //   const instance = data.find((inst) => inst.InstanceId === selectedOption.id);
  //   if (instance) {
  //     setSelectedAccountID(instance.accountId);
  //   }
  // };
 // Output: ["instance1", "instance2"]
   // State for schedule type (One-Time or Recurring)

  
    // Generate cron expression whenever time, month, date, or dayOfWeek changes
    useEffect(() => { 
      
      
      const formatTime = (time) => (time ? `${time.minute()} ${time.hour()}` : '*');
      const formatDateTime = (time) => (time ? `${time.format('mm')} ${time.format('HH')}` : '*');
      const formatAdjustedTime = (time) => {
        if (!time) return '* *'; // Return wildcard if no time is provided
        const adjustedTime = dayjs(time).subtract(5, 'minute'); // Subtract 5 minutes
        return `${adjustedTime.minute()} ${adjustedTime.hour()}`; // Format as "minute hour"
      };
      const formatAdjustedDateime = (datetime) => {
        if (!datetime) return '* *'; // Return wildcard if no time is provided
        const adjustedTime = dayjs(datetime).subtract(5, 'minute'); // Subtract 5 minutes
        return `${adjustedTime.format('mm')} ${adjustedTime.format('HH')}`; // Format as "minute hour"
      };
      const formatDate = (datetime) => {
        if (!datetime) return '*'; // Return wildcard if no datetime is provided
        return datetime.format('DD'); // Extract only the date (day of the month) in "DD" format
      };
      
      const formatMonth = (datetime) => {
        if (!datetime) return '*'; // Return wildcard if no datetime is provided
        return datetime.format('MM'); // Extract only the month in "MM" format
      };// Month is 0-indexed in dayjs
  
      // const startCronTime = formatTime(startTime);
      const startCronTime =  cronJobType === 'recurring' ? formatAdjustedTime(startTime) : formatAdjustedDateime(startDateTime);
      const stopCronTime =  cronJobType === 'recurring' ? formatAdjustedTime(stopTime) : formatAdjustedDateime(stopDateTime);
      const ActualStopcronTime = cronJobType === 'recurring' ? formatTime(stopTime) : formatDateTime(stopDateTime);
      const ActualStartcronTime =  cronJobType === 'recurring' ? formatTime(startTime) : formatDateTime(startDateTime);
  
      const startCronDate = cronJobType === 'recurring' ? '?' : formatDate(startDateTime);
      const stopCronDate = cronJobType === 'recurring' ? '?' : formatDate(stopDateTime);
  
      const startCronMonth = cronJobType === 'recurring' ? '*' : (startDateTime ? formatMonth(startDateTime) : '*');
      const stopCronMonth = cronJobType === 'recurring' ? '*' : (stopDateTime ? formatMonth(stopDateTime) : '*');
      
  
      const startCronDayOfWeek =
      cronJobType === 'one-time'
        ? '?'
        : recurringType === 'weekly'
        ? startDay ? `${startDay}` : '*' // Use startDay for weekly
        :recurringDays.length > 0
        ? recurringDays.map((day) => day.value).join(',') // Use recurringDays for daily
        :  '*' ;
        const stopCronDayOfWeek =
        cronJobType === 'one-time'
        ? '?'
        : recurringType === 'weekly'
        ? endDay ?`${endDay}` :"*"
        :recurringDays.length > 0
        ? recurringDays.map((day) => day.value).join(',') // Use recurringDays for daily
        :  '*' ;// Use startDay and endDay for weekly
       
      
      
      // store the current year in a variable
      // Generate start and stop cron jobs
      setStartCronJob(`${startCronTime} ${startCronDate} ${startCronMonth} ${startCronDayOfWeek} 2025`);
      setStopCronJob(`${stopCronTime} ${stopCronDate} ${stopCronMonth} ${stopCronDayOfWeek} 2025`);
      setActualStartCronJob(`${ActualStartcronTime} ${startCronDate} ${startCronMonth} ${startCronDayOfWeek} 2025`);
      setActualStopCronJob(`${ActualStopcronTime} ${stopCronDate} ${stopCronMonth} ${stopCronDayOfWeek} 2025`);
    }, [startTime, stopTime, startDate, endDate, dayOfWeek, cronJobType, recurringDays, startDateTime, stopDateTime, recurringType, startDay, endDay]);
  
    const handleScheduleTypeChange = (e) => {
      setCronJobType(e.target.value);
      // Reset date and dayOfWeek when schedule type changes
      setStartDate(null);
      setEndDate(null);
      setDayOfWeek('');
    };
    
    console.log('Start Date:', startDate);  
    console.log('Start Time:', startTime);
    console.log('Start Cron Job:', startCronJob);
    console.log('Stop Cron Job:', stopCronJob);
  console.log('Actual Start Cron Job:', actualStartCronJob);
  console.log('Actual Stop Cron Job:', actualStopCronJob);


  const startListeningForUpdates = () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    const url = `https://umanage.dev.hidglobal.com/api/trigger-ssm/start?instanceId=${selectedInstance}&accountId=${selectedAccountID}&region=${selectedRegion}&businesscontact=${instanceDetails.BusinessContactEmail}&email=${user.email}&accountname=${selectedAccount}&firstname=${user.firstName}&instancename=${instanceDetails.InstanceName}&servicenownumber=0000`;
    //instanceId, region,  accountId, instanceName,group1,group2,group3,group4} = req.body;
    const eventSource = new EventSource(url);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setAlertMessage(data.message);
      if(data.message.substring(0,4)=="Succ")
        { 
          setAlertMessage("");
          
          setMessagestatus(true);
        }
      if(data.message.substring(0,5)=="Error"){
        setAlertMessage("");
        setMessage(data.message);
        setMessagestatus(false);
      }

    };

  }
  const handleTriggerSSM = async () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion!");
    setMessage(""); // Clear previous messages

    try {
        const response = await fetch('https://umanage.dev.hidglobal.com/api/customschedule/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              ProvisioningEngineer: user.email,
              InstanceID:instancestring,
                accountId: selectedAccountID,
                Region: selectedRegion,
                StackName:scheduleName,
                StartCronJob:startCronJob,
                StopCronJob:stopCronJob,
                actualStartCronJob:actualStartCronJob,
                actualStopCronJob:actualStopCronJob,
                Timezone:startTimeZone,
                cronJobType: cronJobType,

            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        // Stream the response in chunks
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let receivedText = "";

        while (true) {
            const { done, value } = await reader.read();
            if (done) break;
            
            receivedText = decoder.decode(value, { stream: true });
            setAlertMessage(receivedText); // Append new chunks to message
            if(receivedText.substring(0,4)=="Succ")
              { 
                setAlertMessage("");
                
                setMessagestatus(true);
              }
            if(receivedText.substring(0,5)=="Error"){
              setAlertMessage("");
              setMessage(data.message);
              setMessagestatus(false);
            }
      
        }

        setMessagestatus(true);

    } catch (error) {
        setMessage(`Error: ${error.message}`);
        setMessagestatus(false);
    }
};
  async function logout() {
    try {
      // Send a POST request to the backend logout endpoint
      const response = await fetch('/api/logout', {
        method: 'POST',
        credentials: 'include' // Include cookies with the request
      });
      // Check if the response is successful
      if (response.ok) {
        const result = await response.json();
        // // console.log(result.message); // Log the success message or handle it as needed
   
        // Optionally, redirect the user to a different page or update the UI
        window.location.href = '/login'; // Redirect to the login page or homepage
      } else {
        // // console.error('Logout failed');
      }
    } catch (error) {
      // // console.error('Error during logout:', error);
    }
  };
  const[messagestatus, setMessagestatus] = useState();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
  const [showPortfolioDropdown, setShowPortfolioDropdown] = useState(false);
  const [showTicketTooltip, setshowTicketTooltip] = useState(false); 
  const isAddScheduleDisabled = !(
    cronJobType === 'one-time'
      ? startDateTime && stopDateTime && startTimeZone // Check for one-time schedule
      : recurringType === 'daily'
      ? startTime && stopTime && startTimeZone // Check for daily recurring schedule
      : recurringType === 'weekly'
      ? startTime && stopTime && startDay && endDay && startTimeZone // Check for weekly recurring schedule
      : false // Default to false if no valid type is selected
  );
  // const splitDate = (date) => {
  //   if (!date) return { day: '', month: '', year: '' };
  //   return {
  //     day: date.date(), // Day of the month
  //     month: date.month() + 1, // Month (0-indexed, so add 1)
  //     year: date.year(), // Year
  //   };
  // };

  // const startDateParts = splitDate(startDate);
  // const endDateParts = splitDate(endDate);

  // const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
  //   null,
  //   null,
  // ]);
  // const [teststartDate, setTestStartDate] = useState<Date | undefined>(undefined);
  // const [testendDate, setTestEndDate] = useState<Date | undefined>(undefined);
  // useEffect(() => {
  //   if (dateRange[0]) {
  //     setTestStartDate(dateRange[0]);
  //   } else {
  //     setTestStartDate(undefined);
  //   }

  //   if (dateRange[1]) {
  //     setTestEndDate(dateRange[1]);
  //   } else {
  //     setTestEndDate(undefined);
  //   }
  // }, [dateRange]);

  const dummyOptions = [
    { value: 'dummy1', label: 'Dummy Instance 1' },
    { value: 'dummy2', label: 'Dummy Instance 2' },
    { value: 'dummy3', label: 'Dummy Instance 3' },
    { value: 'dummy4', label: 'Dummy Instance 4' },
    { value: 'dummy5', label: 'Dummy Instance 5' },
  ];
  const toggleButtonStyles = {
     // Default white background
    color: '#000', // Black text
    fontWeight: 'bold', // Bold text
    border: '1px solid  #939393', // Black border between buttons
    '&.Mui-selected': {
      backgroundColor: '#00549B', // Blue background when selected
      color: '#fff', // White text when selected
      fontWeight: 'bold', // Bold text when selected
    },
    '&.Mui-selected:hover': {
      backgroundColor: '#003F73', // Darker blue on hover when selected
    },
    '&:hover': {
      backgroundColor: '#f0f0f0', // Light gray background on hover
    },
  };
  return (
    <div className="Stop-App">
       {/* <Navbar /> */}
  <div className="full-page-content">
  {(message||alertMessage) &&<div  className="notification-container">
        {alertMessage && !message && (
      <div className="alert-card">
        <div className="alert-header">
          <div className="loading-icon">
            <img src={Loading} alt="Loading" className="loading-gif" />
          </div>
          <p className="alert-message">{alertMessage}</p>
          <button className="close-button" onClick={() => setAlertMessage(null)}><IoIosClose /></button>
        </div>
      </div>
    )}

      {/* Status Message Card */}
      {message && (
        <div className={`status-card ${messagestatus ? 'success' : 'error'}`}>
          <div className={`status-icon ${messagestatus ? 'pop-animation' : 'shake-animation'}`}>
            {messagestatus ? <FaCheckCircle size={24} /> : <FaTimesCircle size={24} />}
          </div>
         <p>{message}</p>
          <button className="close-button"onClick={() => {  setMessage(null); setAlertMessage(null);}}><IoIosClose /></button>
          
       
        </div>
      )}
      </div>}

  
    
    

<div className="instance-details-box">
  {/* Description below the heading */}
  <div className="form-section">
  {/* Instance Name Input */}
  <div className="row">
  {/* <div className="col-md-3 form-group">
    
        <h2 className="dropdown-heading">Schedule Name</h2>
        <p className="dropdown-description">Provide a name for the schedule.</p>
        <input
          type="text"
          className="form-control"
          placeholder="Enter Schedule Name"
          value={scheduleName}
          onChange={(e) => setScheduleName(e.target.value)}
        />
      </div> */}
      
      

      <div className="col-md-5 form-group">
   
      <h2 className="dropdown-heading">Select Account </h2>
     
      <select className="form-control" value={selectedAccount} onChange={(e) => setSelectedAccount(e.target.value)}>
        <option value="">Select Account</option>
        {accountNames.map(account => (
          <option key={account} value={account}>{account}</option>
        ))}
      </select>
    </div>

    <div className="col-md-3 form-group">
      <h2 className="dropdown-heading">Select Region </h2>
    
      <select className="form-control" value={selectedRegion} onChange={(e) => setSelectedRegion(e.target.value)} >
        <option value="">Select Region</option>
        {regions.map(region => (
          <option key={region} value={region}>{region}</option>
        ))}
      </select>
    </div>
    </div>


    <div className="row">
    <div className="col-md-12 form-group">
      <h2 className="dropdown-heading">Instance Selection</h2>
      <p className="dropdown-description">Pick one or more instance from the list.</p>
      <Select
                  isMulti
                  className="re-select"
                  value={instanceIds}
                  onChange={handleInstanceChange}
                  options={instances}
                 
                />
      </div>
      </div>
      </div>
      </div>

      
      
      <div className="schedule-type-box">
      <div className="row">
      <div className="col-md-3">
      <FormControl>
      
      <h2 className="dropdown-heading">Schedule Type</h2>
      <p className="dropdown-description">Select the preferred schedule type.</p>
  <RadioGroup
    row
    aria-labelledby="schedule-type-label"
    name="schedule-type-group"
    value={cronJobType} // Bind the selected value to the state
    onChange={(e) => setCronJobType(e.target.value)} // Handle changes
  >
    <FormControlLabel value="one-time" control={<Radio />} label="One-Time" />
    <FormControlLabel value="recurring" control={<Radio />} label="Recurring" />
  </RadioGroup>
  
</FormControl>
</div>
<div className="col-md-6">
<h2 className="dropdown-heading">Time Zone</h2>
<p className="dropdown-description">Select the preferred time zone.</p>
          <select
            className="form-control"
            value={startTimeZone}
            onChange={(e) => setStartTimeZone(e.target.value)}
          >
            <option value="">Select Time Zone</option>
            <option value="America/Los_Angeles">PST Pacific Standard Time</option>
            <option value="America/Denver">MST Mountain Standard Time</option>
            <option value="America/Chicago">CST Central Standard Time</option>
            <option value="America/New_York">EST Eastern Standard Time</option>
            <option value="Europe/Lisbon">WET Western European Time</option>
            <option value="Europe/Paris">CET Central European Time</option>
            <option value="Europe/Bucharest">EET Eastern European Time</option>
            <option value="Asia/Kolkata">IST India Standard Time</option>
          </select>
        </div>
  </div>
</div>

    {/* {cronJobType === 'one-time' && (
      <div>
        <div className="dropdown-container">
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Instance Selection</h2>
      <p className="dropdown-description">Pick an instance from the list.</p>
        
        <LocalizationProvider dateAdapter={AdapterDayjs}>
        <TimePicker
          value={startTime}
          onChange={(newValue) => setStartTime(newValue)}
          
        />
        </LocalizationProvider>
        </div>
        <div className="dropdown-section">
        <h2 className="dropdown-heading">Instance Selection</h2>
        <p className="dropdown-description">Pick an instance from the list.</p>
       
        <select
          className="form-control"
          value={startTimeZone} 
          onChange={(e) => setStartTimeZone(e.target.value)} // Update the state with the selected time zone
        >
          <option value="">Select Time Zone</option>
          <option value="America/Los_Angeles">PST Pacific Standard Time</option>
          <option value="America/Denver">MST Mountain Standard Time</option>
          <option value="America/Chicago">CST Central Standard Time</option>
          <option value="America/New_York">EST Eastern Standard Time</option>
          <option value="Europe/Lisbon">WET Western European Time</option>
          <option value="Europe/Paris">CET Central European Time</option>
          <option value="Europe/Bucharest">EET Eastern European Time</option>
          <option value="Asia/Kolkata">IST India Standard Time</option>
        </select>
        </div>
      </div>
      <div className="dropdown-container">
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Instance Selection</h2>
      <p className="dropdown-description">Pick an instance from the list.</p>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
        
        <div className="time-picker-container">
          <TimePicker
            value={stopTime}
            onChange={(newValue) => setStopTime(newValue)}
            renderInput={(params) => <TextField {...params} fullWidth />}
          />
        </div>
        </LocalizationProvider>
        </div>
        <div className="dropdown-section">
        <h2 className="dropdown-heading">Instance Selection</h2>
        <p className="dropdown-description">Pick an instance from the list.</p>
        <select
          className="form-control"
          value={startTimeZone} // Bind the selected value to the state
          onChange={(e) => setStartTimeZone(e.target.value)} // Update the state with the selected time zone
        >
          <option value="">Select Time Zone</option>
          <option value="America/Los_Angeles">PST Pacific Standard Time</option>
          <option value="America/Denver">MST Mountain Standard Time</option>
          <option value="America/Chicago">CST Central Standard Time</option>
          <option value="America/New_York">EST Eastern Standard Time</option>
          <option value="Europe/Lisbon">WET Western European Time</option>
          <option value="Europe/Paris">CET Central European Time</option>
          <option value="Europe/Bucharest">EET Eastern European Time</option>
          <option value="Asia/Kolkata">IST India Standard Time</option>
        </select>
      </div>
      </div>

      <LocalizationProvider dateAdapter={AdapterDayjs}>
      <div className="dropdown-container">
       <div className="dropdown-section">
        <h2 className="dropdown-heading">Instance Selection</h2>
        <p className="dropdown-description">Pick an instance from the list.</p>
      <MobileDatePicker
        
        onChange={(newValue) => setStartDate(newValue)}
        renderInput={(params) => (
        <TextField
          {...params}
          className="form-control"
          variant="outlined"
          fullWidth
          placeholder="Select Start Date"
        />
        )}
      />
      </div>
      <div className="dropdown-section">
        <h2 className="dropdown-heading">Instance Selection</h2>  
      <label>End Date</label>
      <MobileDatePicker
        
        onChange={(newValue) => setEndDate(newValue)}
        renderInput={(params) => <TextField {...params} fullWidth />}
      />
      </div>
      </div>
      </LocalizationProvider>

    </div>
       
     
    )} */}
{cronJobType === 'one-time' && (
  <div className="recurring-schedule-box">
  <div className="row Start-box">
    {/* Start Schedule Section */}
    <div className="col-md-4 form-group">
    <h2 className="dropdown-heading">Start Time</h2>
    <p className="dropdown-description">Select the preferred date & time to Start the Server.</p>
      <div className="row">
        <div className="col-md-6">
        <LocalizationProvider dateAdapter={AdapterDayjs}>
       
        <DesktopDateTimePicker
  sx={{
    width: 400,
    backgroundColor: '#fff',
  }}
  minTime={dayjs().hour(0).minute(5)}
                maxTime={dayjs().hour(23).minute(59)} 
  placeholder="Select Start Time"
  onChange={(newValue) => setStartDateTime(newValue)}
  value={startDateTime}
  
/>
        </LocalizationProvider>
        </div>
        
      </div>
    </div>

    {/* Stop Schedule Section */}
    <div className="col-md-6 form-group">
    <h2 className="dropdown-heading">Stop Time</h2>
    <p className="dropdown-description">Select the preferred date & time to Stop the Server.</p>
      <div className="row">
        <div className="col-md-6">
          <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DesktopDateTimePicker 
        sx={{ width: 400 ,
          backgroundColor: '#fff',}}
          minTime={dayjs().hour(0).minute(5)}
                maxTime={dayjs().hour(12).minute(59)} 
                skipDisabled={true}
        onChange={(newValue) => setStopDateTime(newValue)}
        value={stopDateTime}
      />
          </LocalizationProvider>
        </div>

      </div>
    </div>
  </div>

  {/* Start and End Date Section */}
  {/* <div className="row mt-4">
    <div className="col-md-6 form-group">
       <h2 className="dropdown-heading">Start Date</h2>
    <p className="dropdown-description">Select the prefferred Start Date.</p>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
      <MobileDatePicker
        
        onChange={(newValue) => setStartDate(newValue)}
        renderInput={(params) => (
        <TextField
          {...params}
          className="form-control"
          variant="outlined"
          fullWidth
          placeholder="Select Start Date"
        />
        )}
      />
      </LocalizationProvider>
    </div>
    <div className="col-md-6 form-group">
    <h2 className="dropdown-heading">Stop Date</h2>
    <p className="dropdown-description">Select the prefferred Stop Date.</p>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
      <MobileDatePicker
        
        onChange={(newValue) => setEndDate(newValue)}
        renderInput={(params) => <TextField {...params} fullWidth />}
      />
      </LocalizationProvider>
    </div>
  </div> */}
</div>
)}
{cronJobType === 'recurring' && (
  <div className="recurring-schedule-box">
    <div className="row">
      {/* Start Schedule Section */}
      <div className="col-md-4 form-group">
      <h2 className="dropdown-heading">Start Time</h2>
      <p className="dropdown-description">Select the preferred time to Start Server.</p>
        <div className="row">
          <div className="col-md-6">
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                sx={{ width: 400 ,
                  backgroundColor: '#fff',}}
                minTime={dayjs().hour(0).minute(5)}
                maxTime={dayjs().hour(23).minute(59)} 
                skipDisabled={true}
                value={startTime}
                onChange={(newValue) => setStartTime(newValue)}
              />
            </LocalizationProvider>
          </div>
         
        </div>
      </div>

      {/* Stop Schedule Section */}
      <div className="col-md-6 form-group">
      <h2 className="dropdown-heading">Stop Time</h2>
      <p className="dropdown-description">Select the preferred time to Stop Server.</p>
        <div className="row">
          <div className="col-md-6">
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <TimePicker
                sx={{ width: 400 ,
                  backgroundColor: '#fff',}}
                minTime={dayjs().hour(0).minute(5)}
                maxTime={dayjs().hour(23).minute(59)} 
                skipDisabled={true}
                value={stopTime}
                onChange={(newValue) => setStopTime(newValue)}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    className="form-control"
                    variant="outlined"
                    placeholder="Select Stop Time"
                  />
                )}
              />
            </LocalizationProvider>
          </div>
          
        </div>
      </div>
    </div>

    <FormControl>
    <div className="row mt-4">
  <h2 className="dropdown-heading">Recurring Type</h2>
  <p className="dropdown-description">Select the preferred recurring schedule type.</p>
  <RadioGroup
    row
    aria-labelledby="recurring-schedule-type-label"
    name="recurring-schedule-type-group"
    value={recurringType} // Bind the selected value to the state
    onChange={(e) => {
      setRecurringType(e.target.value); // Update the recurring type
      setRecurringDays([]); // Clear recurring days
      setWeeklyDays([]); // Clear weekly days
      setStartDay(null); // Reset start day
      setEndDay(null); // Reset end day
    }} // Handle changes
  >
    <FormControlLabel value="daily" control={<Radio />} label="Daily" />
    <FormControlLabel value="weekly" control={<Radio />} label="Weekly" />
  </RadioGroup>
  </div>
  
</FormControl>
{recurringType === 'daily' && (
   <div className="row mt-4">
   <div className="col-md-12">
     <h2 className="dropdown-heading">Days of the Week</h2>
     <p className="dropdown-description">Select the days for the recurring schedule.</p>
     <ToggleButtonGroup
  value={recurringDays.map((day) => day.value)} // Bind the selected values to the state
  onChange={(event, newDays) => {
    const updatedDays = newDays.map((day) => ({
      value: day,
      label: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'][day - 1],
    }));
    setRecurringDays(updatedDays); // Update the state with the selected days
  }}
  aria-label="Recurring Days"
> 
<ToggleButton value={1} aria-label="Sunday" sx={toggleButtonStyles}>
    Sun
  </ToggleButton>
  <ToggleButton value={2} aria-label="Monday" sx={toggleButtonStyles}>
    Mon
  </ToggleButton>
  <ToggleButton value={3} aria-label="Tuesday" sx={toggleButtonStyles}>
    Tue
  </ToggleButton>
  <ToggleButton value={4} aria-label="Wednesday" sx={toggleButtonStyles}>
    Wed
  </ToggleButton>
  <ToggleButton value={5} aria-label="Thursday" sx={toggleButtonStyles}>
    Thu
  </ToggleButton>
  <ToggleButton value={6} aria-label="Friday" sx={toggleButtonStyles}>
    Fri
  </ToggleButton>
  <ToggleButton value={7} aria-label="Saturday" sx={toggleButtonStyles}>
    Sat
  </ToggleButton>
 
</ToggleButtonGroup>
   </div>
 </div>
  )}

  {recurringType === 'weekly' && (
    <div className="row mt-4">
    <div className="col-md-12">
      <h2 className="dropdown-heading">Weekly Schedule</h2>
      <p className="dropdown-description">Select a range of days for the weekly schedule.</p>
      <ToggleButtonGroup
  value={weeklyDays} // Bind the selected values to the state
  onChange={handleDaySelection} // Handle changes
  aria-label="Weekly Days"
>
<ToggleButton value={1} aria-label="Sunday" sx={toggleButtonStyles}>
    Sun
  </ToggleButton>
  <ToggleButton value={2} aria-label="Monday" sx={toggleButtonStyles}>
    Mon
  </ToggleButton>
  <ToggleButton value={3} aria-label="Tuesday" sx={toggleButtonStyles}>
    Tue
  </ToggleButton>
  <ToggleButton value={4} aria-label="Wednesday" sx={toggleButtonStyles}>
    Wed
  </ToggleButton>
  <ToggleButton value={5} aria-label="Thursday" sx={toggleButtonStyles}>
    Thu
  </ToggleButton>
  <ToggleButton value={6} aria-label="Friday" sx={toggleButtonStyles}>
    Fri
  </ToggleButton>
  <ToggleButton value={7} aria-label="Saturday" sx={toggleButtonStyles}>
    Sat
  </ToggleButton>
</ToggleButtonGroup>
      <div className='col-md-12 mt-3'>
      <button
          className="btn btn-secondary mt-2"
          onClick={() => {
            setWeeklyDays([]); // Clear selected days
            setStartDay(null); // Reset start day
            setEndDay(null); // Reset end day
          }}
        >
          Clear
        </button>
      </div>
      {/* <div className="mt-3">
        <p>Start Day: {startDay ? `Day ${startDay}` : 'None'}</p>
        <p>End Day: {endDay ? `Day ${endDay}` : 'None'}</p>
        <p>Selected Days: {weeklyDays.length > 0 ? weeklyDays.join(', ') : 'None'}</p>
      </div> */}
    </div>
  </div>
  )}
    
  </div>
)}
         
        
         

      <button 
      className="TriggerSSM" 
   // disabled={!isAcknowledged || !selectedInstance || isProcessing} 
    onClick={handleTriggerSSM} disabled={isAddScheduleDisabled}
  >
    Add Schedule 
  </button>
  {/* <DatePicker
      selectsRange={true} // Date range selecting enabled
      startDate={startDate}
      endDate={endDate}
      onChange={(update) => {
        setDateRange(update);
      }}
      calendarStartDay={1} // Starts from Monday
    /> */}
  
</div>

    </div>
  );
}

export default Scheduler;
