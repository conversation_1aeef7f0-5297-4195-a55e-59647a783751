import React from 'react';
import './AboutTerminate.css'; // Ensure to create this CSS file for styling
import Terminate from '../assets/about/Terminate.png';
const AboutTerminate = () => (
  <div className="about-terminate-container">
    <h2 className="about-terminate-title">Terminate Action</h2>
    <p className="about-terminate-description">
      The Terminate action is used to permanently remove an instance from your AWS account. This operation is irreversible and should only be performed when the instance is no longer needed.
    </p>
    <h3 className="about-terminate-subtitle">Steps to Terminate an Instance</h3>
    <ol className="about-terminate-steps">
      <li className="about-terminate-step">
        <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
        <div className="about-terminate-image-container">
          <img src={Terminate} alt="Select Account Sample" className="about-terminate-image" />
        </div>
      </li>
      <li className="about-terminate-step">
        <strong>Select the Region:</strong> Pick the region assigned to that account, as instance availability is region-specific.
      </li>
      <li className="about-terminate-step">
        <strong>Select the Instance:</strong> Locate the instance by either its ID or Name for easy identification.
      </li>
      <li className="about-terminate-step">
        <strong>Enter the ServiceNow Ticket ID:</strong> Provide a valid ticket ID (RITM, INC, or REQ) to proceed with terminating the instance.
      </li>
      <li className="about-terminate-step">
        <strong>Review Details:</strong> Confirm all instance details, such as configuration and status.
      </li>
      <li className="about-terminate-step">
        <strong>Acknowledge:</strong> Check the acknowledgment box to confirm understanding of the actions.
      </li>
      <li className="about-terminate-step">
        <strong>Click the "Terminate" Button:</strong> Execute the terminate action. The instance will be terminated, but backups will remain available for 7 days before they are deleted.
      </li>
    </ol>
    <p className="about-terminate-note">
    The instance will terminate on the same day, but we will ensure that backups for this instance remain available for the next 7 days. After this period, the backups will be deleted, and if restored, the instance will have a different IP address.
    </p>
  </div>
);

export default AboutTerminate;
