@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');

.Stop-App {
  font-family: "Lato", sans-serif;
  background-color: #ffffff;
  color: #333;
  min-height: 100vh;
 
}

/* Navbar styling remains unchanged */
.stop-navbar {
  background-color: #ffffff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 10px 20px;
}

.stop-navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stop-navbar-logo {
  height: 50px;
}

.stop-navbar-links {
  display: flex;
  gap: 15px;
}

.stop-navbar-link {
  color: #02569b;
  text-decoration: none;
  font-weight: 900;
  transition: color 0.3s;
}

.stop-navbar-link:hover {
  color: #fff;
  background-color: #02569b;
}

.stop-logout-btn {
  background-color: #ffffff;
  color: #02569b;
  border: none;
  font-size: medium;
  font-weight: 800;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.stop-logout-btn:hover {
  background-color: #00549B;
  color: #fff;
}

.full-page-content {
  padding: 20px;
  max-width: 100%;
  margin: 0 auto;
}

.main-title {
  text-align: center;
  color: #02569b;
  margin-bottom: 30px;
  font-size: 28px;
  font-weight: bold;
}

/* Container for dropdowns */
.dropdown-container {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.dropdown-section {
  flex: 1;
}

.dropdown-heading {
  font-size: 18px;
  font-weight: 700;
  color: #02569b;
  margin-bottom: 5px;
}

.dropdown-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

select {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background-color: #f8f9fa;
  font-size: 14px;
}

/* Instance details styling */
.instance-details {
  margin-top: 20px;
  padding: 15px;
  background-color: #f1f3f6;
  border-left: 4px solid #007bff;
  border-radius: 5px;
}

.instance-details p {
  margin: 5px 0;
  color: #555;
}

/* Trigger SSM button */
.TriggerSSM {
  display: block;
  width: calc(100% - 20px);
  padding: 10px;
  margin: 20px 10px;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.TriggerSSM:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.TriggerSSM:hover:enabled {
  background-color: #0056b3;
}

/* Checkbox for acknowledgment */
.checkbox-container {
  display: flex;
  align-items: center;
  margin: 15px 10px;
}

.checkbox-label {
  margin-left: 10px;
  font-size: 14px;
  color: #333;
}

/* Success and error messages */
.alert-message {
  margin-top: 15px;
  background-color: #ffcc00;
  color: #333;
  padding: 10px;
  border-radius: 5px;
  font-weight: bold;
  text-align: center;
}

.message {
  margin-top: 10px;
  padding: 10px;
  background-color: #007bff;
  color: white;
  border-radius: 5px;
  text-align: center;
}

