/* Scheduler.css */
.scheduler-container {
  display: flex;
  flex-direction: row;
  height: 90vh;
}
.scheduler-container h1 {
  font-size: 3.5em;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}
.sidebar {
  width: 250px; /* Increased width for better spacing */
  background-color: #f4f4f4;
  padding: 20px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  border-radius: 8px; /* Rounded corners */
}

.sidebar h2 {
  font-size: 1.5em;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.sidebar button {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #3419a8;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.sidebar button:hover {
  background-color: #0056b3;
}

.main-content {
  flex: 1;
  padding: 20px;
  background-color: #fff;
}
.toggle-container {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  position: relative;
}

.toggle-container label {
  margin: 0 10px;
  font-size: 1.2em;
  color: #333;
  cursor: pointer;
  padding-bottom: 5px;
  transition: color 0.3s ease;
}

.toggle-container input[type="radio"] {
  display: none;
}

.toggle-container .underline {
  position: absolute;
  bottom: 0;
  left: 40.5%;
  height: 3px;
  width: 90px;
  background-color: #007bff;
  transition: left 0.3s ease;
}
.toggle-container .underline-weekly {
  position: absolute;
  bottom: 0;
  left: 48.3%;
  height: 3px;
  width: 160px;
  background-color: #007bff;
  transition: left 0.3s ease;
}

.toggle-container input[type="radio"]:checked + label {
  color: #007bff;
}
.schedule-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* 4 items per row */
  gap: 20px;
}

.schedule-box {
  background-color: #3419a8;
  color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.schedule-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
.schedule-box button {
  background-color: #ffffff;
  color: #1f40cf;
  width: 80%;
  border: none;
  font-weight: bold;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.schedule-box h3 {
  margin: 0;
  font-size: 1.2em;
  color: #ffffff;
}

.schedule-form {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.9); /* White background with slight transparency */
  display: flex;
  justify-content: center;
  align-items: center;
}

.schedule-form-content {
  background-color: #fff; /* White background */
  padding: 30px;
  border-radius: 12px;
  width: 700px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

.schedule-form-content h2 {
  margin-top: 0;
  font-size: 2em;
  font-weight: bold;
  color: #00008B; /* Dark blue color for the heading */
}

.schedule-form-content form {
  display: flex;
  flex-direction: column;
}

.schedule-form-content label {
  margin-bottom: 5px;
  color: #00008B; /* Dark blue color for labels */
}



.schedule-form-content button {
  background-color: #00008B; /* Dark blue background for buttons */
  color: #fff; /* White text color */
  border: none;
  margin-top: 20px;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}
.schedule-form-content button:disabled {
  background-color: #ccc; /* Gray background */
  cursor: not-allowed; /* Change cursor to not-allowed */
  opacity: 0.6; /* Reduce opacity */
}
.schedule-form-content button:disabled:hover {
  background-color: #ccc; /* Keep the gray background on hover */
  cursor: not-allowed; /* Keep the not-allowed cursor on hover */
  opacity: 0.6; /* Keep the reduced opacity on hover */
}
.schedule-form-content button:hover {
  background-color: #000066; /* Darker blue on hover */
}

.schedule-form-content .close-btn {
  background-color: #dc3545; /* Red background for close button */
}

.schedule-form-content .close-btn:hover {
  background-color: #c82333; /* Darker red on hover */
}

.sch-close-btn-top {
  position: absolute; /* Position the button absolutely within the form content */
  top: 10px; /* Position it 10px from the top */
  right: 10px; /* Position it 10px from the right */
  background-color: #fff; /* Transparent background */
  border: none; /* No border */
  color: #000; /* Black color */
  font-size: 20px; /* Larger font size */
  font-weight: bold; /* Bold text */
  cursor: pointer; /* Pointer cursor on hover */
}

.sch-close-btn-top:hover {
  color: #dc3545; /* Red color on hover */
}
.sch-message {
  background-color: #45ae5e; /* Light green background */
  color: #ffffff; /* Dark green text */
  padding: 10px;
  font-weight: bold;
  position: relative;
  border-radius: 4px;
  margin-top: 10px;
}

.sch-alert {
  background-color: rgb(215, 35, 50); /* Light red background */
  color: #ffffff; /* Dark red text */
  padding: 10px;
  font-weight: bold;
  border-radius: 4px;
  margin-top: 10px;
  position: relative;
}

.sch-close-btn {
  background: none;
  border: none;
  color: #fff;
  font-size: 20px;
  
  top: 5px;
  right:10px;
  position: absolute;
  cursor: pointer;
}
.message-banner {
  margin-top: 20px;
  padding: 15px;
  background-color: #e8f4fc; /* Light blue background for better visibility */
  border: 2px solid #007bff; /* Blue border for emphasis */
  border-radius: 8px;
  text-align: center;
  font-size: 18px; /* Increased font size */
  font-weight: bold; /* Make the text bold */
  color: #0056b3; /* Dark blue text color for better contrast */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Add a subtle shadow for a polished look */
}
.schedule-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  font-size: 14px;
  text-align: left;
}

.schedule-table th, .schedule-table td {
  border: 1px solid #ddd;
  padding: 8px;
}

.schedule-table th {
  background-color: #072c92;
  color: white;
  font-weight: bold;
  text-align: center;
}

.schedule-table tr:nth-child(even) {
  background-color: #f9f9f9;
}

.schedule-table tr:hover {
  background-color: #f1f1f1;
}

.schedule-table td {
  text-align: center;
}

.remove-button {
  background-color: #ff4d4d;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}
.link-container {
  margin-bottom: 20px;
}

.navigate-link {
  color: #007bff;
  text-decoration: none;
  font-size: 16px;
}

.navigate-link:hover {
  text-decoration: underline;
}

.sched-log-remove {
  width: 100%; /* Match the width of the row */
  height: 100%; /* Match the height of the row */
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #fdfdfd; /* Default blue background */
  color: #ff0000; /* Default white text */
  border-radius: 0; /* Remove border radius to match the row */
  padding: 0; /* Remove padding */
}

/* Disabled state for the button */
.sched-log-remove.processing {
  background-color: #fdfdfd; /* Gray background for disabled state */
  color: #0905ee; /* Lighter text color */
  cursor: not-allowed; /* Change cursor to not-allowed */
}