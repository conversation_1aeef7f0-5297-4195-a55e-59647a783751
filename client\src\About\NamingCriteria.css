/* Server Naming Dictionary.css */

.naming-criteria-container {
  padding: 40px;
  background-color: #fff; /* White background for the main content */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow for depth */
  border-radius: 8px; /* Slightly rounded corners */
}

.naming-criteria-header {
  text-align: center;
  color: #007bff; /* Bootstrap primary color */
  margin-bottom: 20px; /* Spacing below the heading */
}

.naming-criteria-subheader {
  margin-top: 30px;
  color: #495057; /* Darker shade for secondary headings */
}

.criteria-table, .example-table {
  width: 100%;
  border-collapse: separate; /* Use 'separate' to enable rounded corners */
  border-spacing: 0; /* Remove default spacing */
  border: 1px solid #9c9c9c; /* Mild border color */
  border-radius: 5px; /* Rounded corners */
}

.criteria-table-header, .example-table-header {
  background-color: #007bff; /* Primary color for the header */
  color: #fff; /* White text for contrast */
  padding: 12px; /* Increased padding for better spacing */
  text-align: left;
  border-bottom: 2px solid #b0b0b0; /* Mild color for header bottom border */
}

.criteria-table-cell, .example-table-cell {
  padding: 12px; /* Increased padding for better spacing */
  text-align: left;
  border-bottom: 1px solid #d9d9d9; /* Subtle border color */
}

.criteria-table-row:nth-child(even) .criteria-table-cell,
.example-table-row:nth-child(even) .example-table-cell {
  background-color: #f2f2f2; /* Light gray for even rows */
}

.criteria-table-row:hover .criteria-table-cell,
.example-table-row:hover .example-table-cell {
  background-color: #e9ecef; /* Slightly darker gray on hover */
}

/* Rounded corners for the first and last cells in the table */
.criteria-table-row:first-child .criteria-table-header:first-child,
.example-table-row:first-child .example-table-header:first-child {
  border-top-left-radius: 10px; /* Rounded top-left corner */
}

.criteria-table-row:first-child .criteria-table-header:last-child,
.example-table-row:first-child .example-table-header:last-child {
  border-top-right-radius: 10px; /* Rounded top-right corner */
}

.criteria-table-row:last-child .criteria-table-cell:first-child,
.example-table-row:last-child .example-table-cell:first-child {
  border-bottom-left-radius: 10px; /* Rounded bottom-left corner */
}

.criteria-table-row:last-child .criteria-table-cell:last-child,
.example-table-row:last-child .example-table-cell:last-child {
  border-bottom-right-radius: 10px; /* Rounded bottom-right corner */
}

@media (max-width: 600px) {
  .naming-criteria-container {
    padding: 15px; /* Adjust padding on smaller screens */
  }

  .criteria-table-header, .criteria-table-cell,
  .example-table-header, .example-table-cell {
    padding: 10px; /* Reduced padding for smaller devices */
  }
}
