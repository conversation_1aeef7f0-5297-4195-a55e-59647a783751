import React from 'react';
import { FaExclamationTriangle } from 'react-icons/fa';
import './Announcement.css'; // New CSS file for styling

const NewAnnouncement = () => {
    const handleClosePopup = () => {
        const popupOverlay = document.querySelector('.new-popup-overlay');
        if (popupOverlay) {
            popupOverlay.style.display = 'none';
        }
    };

    return (
        <div className="new-popup-overlay">
            <div className="new-announcement-container">
                <div className="new-announcement-header">
                    <h1>U Manage v 2.06.25.2.1</h1>
                </div>
                <div className="new-announcement-content">
                    <section>
                        <h2>New Service Actions</h2>
                        <p>Scheduler: Automate instance start and stop actions based on customizable schedules, including support for specific times and time zones.</p>
                        <a href="https://umanage.eit.hidglobal.com/scheduler" target="_blank" rel="noopener noreferrer">Scheduler</a>
                    </section>
                    <section>
                        <h2>New Provision Actions</h2>
                        <p>Provisioning: Dedicated pages for Segmented and Isolated instances with admin access configuration for both environments.</p>

                        <div className="provision-links">
                            <div className="provision-category">
                                <h3>Segmented Instances</h3>
                                <a href="https://umanage.eit.hidglobal.com/createwindows" target="_blank" rel="noopener noreferrer">Provision Windows (Segmented)</a>
                                <a href="https://umanage.eit.hidglobal.com/createlinux" target="_blank" rel="noopener noreferrer">Provision Linux (Segmented)</a>
                            </div>

                            <div className="provision-category">
                                <h3>Isolated Instances</h3>
                                <a href="https://umanage.eit.hidglobal.com/createwindowsisolated" target="_blank" rel="noopener noreferrer">Provision Windows (Isolated)</a>
                                <a href="https://umanage.eit.hidglobal.com/createlinuxisolated" target="_blank" rel="noopener noreferrer">Provision Linux (Isolated)</a>
                            </div>
                        </div>

                        <p className="admin-note">
                            <strong>Note:</strong> Admin access can be configured for both Segmented and Isolated provisioning during the setup process.
                        </p>
                    </section>
                    <section>
                        <h2>U-Access</h2>
                        <p>Fully automated post-approval process: Once approved, the appropriate group is automatically added, and a confirmation email is sent.</p>
                    </section>
                    <section>
                        <h2>QuickSight</h2>
                        <p>Embed and visualize data dashboards directly within the platform for enhanced analytics.</p>
                        <a href="https://umanage.eit.hidglobal.com/quicksight" target="_blank" rel="noopener noreferrer">QuickSight</a>
                    </section>
                </div>
                <button className="new-close-btn" onClick={handleClosePopup}>Close</button>
            </div>
        </div>
    );
};

export default NewAnnouncement;