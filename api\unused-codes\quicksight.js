const express = require('express');
const csvParser = require('csv-parser');
const AWS = require('aws-sdk');
const router = express.Router();

module.exports = (s3, Readable) => {
  router.get('/', async (req, res) => {
    console.log("req");
    
const quicksight = new AWS.QuickSight({
    region: 'us-east-1'
  });
  
    try {
        const params = {
            AwsAccountId: '************',
            DashboardId: '8d0292a5-7626-48fe-9688-92570f7076eb',
            IdentityType: 'QUICKSIGHT',
            //UserArn: 'arn:aws:quicksight:us-east-1:************:user/default/HID-Quicksight-Reader/<EMAIL>',
            UserArn: 'arn:aws:quicksight:us-east-1:************:user/default/HID-Quicksight-Admin/IAMS-USER',
            SessionLifetimeInMinutes: 600,
            Namespace: 'default',
            ResetDisabled: true,
            UndoRedoDisabled: true
          };
      
      try {
        const result = await quicksight.getDashboardEmbedUrl(params).promise();
        res.json({ EmbedUrl: result.EmbedUrl });
      } catch (error) {
        console.error('QuickSight Error:', error);
        res.status(500).json({ error: error.message });
      }
    } catch (err) {
      res.status(500).send(err.message);
    }
  });

  router.post('/', async (req, res) => {
    console.log("req post ");
    
const quicksight = new AWS.QuickSight({
    region: 'us-east-1'
  });
  
    try {
        const params = {
            AwsAccountId: '************',
            DashboardId: '8d0292a5-7626-48fe-9688-92570f7076eb',
            IdentityType: 'QUICKSIGHT',
            //UserArn: 'arn:aws:quicksight:us-east-1:************:user/default/HID-Quicksight-Reader/<EMAIL>',
            UserArn: 'arn:aws:quicksight:us-east-1:************:user/default/HID-Quicksight-Admin/IAMS-USER',
            SessionLifetimeInMinutes: 600,
            Namespace: 'default',
            ResetDisabled: true,
            UndoRedoDisabled: true
          };
      
      try {
        const result = await quicksight.getDashboardEmbedUrl(params).promise();
        res.json({ EmbedUrl: result.EmbedUrl });
      } catch (error) {
        console.error('QuickSight Error:', error);
        res.status(500).json({ error: error.message });
      }
    } catch (err) {
      res.status(500).send(err.message);
    }
  });
 
  return router;
};