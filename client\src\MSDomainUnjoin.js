import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './DomainJoin.css';
import HIDlogo from './assets/hidLogo.png';
import Select from 'react-select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser} from '@fortawesome/free-solid-svg-icons';
import { MdInfo  } from "react-icons/md";
import Ulogo from './assets/Ulogo.png';
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import Loading from './assets/Rocket.gif';
import Navbar from './Navbar';
function DomainJoin() {
  const [data, setData] = useState([]);
  const navigate = useNavigate();
  const [data1, setData1] = useState([]);
  const [accountNames, setAccountNames] = useState([]);
  const [accountId, setAccountId] = useState([]);
  const [regions, setRegions] = useState([]);
  const [instances, setInstances] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState('');
  const [selectedAccountID, setSelectedAccountID] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('');
  const [selectedInstance, setSelectedInstance] = useState('');
  const [selectedBusinessContact, setSelectedBusinessContact] = useState('');
  const [message, setMessage] = useState('');
  // const [user, setUser] = useState('<EMAIL>');
  const [isAcknowledged, setIsAcknowledged] = useState(false);
  const [instanceDetails, setInstanceDetails] = useState({});
  const [firstName, setfirstname] = useState('');
  const [alertMessage, setAlertMessage] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
   const [user, setUser] = useState(
 {
    email: '<EMAIL>',
    displayName: 'Guest',
    firstName: 'Guest'
  });
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
        setUser(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);
  
  useEffect(() => {
    axios.get('https://umanage.dev.hidglobal.com/api/user')
      .then(response => {
        const fetchedData = response.data;
        // console.log('Fetched user data:', fetchedData);
        // console.log(user);
        const userEntry = fetchedData.find(entry => entry.user === user.email);
        // console.log('User entry:', userEntry);
  
        if (userEntry) {
          const accountIds = userEntry.accounts.split(',').map(account => account.trim());
          // console.log('Parsed account IDs:', accountIds);
          setAccountId(accountIds);
        } else {
          setAccountId([]);
        }
      })
      .catch(error => {
        // console.error('Error fetching user accounts:', error);
      });
  }, [user]);
  
  useEffect(() => {
    if (accountId.length > 0) {
      axios.get('https://umanage.dev.hidglobal.com/api/s3')   
        .then(response => {
          let fetchedData = response.data;
          // console.log('Fetched S3 data:', fetchedData);
  
          fetchedData = fetchedData.filter(item => accountId.includes(item.accountId));
          // console.log('Filtered S3 data:', fetchedData);
  
          const uniqueAccounts = [...new Set(fetchedData.map(item => item.AccountName))];
          // console.log('Unique account names:', uniqueAccounts);
  
          setData(fetchedData);
          setAccountNames(uniqueAccounts);
        })
        .catch(error => {
          // console.error('Error fetching S3 data:', error);
        });
    }
  }, [accountId]);
  // Ef

  useEffect(() => {
    if (selectedAccount) {
      // Filter regions based on selected account
      const filteredData = data.filter(item => item.AccountName === selectedAccount);
      const uniqueRegions = [...new Set(filteredData.map(item => item.Region))];
      setRegions(uniqueRegions);
    }
  }, [selectedAccount, data]);

  useEffect(() => {
    if (selectedRegion && selectedAccount) {
      // Filter instances based on selected region and account
      const vpcIds = [
            "vpc-0faa33d1d0178ace1",
            "vpc-0d1bdf90404c142b3",
            "vpc-0ba7a646e7fe80754",
            "vpc-07e8fe22866751e6c",
            "vpc-0bedc0e60c22bac16",
            "vpc-0df8253ab66a7d392",
            "vpc-034a8e4d9e34369a8",
            "vpc-0c5590b85622bb773",
            "vpc-03e4615dd2bfffa39",
            "vpc-08443050f4a738752",
            "vpc-04988ba8ca514adcd",
            "vpc-0ebaeb09b8c123ffa",
            "vpc-0ab72d903129b036b",
            "vpc-065265c813259da83",
            "vpc-08f21242d59a9a144",
            "vpc-05e5183a0cde645ff",
            "vpc-035593dbf23fc85b5",
            "vpc-08cb4595c7583840e",
            "vpc-0deec3f61ea044828",
            "vpc-09f608a2b7dfda7bf",
            "vpc-0bd0f2355667091ff",
            "vpc-07ba5a4ba8248e682",
            "vpc-0e0d0828f8d23617f",
            "vpc-01a3e434a06879958",
            "vpc-071c28836fe6018d7",
            "vpc-0d8c55543ef7ac0a5",
            "vpc-06a6c59c7874ab2dd",
            "vpc-0371c4dbf17cc7733",
            "vpc-07673c084c4fd9a55",
            "vpc-0ed5cb7789cb4526d",
            "vpc-0d64edddd577619b9",
            "vpc-04fe473e8ca76870e",
            "vpc-0923c5f7d79d7afd9",
            "vpc-08ce7197c8ac5314a",
            "vpc-034c527517f37db7e",
            "vpc-074083ee2bf8a79f5",
            "vpc-066d3447c3c9459ab",
            "vpc-0e08f7cbfac007a94",
            "vpc-02a5781d1c9bd227c",
            "vpc-04b28644b7282723e"

      ];

      // Filter instances based on selected region, account, platform (Windows), and VPC ID
      const filteredData = data.filter(item => 
        item.Region === selectedRegion && 
        item.AccountName === selectedAccount &&
        item.platform === "windows" && // Filter for Windows platform
        vpcIds.includes(item.VPCid) // Filter for specific VPC IDs
      );
      
      // Update state to include both id and InstanceName
      setInstances(filteredData.map(item => ({
        id: item.InstanceId,
        label: `${item.InstanceId} - ${item.InstanceName}`, // Combine InstanceId and InstanceName for display
        name: item.InstanceName
      })));
    }
  }, [selectedRegion, selectedAccount, data]);

  const handleInstanceChange = (selectedOption) => {
    setSelectedInstance(selectedOption.id);

    // Find the selected instance using the selected instance ID
    const instance = data.find((inst) => inst.InstanceId === selectedOption.id);
    if (instance) {
      setInstanceDetails({
        InstanceType: instance.InstanceType,
        InstanceName: instance.InstanceName,
        BusinessArea: instance.BusinessArea,
        CostCenter: instance.CostCenter,
        BusinessSegment: instance.BusinessSegment,
        BusinessContactEmail: instance.BusinessContactEmail,
        Environment: instance.Environment,
        SupportTier: instance.SupportTier,
        ProvisioningEngineer: instance.ProvisioningEngineer,
        TechnicalContact: instance.TechnicalContact,
        FunctionalArea: instance.FunctionalArea,
        BackupPlan: instance.BackupPlan,
      });
      setSelectedAccountID(instance.accountId);
    }

    // Set the business contact based on the instance ID
    const businessContact = data.find((item) => item.InstanceId === selectedOption.id)?.BusinessContact;
    setSelectedBusinessContact(businessContact || '');

    // Smooth scroll to the instance details section
    setTimeout(() => {
      document.querySelector('.instance-details').scrollIntoView({ behavior: 'smooth' });
    }, 200);
  };
  const[messagestatus, setMessagestatus] = useState();
  
  const startListeningForUpdates = () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    const url = `https://umanage.dev.hidglobal.com/api/trigger-ssm/domainunjoin/windos?instanceId=${selectedInstance}&accountId=${selectedAccountID}&region=${selectedRegion}&instanceName=${instanceDetails.InstanceName}&group1=${groupValues[0]}&group2=${groupValues[1]}&group3=${groupValues[2]}&group4=${groupValues[3]}&firstname=${user.firstName}&email=${user.email}`;
    //instanceId, region,  accountId, instanceName,group1,group2,group3,group4} = req.body;
    const eventSource = new EventSource(url);

    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      setAlertMessage(data.message);
      if(data.message.substring(0,4)=="RITM")
        { 
          setAlertMessage("");
          setMessage(data.message);
          setMessagestatus(true);
        }
      if(data.message.substring(0,5)=="Error"){
        setAlertMessage("");
        setMessage(data.message);
        setMessagestatus(false);
      }

    };

    eventSource.onerror = () => {
      console.error('Error with SSE connection.');
      setIsProcessing(false);
      eventSource.close();
    };

    eventSource.onopen = () => {
      console.log('Connected to the SSE server.');
    };
  };


  const handleTriggerSSM = () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    axios.post('https://umanage.dev.hidglobal.com/api/trigger-ssm/domainjoin/windos', {
      instanceId: selectedInstance,
      region:"",
      accountId:"",
      instanceName:"",
      group1:"",
      group2:"",
      group3:"",
      group4:""
    })
    .then(response => {
       
      setMessage(`Domain Changed Successfully !!!`);
      setMessagestatus(true);
    
      })
    .catch(error => {
      setMessage(`Error: ${error}`);
      setMessagestatus(false);
    });
  };
  async function logout() {
    try {
      // Send a POST request to the backend logout endpoint
      const response = await fetch('/api/logout', {
        method: 'POST',
        credentials: 'include' // Include cookies with the request
      });
      // Check if the response is successful
      if (response.ok) {
        const result = await response.json();
        // // console.log(result.message); // Log the success message or handle it as needed
   
        // Optionally, redirect the user to a different page or update the UI
        window.location.href = '/login'; // Redirect to the login page or homepage
      } else {
        // // console.error('Logout failed');
      }
    } catch (error) {
      // // console.error('Error during logout:', error);
    }
  };
  
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showServiceDropdown, setShowServiceDropdown] = useState(false);
  const [showPortfolioDropdown, setShowPortfolioDropdown] = useState(false);
  const [showTicketTooltip, setshowTicketTooltip] = useState(false); 


  const [groupCount, setGroupCount] = useState(0);

  // State to store the input values for each group
  const [groupValues, setGroupValues] = useState(Array(4).fill(""));

  // Handle incrementing the group count
  const incrementCount = () => {
    setGroupCount((prevCount) => Math.min(prevCount + 1, 4)); // Max count is 4
  };

  // Handle decrementing the group count
  const decrementCount = () => {
    setGroupCount((prevCount) => Math.max(prevCount - 1, 0)); // Min count is 1
  };

  // Handle change in the group input fields
  const handleGroupInputChange = (index, value) => {
    const newGroupValues = [...groupValues];
    newGroupValues[index] = value;
    setGroupValues(newGroupValues);
  };
  return (
    <div className="Stop-App">
       <Navbar />
  <div className="full-page-content">
  {(message || alertMessage) && (
  <div className="notification-overlay">
    <div className="notification-container1">
      {alertMessage && !message && (
        <div className="alert-card1">
          <div className="alert-header1">
            <div className="loading-icon1">
              <img src={Loading} alt="Loading" className="loading-gif" />
            </div>
            <p className="alert-message1">{alertMessage}</p>
            <button className="close-button1" onClick={() => setAlertMessage(null)}>
              <IoIosClose />
            </button>
          </div>
        </div>
      )}

      {message && (
        <div className={`status-card1 ${messagestatus ? 'success' : 'error'}`}>
          <div className={`status-icon1 ${messagestatus ? 'pop-animation1' : 'shake-animation1'}`}>
            {messagestatus ? <FaCheckCircle size={30} /> : <FaTimesCircle size={30} />}
          </div>
          <p >{message}</p>
          <button
            className="close-button1"
            onClick={() => {
              setMessage(null);
              setAlertMessage(null);
            }}
          >
            <IoIosClose />
          </button>
        </div>
      )}
    </div>
  </div>
)}
  <p className="main-title label-with-icon">
    Windows Domain Unjoin </p>
    <div className="info-icon-container">
      <MdInfo
        className="info-icon"
        size={23} // Adjust the size of the icon
        onClick={() => setshowTicketTooltip(!showTicketTooltip)} // Toggle tooltip visibility
      />
    </div>
    {showTicketTooltip && (
      <div className="tooltip-overlay">
        <div className="tooltip-card">
          <p>
            Only the windows instance that are domain joined is shown.
          </p>
        </div>
      </div>
    )}

  
  {/* Description below the heading */}
  

  <div className="dropdown-container">
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Account Selection</h2>
      <p className="dropdown-description">Select the account to manage instances.</p>
      <select className="form-control" value={selectedAccount} onChange={(e) => setSelectedAccount(e.target.value)}>
        <option value="">Select Account</option>
        {accountNames.map(account => (
          <option key={account} value={account}>{account}</option>
        ))}
      </select>
    </div>

    <div className="dropdown-section">
      <h2 className="dropdown-heading">Region Selection</h2>
      <p className="dropdown-description">Choose the region for the selected account.</p>
      <select className="form-control" value={selectedRegion} onChange={(e) => setSelectedRegion(e.target.value)} >
        <option value="">Select Region</option>
        {regions.map(region => (
          <option key={region} value={region}>{region}</option>
        ))}
      </select>
    </div>
  </div>

   
  <div className="dropdown-container">    
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Instance Selection</h2>
      <p className="dropdown-description">Pick an instance from the list.</p>
      <Select
        value={instances.find(instance => instance.id === selectedInstance)} // Find the selected instance
        onChange={handleInstanceChange} // Handle instance change
        options={instances} // Options for the dropdown
        isSearchable={true} // Enable search
        placeholder="Search or select an instance"
         // Add form-control class
      />
    </div>
    
    <div className="dropdown-section">
    <h2 className="dropdown-heading"> Group Count</h2>
    <p className="dropdown-description">Pick number of groups need to attached</p>
      {/* Counter for group count */}
      <div className="counter">
        <button onClick={decrementCount}>-</button>
        <span>{groupCount}</span>
        <button onClick={incrementCount}>+</button>
      </div>
</div>
</div>
<div className="dropdown-container">  
      {Array.from({ length: groupCount }, (_, index) => (
        
        <div className="dropdown-section" key={index}>
          <h2 className="dropdown-heading">Group {index + 1}</h2>
          <input
            type="text"
            placeholder={`Enter value for Group ${index + 1}`}
            value={groupValues[index]}
            className="form-control"
            onChange={(e) => handleGroupInputChange(index, e.target.value)}
          />
        </div>
        
      ))}
    </div>
      {/* Display the stored values */}
      
    {console.log("GroupValues",groupValues)}
    {console.log("instanceDetails",instanceDetails)}


  {selectedInstance && (
    <div className="instance-details">
      <p><strong>Instance Type <span className="colon">:</span></strong> {instanceDetails.InstanceType}</p>
      <p><strong>Instance Name <span className="colon">:</span></strong> {instanceDetails.InstanceName}</p>
      <p><strong>Business Area <span className="colon">:</span></strong> {instanceDetails.BusinessArea}</p>
      <p><strong>Cost Center<span className="colon">:</span></strong> {instanceDetails.CostCenter}</p>
      <p><strong>Business Segment<span className="colon">:</span></strong> {instanceDetails.BusinessSegment}</p>
      <p><strong>Business Contact Email<span className="colon">:</span></strong> {instanceDetails.BusinessContactEmail}</p>
      <p><strong>Environment<span className="colon">:</span></strong> {instanceDetails.Environment}</p>
      <p><strong>Support Tier<span className="colon">:</span></strong> {instanceDetails.SupportTier}</p>
      <p><strong>Provisioning Engineer<span className="colon">:</span></strong> {instanceDetails.ProvisioningEngineer}</p>
      <p><strong>Technical Contact<span className="colon">:</span></strong> {instanceDetails.TechnicalContact}</p>
      <p><strong>Functional Area<span className="colon">:</span></strong> {instanceDetails.FunctionalArea}</p>
      <p><strong>Backup Plan<span className="colon">:</span></strong> {instanceDetails.BackupPlan}</p>
    </div>
  )}

  {selectedInstance && (
    <div className="checkbox-container">
      <input 
        type="checkbox" 
        id="acknowledge" 
        checked={isAcknowledged} 
        onChange={() => setIsAcknowledged(!isAcknowledged)} 
      />
      <label htmlFor="acknowledge" className="checkbox-label">
        I acknowledge the risks associated with unjoining the instance from the domain.
      </label>
    </div>
  )}

  <button 
    className="TriggerSSM" 
    // disabled={!isAcknowledged || !selectedInstance || isProcessing} 
    onClick={startListeningForUpdates}
  >
    Domain Join
  </button>
  
</div>

    </div>
  );
}

export default DomainJoin;
