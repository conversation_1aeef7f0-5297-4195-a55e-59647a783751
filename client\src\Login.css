@import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;700&display=swap');

.login-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-image: url("/src/assets/home-bg.jpg");
  background-size: cover;
  font-family: 'Lato', sans-serif;
  background-size: 200% 200%;
  animation: gradientAnimation 50s ease infinite;
  position: relative;
  overflow: hidden;
}





@keyframes gradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}




.login-container {
  display: flex;
  width: 1000px;
  height: 400px;
  
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
  background-color: #ffffff;
  z-index: 2;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-container:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.4);
}

.login-left {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 75%;
  padding: 20px;
  
  position: relative;
}
.login-left::after {
  content: '';
  position: absolute;
  top: 50%; /* Position the border vertically centered */
  right: 0; /* Align the border to the right side */
  transform: translateY(-50%); /* Center the border vertically */
  height: 290px; /* Set the desired height of the border */
  border-right: 3px solid #637597; /* Set the border style */
}

.login-logo {
  width: 500px;
  margin-bottom: 70px;
  transform: translateY(0);
  transition: transform 0.5s ease-in-out;
}

.login-left h2 {
  margin: 0 0 10px;
  font-size: 50px;
  font-weight: 900;
  color: #243364;
  text-align: center;
  z-index: 2;
}

.login-right {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 50%;
  padding: 20px;
}

.cloud-image {
  width: 250px;
  margin-bottom: 50px;
  filter: hue-rotate(300deg) saturate(150%) brightness(1.02);
}



.login-button {
  padding: 13px 30px;
  font-size: 30px;
  color: #ffffff;
  background-color: #0f4983;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  margin-bottom: 10px;
  font-family: Lato;
  font-weight: 600;
  transition: background-color 0.3s, transform 0.3s;
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.2);
}

.login-button:hover {
  background-color: #063059;
  transform: translateY(-10px);
  box-shadow: 0 15px 20px rgba(0, 0, 0, 0.3);
}

