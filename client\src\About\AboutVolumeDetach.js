import React from 'react';

const AboutVolumeDetach = () => {
    return (
        <div className="about-section">
                          <h2 className="about-start-title">Volume Detach</h2>
          <p className="about-start-description">
  The Volume Detach action is used to <strong>delete an EBS volume from an EC2 instance</strong>. 
</p>


            <h3 className="about-start-subtitle">Steps to Detach Volume</h3>
            <ol className="about-start-steps">
              <li className="about-start-step">
                <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
                <div className="about-start-image-container">
                  {/* <img src={Start} alt="Select Account Sample" className="about-start-image" /> */}
                </div>
              </li>
              <li className="about-start-step">
                <strong>Select the AZ(AvailabilityZone):</strong> Pick the AvailabilityZone associated with that account, as Volume availability is AvailabilityZone-specific.
              </li>
              <li className="about-start-step">
                <strong>Select the Instance:</strong> Locate the instance by its ID or Name for easy identification.
                
              </li>
              
             <li className="about-start-step">
                <strong>Select the Device Names:</strong> Locate the Volume to br detach.
                
              </li>
             
              <li className="about-start-step">
                <strong>Click the "Detach Volume" Button:</strong> Execute the Detach action. You can either wait in the portal for a 
                status update or log out, as a confirmation email will be sent indicating the result of the action.
              </li>
            </ol>
        </div>
    );
};

export default AboutVolumeDetach;