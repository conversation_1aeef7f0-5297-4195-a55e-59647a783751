const express = require('express');
const csvParser = require('csv-parser');
const router = express.Router();
const nodemailer = require('nodemailer');

module.exports = (s3, Readable) => {
  router.post('/', async (req, res) => {
    console.log(req.body);
    try {
        console.log(req);
          const transporter = nodemailer.createTransport({
            host: 'relay.assaabloy.net',
            port: 25,
            secure: false,
            auth: {
              user: '<EMAIL>',
              pass: '',
            },
          });
      
          const mail = await transporter.sendMail({
            from: req.body.from,
            to: req.body.to,
            cc: `<EMAIL>,${req.body.cc}`,
            subject: req.body.subject,
            html: req.body.html ,
      
          });
          res.status(200).send("successfully sent");
    } catch (err) {
      res.status(500).send(err.message);
    }
  });
 
  return router;
};