const AWS = require('aws-sdk');
const XLSX = require('xlsx');
const cron = require('node-cron');

const s3 = new AWS.S3();

const BUCKET_NAME = 'server-provision-application';
const SOURCE_FILE = 'tickets/Create.xlsx';
const DESTINATION_FILE = 'reports/weekly-report.xlsx';

// Function to convert Excel serial date to JavaScript Date
const convertExcelDateToJSDate = (serial) => {
  const excelEpoch = new Date(1900, 0, 1); // January 1, 1900
  const daysOffset = serial - 2; // Excel incorrectly treats 1900 as a leap year, so subtract 2 days
  const millisecondsPerDay = 24 * 60 * 60 * 1000;
  return new Date(excelEpoch.getTime() + daysOffset * millisecondsPerDay);
};

const fetchAndUpdateWeeklyReport = async () => {
  try {
    // Fetch the source Excel file from S3
    const params = {
      Bucket: BUCKET_NAME,
      Key: SOURCE_FILE,
    };

    console.log('Fetching source file from S3...');
    const data = await s3.getObject(params).promise();
    console.log('Source file fetched successfully.');

    const workbook = XLSX.read(data.Body, { type: 'buffer' });
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];

    // Convert sheet data to JSON
    const rows = XLSX.utils.sheet_to_json(sheet);
    console.log('Rows from source file:', rows);

    // Filter rows for the current week
    const currentWeekStart = new Date();
    currentWeekStart.setDate(currentWeekStart.getDate() - currentWeekStart.getDay()); // Start of the week (Sunday)
    const currentWeekEnd = new Date(currentWeekStart);
    currentWeekEnd.setDate(currentWeekStart.getDate() + 6); // End of the week (Saturday)

    console.log('Current week start:', currentWeekStart);
    console.log('Current week end:', currentWeekEnd);

    const filteredRows = rows.filter((row) => {
      const triggerStopTime = row['trigger stop time']; // Assuming the column name is 'trigger stop time'
      const createdDate = convertExcelDateToJSDate(triggerStopTime);
      console.log('Checking row:', row);
      console.log('Converted CreatedDate:', createdDate);
      return createdDate >= currentWeekStart && createdDate <= currentWeekEnd;
    });

    console.log('Filtered rows for the current week:', filteredRows);

    // Prepare data for the new Excel file
    const newSheetData = [['Instance Name', 'Instance ID', 'IP Address', 'Created Date']]; // Headers
    filteredRows.forEach((row) => {
      const createdDate = convertExcelDateToJSDate(row['trigger stop time']);
      newSheetData.push([row['InstanceName'], row['InstanceId'], row['IPAddress'], createdDate.toLocaleDateString()]);
    });

    console.log('Data to be written to the new Excel file:', newSheetData);

    // Create a new workbook and sheet
    const newWorkbook = XLSX.utils.book_new();
    const newSheet = XLSX.utils.aoa_to_sheet(newSheetData);
    XLSX.utils.book_append_sheet(newWorkbook, newSheet, 'Weekly Report');

    // Convert the new workbook to a buffer
    const newFileBuffer = XLSX.write(newWorkbook, { type: 'buffer', bookType: 'xlsx' });

    // Upload the new file to S3
    const uploadParams = {
      Bucket: BUCKET_NAME,
      Key: DESTINATION_FILE,
      Body: newFileBuffer,
      ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };

    await s3.putObject(uploadParams).promise();
    console.log('Weekly report updated successfully!');
  } catch (error) {
    console.error('Error generating weekly report:', error);
  }
};

// Run the function immediately for testing
fetchAndUpdateWeeklyReport();

// Schedule the task to run every minute for testing
cron.schedule('* * * * *', fetchAndUpdateWeeklyReport);