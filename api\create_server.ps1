# Set Variables
$username = "<EMAIL>"
$password = "x5EMe7a4W3Qfb6FDo82N"
$baseUrl = "https://adx-api.assaabloy.net/restApi"

# ------------------------
# 🟢 Step 1: Create Session
# ------------------------
$response = Invoke-RestMethod -Uri "$baseUrl/api/authSessions/create" -Method Post -Body (
    @{
        username = $username
        password = $password
    } | ConvertTo-Json -Depth 10
) -ContentType "application/json"

# Debug: Print Response
Write-Host "Session Response: $response"

if (-not $response.sessionId) {
    Write-Host "❌ Error: Unable to create session."
    exit 1
}
$sessionId = $response.sessionId

# ------------------------
# 🔐 Step 2: Get Auth Token
# ------------------------
$authResponse = Invoke-RestMethod -Uri "$baseUrl/api/auth" -Method Post -Body (
    @{
        sessionId = $sessionId
        type = 0
    } | ConvertTo-Json -Depth 10
) -ContentType "application/json"

# Debug: Print Auth Token
Write-Host "Auth Token Response: $authResponse"

if (-not $authResponse.token) {
    Write-Host "❌ Error: Unable to obtain authentication token."
    exit 1
}
$token = $authResponse.token

# ------------------------
# 🖥️ Step 3: Create Server
# ------------------------
$response = Invoke-RestMethod -Uri "$baseUrl/api/directoryObjects/executeCustomCommand" -Method Post -Headers @{
    "Adm-Authorization" = $token
} -Body (
    @{
        directoryObject = "DC=ad,DC=global"
        customCommandId = "c39819b3-4a52-4476-9950-3e2e6c8bfdd5"
        parameters = @(
            @{ type = "List"; name = "param-Provider"; value = "A" },
            @{ type = "List"; name = "param-Region"; value = "F" },
            @{ type = "List"; name = "param-Locale"; value = "C" },
            @{ type = "List"; name = "param-SiteDesignator"; value = "1" },
            @{ type = "List"; name = "param-Zone"; value = "1" },
            @{ type = "List"; name = "param-Environment"; value = "SBX" },
            @{ type = "List"; name = "param-OS"; value = "MS" },
            @{ type = "List"; name = "param-Application"; value = "TES07" },
            @{ type = "List"; name = "param-PatchingGroup"; value = "DEVTEST" },
            @{ type = "Text"; name = "param-Description"; value = "This is a test" },
            @{
                type = "ADObject"; name = "param-SiteOU"; value = @(
                    @{ referenceType = 0; key = "OU=CNSUH,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global" }
                )
            },
            @{
                type = "ADObject"; name = "param-ManagedBy"; value = @(
                    @{ referenceType = 0; key = "CN=Srinivasan, Prasana,OU=INCHE,OU=UsersInternal,OU=Users,OU=HID,OU=SSC,DC=ad,DC=global" }
                )
            }
        )
    } | ConvertTo-Json -Depth 10
) -ContentType "application/json"

# ------------------------
# 📊 Step 4: Output Response
# ------------------------
if ($response) {
    Write-Host "✅ Server created successfully. Response:"
    $response | ConvertTo-Json -Depth 10
} else {
    Write-Host "❌ Error: Unable to create server."
}
