const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');
const nodemailer = require('nodemailer');
const fs = require("fs");
// Export the function to set up the router with necessary utilities
module.exports = (generateTicketNumber, storeDataInS3) => {
  let originalCredentials = AWS.config.credentials;
 
  // Function to assume an IAM role and return temporary credentials
  const assumeRole = async (accountId,firstname) => {
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::${accountId}:role/CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    console.log(firstname);
    console.log(params);
    try {
      const data = await sts.assumeRole(params).promise();
      console.log("i am in assume role success");
      return data.Credentials;
    } catch (error) {
        console.log(error);
      AWS.config.update({ credentials: null });
        console.error('Error assuming role:', error.code, error.message);
        if (error.code === 'AccessDenied') {
          const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay));
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
            // Handle AccessDenied specifically, maybe log the accounts involved
       
     
    }else{
      console.error('Error assuming role:', error);
      throw error;
    }}
  };
 
  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };
 
  // Function to configure AWS services (SSM, S3, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      cloudFormation: new AWS.CloudFormation()
    };
  };
 
 
  const handleSSMRequest = async (req, res, documentName) => {
    const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} =  req.body;
 console.log("i am in migration  ssm");
 console.log(req.body);
    let firstname= req.body.firstname;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;
    res.setHeader('Content-Type', 'text/plain');
    res.setHeader('Transfer-Encoding', 'chunked');

    const sendUpdate = (message) => {
      res.write(`${message }\n\n`);
    };
// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
    if (!instanceId || !region || !accountId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
 
    const params = {
      DocumentName: documentName,
      Parameters: {
        InstanceID: [instanceId],
        VolumeId: req.body.VolumeId,
        KmsKeyId: [req.body.KMSKey[0]],
        DeviceName: req.body.DeviceName,
        Region: [req.body.region],
        AvailabilityZone: [req.body.availabilityzone],
        ShareWithAccount:[req.body.targetaccount],
        AutomationAssumeRole: [`arn:aws:iam::${accountId}:role/CrossAccountAccessRole`]
        // Add other parameters required by your SSM document here
      }
    };
 
    try {
      // AWS.config.update({ credentials: originalCredentials });
      if(accountId!=************){
      const credentials = await assumeRole(accountId,y);
      await initializeAWS(credentials, region);}else{AWS.config.update({
        region: region
      });}
      const { ssm } = configureAWS();
      console.log("came back to function");
      console.log(params);
      const ticketNumber = generateTicketNumber();
      let executionID;
      const startTime = new Date();
      let data;
      try{
       data = await ssm.startAutomationExecution(params).promise();
     }catch(err){
        console.log(err);
      }
      executionID = data.AutomationExecutionId;
      console.log(originalCredentials);
      AWS.config.update({ credentials: originalCredentials });
      // const getAutomationssmExecutionStatus1 = async (ssm, executionId) => {
      //   try {
      //     const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      //     console.log('Automation execution data:', data); // Log entire response
      //     const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      //     return data;
      //   } catch (error) {
      //     console.error('Error fetching execution status:', error);
          
      //     throw error;
      //   }
      // };
      const getAutomationssmExecutionStatus1 = async (ssm, executionId, maxRetries = 5, baseDelay = 1000) => {
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
          try {
            const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
            console.log('Automation execution data:', data);
            return data;
          } catch (error) {
            console.error(`Attempt ${attempt + 1} - Error fetching execution status:`, error);
      
            if (error.code === 'ThrottlingException') {
              const delay = baseDelay * Math.pow(2, attempt); // exponential backoff
              console.log(`Throttled. Retrying after ${delay}ms...`);
              await new Promise(resolve => setTimeout(resolve, delay));
            } else {
              throw error; // Rethrow for non-throttling errors
            }
          }
        }
      
        throw new Error('Max retries reached for getAutomationExecution');
      };
      
      let servicenownumber;
        // Poll status and fetch detailed result
        const pollAutomationssmExecutionStatus1 = async (ssm, executionId, interval = 3000) => {
          return new Promise((resolve, reject) => {
            const intervalId = setInterval(async () => {
              try {
                const data = await getAutomationssmExecutionStatus1(ssm, executionId);
                const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
                console.log('Current status:', status);
               
                sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
                if (['Success', 'Failed', 'TimedOut', 'Cancelled','Pending'].includes(status)) {
                    if ([ 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
                        sendUpdate(`Error: ${data.AutomationExecution.CurrentStepName} step is ${status}`);
                    }
                  clearInterval(intervalId);
                  resolve(status);
                }
              } catch (error) {
                clearInterval(intervalId);
                reject(error);
              }
            }, interval);
          });
        };
      // Poll status and fetch detailed result
      const status = await pollAutomationssmExecutionStatus1(ssm, executionID);
      const executionDetails = await getAutomationssmExecutionStatus1(ssm, executionID);
      // const data = await getAutomationssmExecutionStatus1(ssm, executionId);
      const status2= executionDetails.AutomationExecution ? executionDetails.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      console.log(executionDetails);
      console.log(status2);
      console.log(executionDetails.AutomationExecution.Outputs);
      const outputs = executionDetails?.AutomationExecution?.Outputs || {};

console.log('✅ Outputs:');
Object.keys(outputs).forEach((key) => {
  console.log(`${key}:`, outputs[key]);
});
      const copyImageId = outputs['CopyImage.imageId']?.[0]; // Access first value
      console.log(`📸 Copy Image ID: ${copyImageId}`);
      
      let createAmiId = outputs['Create_AMI_1.ImageId']?.[0];
      console.log(`💾 Created AMI ID: ${createAmiId}`);
      let ec2Tags = outputs['Get_Ec2_Tags_AMI_KMS_KEY.EC2Tags']?.[0];
console.log(outputs['Get_Ec2_Tags_AMI_KMS_KEY.EC2Tags']?.[0]);
console.log(outputs['Get_Ec2_Tags_AMI.EC2Tags']?.[0]);
      // Check if the output is valid or contains an error message
      if (!ec2Tags || ec2Tags.includes("No output available yet")) {
          ec2Tags = outputs['Get_Ec2_Tags_AMI.EC2Tags']?.[0]; // Try the alternative step
      }
      if (!createAmiId || createAmiId.includes("No output available yet")) {
        createAmiId = copyImageId; // Try the alternative step
    }
      
      // Final check before logging
      if (ec2Tags && !ec2Tags.includes("No output available yet")) {
          console.log(`🏷️ EC2 Tags: ${ec2Tags}`);
      } else {
          console.log("⚠️ No valid EC2 Tags available.");
      }
      //console.log(ec2Tags._CostCenterDescription);
      //console.log(ec2Tags[_CostCenterDescription]);
      let nextToken = null; // Pagination token
      let allStepExecutions = []; // Store all step executions
      async function fetchAllStepExecutions() {
      try {
        do {
          // Fetch execution details with pagination
          const params = {
            AutomationExecutionId: executionID,
            MaxResults: 50, // Max steps per page (50 is the limit)
            NextToken: nextToken, // Use NextToken for pagination
          };
    
          const executionDetails = await ssm
            .describeAutomationStepExecutions(params)
            .promise();
    
          // Add fetched steps to the main array
          if (executionDetails.StepExecutions) {
            allStepExecutions.push(...executionDetails.StepExecutions);
          }
    
          // Get the NextToken if there are more pages
          nextToken = executionDetails.NextToken || null;
        } while (nextToken); // Continue fetching if NextToken is not null
      } catch (error) {
        console.error("Error fetching step executions:", error);
      }}

      let useroutputs='';
      let tags='';
 // Append log data to "execution_logs.txt"
 async function logStepOutputs() {
    //await fetchAllStepExecutions();
  
    if (allStepExecutions.length === 0) {
      fs.appendFileSync(
        "migration_logs.txt",
        "No StepExecutions found\n\n",
        "utf8"
      );
      return;
    }
  
    // Iterate through all steps
    allStepExecutions.forEach((step, index) => {
      // Check if the step name starts with "Output"
      if (step.StepName.startsWith("Output")) {
        if(step.StepStatus === "Success"){
        const stepLogData = `
  Step ${index + 1}: ${step.StepName}
  Status: ${step.StepStatus}
  Outputs: ${JSON.stringify(step.Outputs, null, 2)}
  `;
  useroutputs += JSON.stringify(step.Outputs["Output1"][0], null, 2);
tags=step.Outputs["Output2"][0]

        // Append step output to "migration_logs.txt"
        fs.appendFileSync("migration_logs.txt", stepLogData + "\n\n", "utf8");
      }}
    });
  
    // Log final outputs if needed
    const finalOutputs = `
  Final Outputs: ${JSON.stringify(
      executionDetails.AutomationExecution?.Outputs,
      null,
      2
    )}
    useroutput: ${useroutputs}
    tags: ${tags}
  `;
    fs.appendFileSync("migration_logs.txt", finalOutputs + "\n\n", "utf8");
  }

  // Parse the ec2Tags string into an object
const ec2TagsObj = JSON.parse(ec2Tags);
console.log("tags in object form");
console.log(ec2TagsObj);
  const tagMappings = {
    "_CostCenter": "Cost Center",
    "_CostCenterDescription": "Cost Center Description",
    "_SupportTier": "Support Tier",
    "_SupportTierDescription": "Support Tier Description",
    "_ProvisioningEntity": "Provisioning Entity",
    "_BusinessArea": "Business Area",
    "_BusinessContact": "Business Contact",
    "_BusinessContactEmail": "Business Contact Email",
    "_BusinessSegment": "Business Segment",
    "_BusinessSegmentDescription": "Business Segment Description",
    "_TechnicalContact": "Technical Contact",
    "_TechnicalContactEmail": "Technical Contact Email",
    "_Environment": "Environment",
    "_NetworkLocation": "Network Location",
    "_FunctionalArea": "Functional Area",
    "_ProvisioningEngineer": "Provisioning Engineer",
    "_BackupPlan": "Backup Plan"
  };
  
  // Create formatted tag string
  let tagDetails = "";
  for (const [key, label] of Object.entries(tagMappings)) {
    if (ec2TagsObj[key]) {
      tagDetails += `<p><strong>${label}:</strong> ${ec2TagsObj[key]}</p>`;
    }
  }
 console.log(tagDetails);  
   
  // Run the logging function
 // await logStepOutputs();

  console.log("before mail");
 
    // const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} = req.body;
      const endTime =new Date();
      sendUpdate('Execution Successfull Sending Email');
      console.log('Execution Successfull Sending Email');
     
      try{
        const transporter = nodemailer.createTransport({
          host: 'relay.assaabloy.net',
          port: 25,
          secure: false,
          auth: {
            user: '<EMAIL>',
            pass: '',
          },
        });
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `<EMAIL>`,
        cc: `<EMAIL>`,
        subject: `${documentName} Notification - ${instanceId}`,
        html: `<p>Hi,</p>  
 
<p>We are pleased to inform you that the EC2 instance Migration operation has been successfully completed. Below are the details of the operation:</p>  
 
<p><strong>Status:</strong>${status}</p>  
<p><strong>Operation:</strong> ${documentName}</p>  
<p><strong>Instance Name:</strong> ${instancename}</p>  
<p><strong>Account ID:</strong> ${accountId}</p>  
<p><strong>Execution Date:</strong> ${startTime}</p>  
<p><strong>Instance ID:</strong> ${instanceId}</p>  
<p><strong>Region:</strong> ${region}</p>  
<p><strong>Business Contact:</strong>${businesscontact}</p>  
<p><strong>Service Initialization Engineer:</strong> ${email}</p>  
<p><strong>AWS Account:</strong>${accountname}</p> 
<p><strong>AMI ID With KMS:</strong>${copyImageId} </p>
<p><strong>AMI ID:</strong>${createAmiId} </p>
 
<p><strong>Security Groups:</strong></p>  
<p><strong>Subnet IDs</strong> </p>  
 
${tagDetails}

<p>You can access the AWS Portal here:  
<a href="https://hidglobal.awsapps.com/" style="color: blue; text-decoration: none;">Click here</a>.</p>  
<p>Thanks,<br>GDIAS Team</p>
 
<p>***** This message is auto-generated by EIT Cloud Team. For assistance, please reach out to  
<a href="mailto:<EMAIL>" style="color: blue; text-decoration: none;"><EMAIL></a> *****</em></p>
 
    `
   
  ,
});
fs.appendFileSync("email-umanage.txt", "email sent \n", "utf8");
}catch(err){
  fs.appendFileSync("migration-error.txt", `${err} \n `, "utf8");
  const transporter = nodemailer.createTransport({
    host: 'relay.assaabloy.net',
    port: 25,
    secure: false,
    auth: {
      user: '<EMAIL>',
      pass: '',
    },
  });

  const mail = await transporter.sendMail({
    from: '<EMAIL>',
    to: `<EMAIL>`,
    cc: `<EMAIL>`,
    subject: `${documentName} Notification - ${instanceId}`,
    html: `<p>Hi,</p>  

<p>We are pleased to inform you that the EC2 instance Migration operation has been Not-Successfully completed. Below are the details of the operation:</p>  

${err}
`

,
});
}
console.log('Execution Successfull  Email sent');
      sendUpdate('Successfull Executed Storing Logs');
      await new Promise(resolve => setTimeout(resolve, 10000));
     //
      
     //await storeDataInS3(ticketNumber, executionID,instancename,accountId, documentName, startTime,endTime,status, instanceId,businesscontact,email,accountname,servicenownumber);
      AWS.config.update({ credentials: originalCredentials });
      //sendUpdate('Successfull');
      res.end('Successfull');
     

    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
 
      sendUpdate(`Error:`+ err);
      const transporter = nodemailer.createTransport({
        host: 'relay.assaabloy.net',
        port: 25,
        secure: false,
        auth: {
          user: '<EMAIL>',
          pass: '',
        },
      });
 
      const mail = await transporter.sendMail({
        from: '<EMAIL>',
        to: `<EMAIL>`,
        cc: `<EMAIL>`,
        subject: `${documentName} Notification - ${instanceId}`,
        html: `<p>Error in migration,
        ${err}
        </p>  
 
 
    `
   
  ,
});
      res.end(`Error: ${err}`);
    }
  };
  
 
 


  // Route to stop an EC2 instance
  router.post('/CrossAccountMigration', (req, res) => handleSSMRequest(req, res, 'Account_migration'));
 
 
  return router;
};
 