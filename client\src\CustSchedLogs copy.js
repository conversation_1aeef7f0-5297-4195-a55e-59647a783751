import React, { useState, useEffect } from 'react';
import Select from 'react-select';
import axios from 'axios';
import './scheduler.css';
import { DataGrid } from '@mui/x-data-grid';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import DeleteOutlineSharpIcon from '@mui/icons-material/DeleteOutlineSharp';
import CircularProgress from '@mui/material/CircularProgress';
import Loading from './assets/Rocket.gif';
import { IoIosClose } from "react-icons/io";
import Navbar from './Navbar';
import { Link } from 'react-router-dom'
const Scheduler = () => {
    const [logs, setLogs] = useState([]);
     const [user, setUser] = useState( {
        email: '<EMAIL>',
        displayName: 'Guest',
        firstName: 'Guest'
      });
    const [mySchedules, setMySchedules] = useState([]);
    const [myScheduleList, setMyScheduleList] = useState([]);
    const [removeMessage, setremoveMessage] = useState('');
    const [removeError, setremoveError] = useState('');
    const [isProcessing, setIsProcessing] = useState(false);
    const [alertMessage, setAlertMessage] = useState(''); 
    const [message, setMessage] = useState('');
    const [messagestatus, setMessagestatus] = useState(false);
    const [accountId, setAccountId] = useState([]);
    useEffect(() => {
        async function checkAuth() {
          try {
            const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
            setUser(response.data.user);
          } catch (error) {
            // Set user to null in case of an error
          }
        }
        checkAuth();
      }, []);
      useEffect(() => {
        axios.get('https://umanage.dev.hidglobal.com/api/user')
          .then(response => {
            const fetchedData = response.data;
             //console.log('Fetched user data:', fetchedData);
           // console.log(user);
            const userEntry = fetchedData.find(entry => entry.user === user.email);
             //console.log('User entry:', userEntry);
      
            if (userEntry) {
              const accountIds = userEntry.accounts.split(',').map(account => account.trim());
              // console.log('Parsed account IDs:', accountIds);
              setAccountId(accountIds);
            } else {
              setAccountId([]);
            }
            
          })
          .catch(error => {
            // console.error('Error fetching user accounts:', error);
          });
          axios.get('https://umanage.dev.hidglobal.com/api/user')
          .then(response => {const fetchedData = response.data;
                const filteredSchedules = fetchedData.filter(schedule =>
                  accountId.includes(String(schedule.accountId))
                );
                fetchedData.forEach(schedule => {
                  console.log('Schedule Account ID:', schedule.accountId);
                  console.log('Account ID Array:', accountId);
                  console.log('Match Found:', accountId.includes(schedule.accountId));
                });
                setMySchedules(fetchedData);
                
              }) 
              .catch(error => {
                // console.error('Error fetching user accounts:', error);
              });
            
            
      }, [user]);
    console.log('Account IDs:', accountId); // Log the account IDs to verify 
      
    
    useEffect(() => {
      
    
      async function fetchSchedules() {
        try {
          const response = await axios.get('https://umanage.dev.hidglobal.com/api/s3');
          const fetchedData = response.data;
    
          // Filter schedules where accountId matches
          const filteredSchedules = fetchedData.filter(schedule =>
            accountId.includes(String(schedule.accountId))
          );
    
          setMySchedules(filteredSchedules);
        } catch (error) {
          console.error('Error fetching schedules:', error);
        }
      }
    
      // Fetch schedules initially
      fetchSchedules();
    
  
      
    }, [ accountId]);
     
    
    
      useEffect(() => {
        // Check if "************" exists in the accountId array
        const accountExists = accountId.includes("************");
      
        // Log true or false
        console.log('Does account ID "************" exist?', accountExists);
      }, [accountId]);
      console.log('My Schedules:', mySchedules);
      
    
    
      const mySchedules1 = [
        {
          AccountName: "Test Account",
          InstanceId: "i-********90abcdef",
          InstanceName: "TestInstance1",
          InstanceType: "t2.micro",
          IPaddress: "***********",
          state: "running",
          Region: "us-east-1",
          platform: "",
          ImageId: "ami-0bbddd0354b96aced",
          BusinessArea: "Engineering",
          BusinessSegment: "1010",
          BusinessSegmentDescription: "Engineering Segment",
          CostCenter: "4810",
          CostCenterDescription: "Engineering Cost Center",
          FunctionalArea: "Development",
          BackupPlan: "BRONZE",
          Environment: "PRODUCTION",
          ProvisioningEngineer: "John Doe",
          ProvisioningEntity: "Test Entity",
          ProvisioningJustification: "REQ123456",
          SupportTier: "TIER1",
          BusinessContact: "Jane Smith",
          BusinessContactEmail: "<EMAIL>",
          TechnicalContact: "Mike Johnson",
          TechnicalContactEmail: "<EMAIL>",
          VPCid: "vpc-********",
          accountId: "********9012",
        },
        {
          AccountName: "Demo Account",
          InstanceId: "i-09********fedcba",
          InstanceName: "DemoInstance2",
          InstanceType: "t3.small",
          IPaddress: "***********",
          state: "stopped",
          Region: "eu-central-1",
          platform: "",
          ImageId: "ami-0bbddd0354b96aced",
          BusinessArea: "Sales",
          BusinessSegment: "2020",
          BusinessSegmentDescription: "Sales Segment",
          CostCenter: "5820",
          CostCenterDescription: "Sales Cost Center",
          FunctionalArea: "Marketing",
          BackupPlan: "SILVER",
          Environment: "NON-PRODUCTION",
          ProvisioningEngineer: "Alice Brown",
          ProvisioningEntity: "Demo Entity",
          ProvisioningJustification: "REQ654321",
          SupportTier: "TIER2",
          BusinessContact: "Bob Williams",
          BusinessContactEmail: "<EMAIL>",
          TechnicalContact: "Sarah Davis",
          TechnicalContactEmail: "<EMAIL>",
          VPCid: "vpc-********",
          accountId: "9********098",
        },
      ];

    const columns = [
      { field: 'AccountName', headerName: 'Account Name', width: 200 },
      { field: 'InstanceId', headerName: 'Instance ID', width: 200 },
      { field: 'InstanceName', headerName: 'Instance Name', width: 200 },
      { field: 'InstanceType', headerName: 'Instance Type', width: 150 },
      { field: 'IPaddress', headerName: 'IP Address', width: 150 },
      { field: 'state', headerName: 'State', width: 150 },
      { field: 'Region', headerName: 'Region', width: 150 },
      {
        field: 'platform',
        headerName: 'Platform',
        width: 150,
        renderCell: (params) => {
          return params.row.platform === 'windows' ? 'windows' : 'linux';
        },
      },
      { field: 'ImageId', headerName: 'Image ID', width: 200 },
      { field: 'BusinessArea', headerName: 'Business Area', width: 150 },
      { field: 'BusinessSegment', headerName: 'Business Segment', width: 150 },
      { field: 'BusinessSegmentDescription', headerName: 'Segment Description', width: 200 },
      { field: 'CostCenter', headerName: 'Cost Center', width: 150 },
      { field: 'CostCenterDescription', headerName: 'Cost Center Description', width: 200 },
      { field: 'FunctionalArea', headerName: 'Functional Area', width: 200 },
      { field: 'BackupPlan', headerName: 'Backup Plan', width: 150 },
      { field: 'Environment', headerName: 'Environment', width: 150 },
      { field: 'ProvisioningEngineer', headerName: 'Provisioning Engineer', width: 200 },
      { field: 'ProvisioningEntity', headerName: 'Provisioning Entity', width: 200 },
      { field: 'ProvisioningJustification', headerName: 'Justification', width: 200 },
      { field: 'SupportTier', headerName: 'Support Tier', width: 150 },
      { field: 'BusinessContact', headerName: 'Business Contact', width: 200 },
      { field: 'BusinessContactEmail', headerName: 'Contact Email', width: 250 },
      { field: 'TechnicalContact', headerName: 'Technical Contact', width: 200 },
      { field: 'TechnicalContactEmail', headerName: 'Technical Contact Email', width: 250 },
      { field: 'VPCid', headerName: 'VPC ID', width: 200 },
      { field: 'accountId', headerName: 'Account ID', width: 200 },
    ];
    
    // Add an `id` field to each row (required by DataGrid)
    const rows = mySchedules.map((row, index) => ({ id: index, ...row }));
      return(
        <div>
           <Navbar />
          
    {(message||alertMessage) &&<div  className="notification-container">
            {alertMessage && !message && (
          <div className="alert-card">
            <div className="alert-header">
              <div className="loading-icon">
                <img src={Loading} alt="Loading" className="loading-gif" />
              </div>
              <p className="alert-message">{alertMessage}</p>
              <button className="close-button" onClick={() => setAlertMessage(null)}><IoIosClose /></button>
            </div>
          </div>
        )}
    
          {/* Status Message Card */}
          {message && (
            <div className={`status-card ${messagestatus ? 'success' : 'error'}`}>
              <div className={`status-icon ${messagestatus ? 'pop-animation' : 'shake-animation'}`}>
                {messagestatus ? <FaCheckCircle size={24} /> : <FaTimesCircle size={24} />}
              </div>
             <p>{message}</p>
              <button className="close-button"onClick={() => {  setMessage(null); setAlertMessage(null);}}><IoIosClose /></button>
              
           
            </div>
          )}
          </div>}
          <div style={{ height: 700, width: '100%' }}>
        <DataGrid
          rows={rows}
          columns={columns}
          pageSize={7}
          rowsPerPageOptions={[5, 10, 20]}
          sx={{
            '& .MuiDataGrid-columnHeaders': {
              backgroundColor: '#f5f5f5', // Optional: Add a background color for the header
              fontSize: '1.0rem', // Make the font size larger
              fontWeight: 'bold', // Make the text bold
               // Optional: Make the text uppercase
            },
            '& .MuiDataGrid-columnHeaderTitle': {
              fontWeight: 'bold', // Ensure the column header title is bold
            },
          }}
        />
      </div>
                     
                    </div>
      );

};

export default Scheduler;