// import React, { useState, useEffect } from 'react';
// import Select from 'react-select';
// import TimePicker from 'react-time-picker'; // Install using `npm install react-time-picker`
// import './scheduler.css';

// const CustomScheduler = () => {
//   const [awsAccount, setAwsAccount] = useState('');
//   const [region, setRegion] = useState('');
//   const [instances, setInstances] = useState([]);
//   const [instanceIds, setInstanceIds] = useState([]);
//   const [time, setTime] = useState(''); // State for time selection
//   const [month, setMonth] = useState(''); // State for month selection
//   const [date, setDate] = useState(''); // State for date selection
//   const [dayOfWeek, setDayOfWeek] = useState(''); // State for day of the week selection
//   const [cronJob, setCronJob] = useState(''); // State for generated cron job
//   const [accounts, setAccounts] = useState([]);
//   const [regions, setRegions] = useState([]);
//   const [data, setData] = useState([]);
//   const [message, setMessage] = useState('');
//   const [error, setError] = useState('');

//   // Generate cron expression whenever time, month, date, or dayOfWeek changes
//   useEffect(() => {
//     const cronTime = time ? `${time.split(':')[1]} ${time.split(':')[0]}` : '*';
//     const cronMonth = month || '*';
//     const cronDate = date || (dayOfWeek ? '*' : '*');
//     const cronDayOfWeek = dayOfWeek || (date ? '*' : '*');
//     setCronJob(`${cronTime} ${cronDate} ${cronMonth} ${cronDayOfWeek}`);
//   }, [time, month, date, dayOfWeek]);

//   // Fetch user accounts and instance details (similar to Scheduler.js)
//   useEffect(() => {
//     // Fetch logic here...
//   }, []);

//   return (
//     <div className="custom-scheduler-container">
//       <h1>Custom Scheduler</h1>
//       <form>
//         <label>AWS Account</label>
//         <Select
//           className="re-select"
//           value={accounts.find(account => account.CreateAccountId === awsAccount) || null}
//           onChange={(selectedOption) => setAwsAccount(selectedOption.value)}
//           options={accounts.map(account => ({
//             value: account.CreateAccountId,
//             label: account.CreateAccountName,
//           }))}
//           isSearchable={true}
//           placeholder="Select an AWS Account"
//         />

//         <label>Region</label>
//         <select
//           className="form-control"
//           value={region}
//           onChange={(e) => setRegion(e.target.value)}
//           disabled={!awsAccount}
//         >
//           <option value="">Select Region</option>
//           {regions.map(region => (
//             <option key={region} value={region}>{region}</option>
//           ))}
//         </select>

//         <label>Instance Name</label>
//         <Select
//           isMulti
//           className="re-select"
//           value={instanceIds}
//           onChange={(selectedOptions) => setInstanceIds(selectedOptions)}
//           options={instances}
//           isDisabled={!region}
//         />

//         <label>Time</label>
//         <TimePicker
//           onChange={setTime}
//           value={time}
          
//           format="HH:mm"
//         />
        
//         <label>Month</label>
//         <select
//           className="form-control"
//           value={month}
//           onChange={(e) => setMonth(e.target.value)}
//         >
//           <option value="">Every Month</option>
//           {['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'].map((month, index) => (
//             <option key={index} value={index + 1}>{month}</option>
//           ))}
//         </select>

//         <label>Date</label>
//         <select
//           className="form-control"
//           value={date}
//           onChange={(e) => {
//             setDate(e.target.value);
//             if (e.target.value) setDayOfWeek(''); // Disable DOW if date is selected
//           }}
//         >
//           <option value="">Every Date</option>
//           {Array.from({ length: 31 }, (_, i) => i + 1).map(date => (
//             <option key={date} value={date}>{date}</option>
//           ))}
//         </select>

//         <label>Day of the Week</label>
//         <select
//           className="form-control"
//           value={dayOfWeek}
//           onChange={(e) => {
//             setDayOfWeek(e.target.value);
//             if (e.target.value) setDate(''); // Disable date if DOW is selected
//           }}
//           disabled={!!date} // Disable if a date is selected
//         >
//           <option value="">Every Day</option>
//           {['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'].map((day, index) => (
//             <option key={index} value={index}>{day}</option>
//           ))}
//         </select>

//         <p>Generated Cron Job: <strong>{cronJob}</strong></p>

//         {message && <p className="sch-message">{message}</p>}
//         {error && <p className="sch-alert">{error}</p>}

//         <button type="submit" disabled={!awsAccount || !region || instanceIds.length === 0 || !cronJob}>
//           Add Custom Schedule
//         </button>
//       </form>
//     </div>
//   );
// };

// export default CustomScheduler;