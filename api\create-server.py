import requests
import json
 
# ------------------------
# ⚙️ Configuration
# ------------------------
username = "<EMAIL>"
password = "x5EMe7a4W3Qfb6FDo82N"
BASE_URL = "https://adx-api.assaabloy.net/restApi"
 
# Disable SSL warnings (Remove this in production)
requests.packages.urllib3.disable_warnings()
 
 
# ------------------------
# 🟢 Step 1: Create Session
# ------------------------
def create_session():
    url = f"{BASE_URL}/api/authSessions/create"
    payload = {
        "username": username,
        "password": password
    }
 
    response = requests.post(url, json=payload, headers={"Content-Type": "application/json"}, verify=False)
 
    if response.status_code == 200:
        session_id = response.json().get("sessionId")
        print(f"✅ Session created successfully. Session ID: {session_id}")
        return session_id
    else:
        print(f"❌ Error creating session: {response.status_code} - {response.text}")
        exit(1)
 
 
# ------------------------
# 🔐 Step 2: Get Auth Token
# ------------------------
def get_auth_token(session_id):
    url = f"{BASE_URL}/api/auth"
    payload = {
        "sessionId": session_id,
        "type": 0
    }
 
    response = requests.post(url, json=payload, headers={"Content-Type": "application/json"}, verify=False)
 
    if response.status_code == 200:
        token = response.json().get("token")
        print(f"✅ Authentication token obtained.")
        return token
    else:
        print(f"❌ Error obtaining authentication token: {response.status_code} - {response.text}")
        exit(1)
 
 
# ------------------------
# 🖥️ Step 3: Create Server
# ------------------------
def create_server(token):
    url = f"{BASE_URL}/api/directoryObjects/executeCustomCommand"
    headers = {
        "Adm-Authorization": token,
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
 
    payload = {
        "directoryObject": "DC=ad,DC=global",
        "customCommandId": "c39819b3-4a52-4476-9950-3e2e6c8bfdd5",
        
        "parameters": [
            {"type": "Text", "name": "param-Provider", "value": "A"},
            {"type": "Text", "name": "param-Region", "value": "F"},
            {"type": "Text", "name": "param-Locale", "value": "C"},
            {"type": "Text", "name": "param-SiteDesignator", "value": "1"},
            {"type": "Text", "name": "param-Zone", "value": "1"},
            {"type": "Text", "name": "param-Environment", "value": "SBX"},
            {"type": "Text", "name": "param-OS", "value": "MS"},
            {"type": "Text", "name": "param-Application", "value": "TES0"},
            {"type": "Text", "name": "param-PatchingGroup", "value": "DEVTEST"},
            {"type": "Text", "name": "param-Description", "value": "This is a test"},
            {
                "type": "ADObject",
                "name": "param-SiteOU",
                "value": [
                    {
                        "referenceType": 1,
                        "template": "OU=CNSUH,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global"
                    }
                ]
            },
            {
                "type": "ADObject",
                "name": "param-ManagedBy",
                "value": [
                    {
                        "referenceType": 1,
                        "template": "CN=Srinivasan\, Prasana,OU=INCHE,OU=UsersInternal,OU=Users,OU=HID,OU=SSC,DC=ad,DC=global"
                    }
                ]
            }
        ]
    }
 
    
    request = requests.post(url, json=payload, headers=headers, verify=False)
    response = json.loads(request.content)
    print(f"Response {response}")
    #     print(f"Response Status Code: {request.status_code}")  # ✅ Print status code
    #     print(f"Response Text: {request.text}")  # ✅ Print request content
 
    #     if request.status_code == 200:
    #         data = request.json()
    #         messages = data.get("innerMessages", {}).get("innerMessages", {}).get("innerMessages", [])
    #         generated_name = [
    #             msg["text"].split("Generated name = ")[1]
    #             for msg in messages if "Generated name =" in msg["text"]
    #         ]
 
    #         if generated_name:
    #             print(f"✅ Server created successfully. Generated Name: {generated_name[0]}")
    #         else:
    #             print("✅ Server created, but no name generated.")
    #     else:
    #         print(f"❌ Error executing custom command: {request.status_code} - {request.text}")
 
    # except requests.exceptions.RequestException as e:
    #     print(f"❌ Error executing custom command: {str(e)}")
        
 
# ------------------------
# 🚀 Main Function
# ------------------------
def main():
    try:
        session_id = create_session()
        token = get_auth_token(session_id)
        create_server(token)
    except Exception as e:
        print(f"❌ Workflow failed: {str(e)}")
 
 
if __name__ == "__main__":
    main()