import React, { useState, useEffect } from 'react';
import axios from 'axios';
import Select from 'react-select';
import LoadingPage from './Loding';
import { IoIosArrowDroprightCircle } from "react-icons/io";
import './Migration.css'
const Migration = () => {
  const [instanceNames, setInstanceNames] = useState(['']);
  //const [isLoading, setIsLoading] = React.useState(true);
  const [allservernames, setAllServerNames] = useState(['']);
  const [Sitecodeoptions, setSitecodeoptions] = useState(['']);
  const [AWSaccoptions, setAWSaccoptions] = useState(['']);
  const [Contactoptions, setContactoptions] = useState(['']);// Array of instance names
  const [formData, setFormData] = useState({}); // Stores the selected/filtered data
  const [allData, setAllData] = useState([]); // Stores all fetched data
  const [isLoading, setIsLoading] = useState(true); // Loading state for fetching data
  const [isSubmitEnabled, setIsSubmitEnabled] = useState(false); // Controls submit button visibility
  const [selectedServer, setSelectedServer] = useState('');
  const [dnrcount, setdnrcount] = useState('');
  const [plannedcount, setplannedcount] = useState('');
  const [preplannedcount, setpreplannedcount] = useState('');
  const [keeponpremcount, setkeeponpremcount] = useState('');
  const [selectedInstance, setSelectedInstance] = useState(null);
  const [hoverPosition, setHoverPosition] = useState({ top: 0, left: 0 });
  const [isToggled, setIsToggled] = useState(false); // Toggle state
  const [selectedRows, setSelectedRows] = useState([]);
  const [namelength, setnamelength] = useState();
  const [multiservernames, setMultiServerNames] = useState(['']);
  const [dnrCount, setDnrCount] = useState(0);
const [plannedCount, setPlannedCount] = useState(0);
const [tbdCount, settbdCount] = useState(0);
const [preplannedCount, setpreplannedCount] = useState(0);
const [keeponpremCount, setkeeponpremCount] = useState(0);

  const handleToggle = () => {
    setIsToggled((prev) => !prev); // Toggle between true and false
  };
 
  const [tagsData, setTagsData] = useState([]);
  const [optionData, setOptionData] = useState([]);
  const [tempservers, settempservers] = useState(['']);
  const [ SelectedSiteCode,setSelectedSiteCode] = useState('');
  const [ SelectedAWSacc,setSelectedAWSacc] = useState('');
  const [ SelectedContact,setSelectedContact] = useState('');
  const [selectedMD, setSelectedMD] = useState(''); // State for selected migration disposition
  useEffect(() => {
    axios
      .get('https://umanage.dev.hidglobal.com/api/migration/data') // Adjust the URL to your backend endpoint
      .then((response) => {
        // Extract ServerName from the response
        const scoptions = response.data.map((item) => item.SiteCode);
        const accoptions = response.data.map((item) => item.TargetAWSAccount);
        const contactoptions= response.data.map((item) => item._BusinessContactEmail);
        const data = response.data.filter(
          (item) =>
            ![
             
              'Migrated',
              'Terminated',
              'Do Not Migrate',
             
            ].includes(item.MigrationDisposition)
        );
        const serveroptions= response.data.map((item) =>item.ServerName);
        settempservers(data);
        setSitecodeoptions(scoptions);
        setContactoptions(contactoptions);
        setAWSaccoptions(accoptions);
        setIsLoading(false);
       
      })
     
      .catch((error) => {
        console.error('Error fetching server names:', error);
      });
  }, []);
 
  useEffect(() => {
    if (SelectedSiteCode) {
      const filteredInstances = allservernames.filter(
        (instance) => instance['SiteCode'] === SelectedSiteCode
      );
  
      // Count instances based on migration disposition
      const dnrInstances = filteredInstances.filter(
        (instance) => instance['MigrationDisposition'] === 'DNR'
      ).length;
  
      const plannedInstances = filteredInstances.filter(
        (instance) => instance['MigrationDisposition'] === 'Planned'
      ).length;
      const tbdInstances = filteredInstances.filter(
        (instance) => instance['MigrationDisposition'] === 'TBD'
      ).length;
      const preplannedInstances = filteredInstances.filter(
        (instance) => instance['MigrationDisposition'] === 'Pre-Planned'
      ).length;
      const keeponpremInstances = filteredInstances.filter(
        (instance) => instance['MigrationDisposition'] === 'Keep on Prem'
      ).length;
      settbdCount(tbdInstances);
      setDnrCount(dnrInstances);
      setPlannedCount(plannedInstances);
      setpreplannedCount(preplannedInstances);
      setkeeponpremCount(keeponpremInstances);
    } else {
      setDnrCount(0);
      setPlannedCount(0);
    }
  }, [SelectedSiteCode, allservernames]);
  useEffect(() => {
    // Filter server names based on selected dropdown values
    const filtered = tempservers.filter((server) => {
      return (
        (!SelectedSiteCode || server.SiteCode === SelectedSiteCode) &&
        (!SelectedAWSacc || server.TargetAWSAccount === SelectedAWSacc) &&
        (!SelectedContact || server._BusinessContactEmail === SelectedContact) &&
        (!selectedMD || server.MigrationDisposition === selectedMD) // Filter by migration disposition
      );
    });
 
    setAllServerNames(filtered);
  }, [SelectedSiteCode, SelectedAWSacc, SelectedContact, allservernames]);
 
  const handleServerNameChange = (selectedOptions) => {
    const selectedNames = selectedOptions ? selectedOptions.map((option) => option.value) : [];
 
    if (selectedNames.length > 0) {
      // Filter based on selected server names
      const filteredByNames = allservernames.filter((instance) => selectedNames.includes(instance.ServerName));
      setMultiServerNames(filteredByNames);
    }
     setnamelength(selectedNames.length);
  };
  console.log("Multiselect",multiservernames);
 
 
  useEffect(() => {
    axios
      .get('https://umanage.dev.hidglobal.com/api/migration/tages') // Adjust the URL to your backend endpoint
      .then((response) => {
      setAllData(response.data); // Extract ServerName from the response
        console.log("Alldata",allData);
        console.log(response.data);
      })
     
      .catch((error) => {
        console.error('Error fetching Tag data:', error);
      });
  }, []);
 
  // useEffect(() => {
  //   const fetchData = async () => {
  //     setIsLoading(true);
  //     try {
  //       const response = await axios.get('https://umanage.dev.hidglobal.com/api/migration/tages');
  //       setAllData(response.data);
  //       console.log("Alldata",allData); // Store fetched data
  //     } catch (error) {
  //       console.error('Error fetching data:', error);
  //       alert('Failed to fetch data from the backend.');
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };
 
  //   fetchData(); // Fetch data once when the component mounts
  // }, []); // Empty dependency array means it runs once after the component mounts
 
  // Handle IP address input blur event
  const handleAddInstanceName = () => {
    setInstanceNames([...instanceNames, '']);
  };
 
 
  const handleRemoveInstanceName = (index) => {
    const updatedNames = instanceNames.filter((_, i) => i !== index);
    setInstanceNames(updatedNames);
  };
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value, // Update the specific field based on the input's name
    }));
  };
  const [isHoverVisible, setHoverVisible] = useState(false);
   const handleRowClick = (instance, event) => {
    if (isHoverVisible) return;
    const rect = event.currentTarget.getBoundingClientRect();
    setHoverPosition({
      top: rect.top - 150, // Adjust position to show above the row
      left: rect.left,
    });
    setFormData({
        array:[instance['ServerName']],
        ServerName: instance['ServerName'],
        IPAddress: instance['IPAddress'],
        newipaddress: instance['newipaddress'],
        Country: instance['Country'],
        Location: instance['Location'],
        DependencyGroup: instance['DependencyGroup'],
        MigrationDisposition: instance['MigrationDisposition'], // Column G (index 6)
        UseCase: instance['UseCase'], // Column C (index 2)
        UseCaseDescription: instance['UseCaseDescription'], // Column M (index 12)
        TargetAWSAccount: instance['TargetAWSAccount'], // Column O (index 14)
        SiteCode: instance['SiteCode'],
        _CostCenter: instance['_CostCenter'], // Column P (index 15)
        _CostCenterDescription: instance['_CostCenterDescription'], // Column Q (index 16)
        _BusinessSegment: instance['_BusinessSegment'], // Column U (index 20)
        _BusinessSegmentDescription: instance['_BusinessSegmentDescription'], // Column S (index 18)
        DeprecatedOS: instance['DeprecatedOS'], // Column T (index 19)
        DomainJoined: instance['DomainJoined'], // Column W (index 22)
        PatchedtoDate: instance['PatchedtoDate'], // Column F (index 5)
        map_migrated: instance['map-migrated'],
        _SupportTier: instance['_SupportTier'],
        _SupportTierDescription: instance['_SupportTierDescription'],
        _BackupPlan: instance['_BackupPlan'], // Column AD (index 29)
        _BackupPlanDescription: instance['_BackupPlanDescription'], // Column AF (index 31)
        _BusinessContact: instance['_BusinessContact'],
        SecondBusinessContact: instance['SecondBusinessContact'],
        _BusinessContactEmail: instance['_BusinessContactEmail'], // Column AD (index 29)
        _TechnicalContact: instance['_TechnicalContact'],
        _TechnicalContactEmail: instance['_TechnicalContactEmail'], // Column AD (index 29)
        _ProvisioningJustification: instance['_ProvisioningJustification'],
        _NetworkLocation: instance['_NetworkLocation'],
        _BusinessArea: instance['_BusinessArea'],
        _FunctionalArea: instance['_FunctionalArea'],
      });
    console.log(instance);
    // const selectedInstances = selectedOptions.map((option) => option.value);
    setInstanceNames(instance);
    setHoverVisible(true);
    setSelectedInstance(instance);  // Set the selected instance to display in modal
  };  useEffect(() => {
    const handleClickOutside = (event) => {
      if (isHoverVisible && !event.target.closest(".hover-card")) {
        handleCloseHover();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
 
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isHoverVisible]);
  const handleCloseHover = () => {
    setHoverVisible(false); // Hide hover
    setInstanceNames(null);  
    setSelectedInstance(null);   // Clear data
  };
 
  const handleCloseModal = () => {
    setSelectedInstance(null);  // Close the modal
  };
 
 
  // Handle instance name input change
  const handleInstanceChange = (selectedOptions) => {
    const selectedInstances = selectedOptions.map((option) => option.value);
    setInstanceNames(selectedInstances); // Update the state with selected instances
  };
  // Send a POST request to the backend when the "Fetch" button is clicked
  const handleFetchData = async () => {
   console.log(instanceNames);
 
    setIsLoading(true);
    try {
      const response = await axios.get('https://umanage.dev.hidglobal.com/api/migration/data');
      //console.log(response);
      // Process the data after fetching
      const filteredData = response.data.find(
         (row) => row.ServerName === instanceNames[0]// && row.IPAddress === formData.IPAddress
      );
      console.log("Filter",filteredData);
 
      if (filteredData) {
        // Predefine the values based on the returned row
        setFormData({
          array:instanceNames,
          ServerName: instanceNames[0],
          IPAddress: filteredData['IPAddress'],
          newipaddress: filteredData['newipaddress'],
          Country: filteredData['Country'],
          Location: filteredData['Location'],
          DependencyGroup: filteredData['DependencyGroup'],
          MigrationDisposition: filteredData['MigrationDisposition'], // Column G (index 6)
          UseCase: filteredData['UseCase'], // Column C (index 2)
          UseCaseDescription: filteredData['UseCaseDescription'], // Column M (index 12)
          TargetAWSAccount: filteredData['TargetAWSAccount'], // Column O (index 14)
          SiteCode: filteredData['SiteCode'],
          _CostCenter: filteredData['_CostCenter'], // Column P (index 15)
          _CostCenterDescription: filteredData['_CostCenterDescription'], // Column Q (index 16)
          _BusinessSegment: filteredData['_BusinessSegment'], // Column U (index 20)
          _BusinessSegmentDescription: filteredData['_BusinessSegmentDescription'], // Column S (index 18)
          DeprecatedOS: filteredData['DeprecatedOS'], // Column T (index 19)
          DomainJoined: filteredData['DomainJoined'], // Column W (index 22)
          PatchedtoDate: filteredData['PatchedtoDate'], // Column F (index 5)
          map_migrated: filteredData['map-migrated'],
          _SupportTier: filteredData['_SupportTier'],
          _SupportTierDescription: filteredData['_SupportTierDescription'],
          _BackupPlan: filteredData['_BackupPlan'], // Column AD (index 29)
          _BackupPlanDescription: filteredData['_BackupPlanDescription'], // Column AF (index 31)
          _BusinessContact: filteredData['_BusinessContact'],
          SecondBusinessContact: filteredData['SecondBusinessContact'],
          _BusinessContactEmail: filteredData['_BusinessContactEmail'], // Column AD (index 29)
          _TechnicalContact: filteredData['_TechnicalContact'],
          _TechnicalContactEmail: filteredData['_TechnicalContactEmail'], // Column AD (index 29)
          _ProvisioningJustification: filteredData['_ProvisioningJustification'],
          _NetworkLocation: filteredData['_NetworkLocation'],
          _BusinessArea: filteredData['_BusinessArea'],
          _FunctionalArea: filteredData['_FunctionalArea'],
        });
 
        setIsSubmitEnabled(true);
        alert('Matching data found!');
      } else {
        alert('No matching data found.');
        setFormData({});
        setIsSubmitEnabled(false);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      alert('Failed to fetch data from the backend.');
    } finally {
      //setIsLoading(false);
    }
  };
  useEffect(() => {
    const fetchData = async () => {
      //setIsLoading(true);
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/migration/tages');
        const allFetchedData = response.data;
 
        // Transform data into optionData format
        const tagNames = [
          '_InstanceSource',
          'MigrationStatus',
          'TargetAWSAccount',
          '_SupportTier',
          '_SupportTierDescription',
          'UseCase',
          'UseCaseDescription',
          '_BackupPlan',
          '_BackupPlanDescription',
          '_CostCenter',
          '_CostCenterDescription',
          '_BusinessArea',
          '_BusinessSegment',
          '_BusinessSegmentDescription',
          '_FunctionalArea',
        ];
 
        const optionData = tagNames.map(tag => ({
          [tag]: [...new Set(allFetchedData.map(item => item[tag]).filter(Boolean))], // Unique and non-empty values
        }));
 
        setOptionData(optionData); // Store transformed data
        //console.log('Option Data:', optionData);
      } catch (error) {
        console.error('Error fetching data:', error);
        alert('Failed to fetch data from the backend.');
      } finally {
        //setIsLoading(false);
      }
    };
 
    fetchData();
  }, []);
 // console.log('Option Data:', optionData);
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch('https://umanage.dev.hidglobal.com/api/migration/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      console.log(formData);
      if (response.status==200) {
        const result = await response.json();
        alert('Data submitted successfully!');
        console.log('Server response:', result);
      } else {
        console.error('Failed to submit data:', response.status);
        alert('Failed to submit data. Please try again.');
      }
    } catch (error) {
      console.error('Error during submission:', error);
      alert('An error occurred while submitting data.');
    }
  };
  const handleSupportTierChange = (event) => {
    const selectedTier = event.target.value; // Get the selected support tier
    const matchingRow = allData.find((row) => row._SupportTier === selectedTier); // Find the matching row
    const description = matchingRow?._SupportTierDescription || ''; // Get the description if available
 
    // Update formData with the selected tier and its description
    setFormData((prevData) => ({
      ...prevData,
      _SupportTier: selectedTier,
      _SupportTierDescription: description, // Set the corresponding description
    }));
  };
  const handleUseCaseChange = (event) => {
    const selectedTier = event.target.value; // Get the selected support tier
    const matchingRow = allData.find((row) => row.UseCase === selectedTier); // Find the matching row
    const description = matchingRow?.UseCaseDescription || ''; // Get the description if available
 
    // Update formData with the selected tier and its description
    setFormData((prevData) => ({
      ...prevData,
      UseCase: selectedTier,
      UseCaseDescription: description, // Set the corresponding description
    }));
  };
  const [selectedInstances, setSelectedInstances] = useState([]); // Separate array for selected instances
 
 
  // Handle radio button change
  const handlesingleRowClick = (instance) => {
    const isInstanceSelected = selectedInstances.includes(instance['ServerName']);
console.log(instance['serverName']);
    if (isInstanceSelected) {
      // Remove from the array if already selected
      setSelectedInstances((prev) => prev.filter((serverName) => serverName !== instance['ServerName']));
    } else {
      // Add to the array if not already selected
      setSelectedInstances((prev) => [...prev, instance['ServerName']]);
    }
  };
  const handlemultiSubmit = () => {
    // When submit is clicked, set the selected instances to submittedData
    if (isHoverVisible) return;
    const rect = {
      top: 100,     // Distance from the top of the viewport in pixels
      left: 200,    // Distance from the left of the viewport in pixels
      width: 300,   // Width of the element in pixels
      height: 50,   // Height of the element in pixels
    };
 
   
 
    setHoverPosition({
      top: rect.top - 150, // Adjust position to show above the row
      left: rect.left,
    });
    const instance=allservernames.find(
      (instance) => instance.ServerName === selectedInstances[0]
    );
    setFormData({
        array:selectedInstances,
        ServerName: instance['ServerName'],
        IPAddress: instance['IPAddress'],
        newipaddress: instance['newipaddress'],
        Country: instance['Country'],
        Location: instance['Location'],
        DependencyGroup: instance['DependencyGroup'],
        MigrationDisposition: instance['MigrationDisposition'], // Column G (index 6)
        UseCase: instance['UseCase'], // Column C (index 2)
        UseCaseDescription: instance['UseCaseDescription'], // Column M (index 12)
        TargetAWSAccount: instance['TargetAWSAccount'], // Column O (index 14)
        SiteCode: instance['SiteCode'],
        _CostCenter: instance['_CostCenter'], // Column P (index 15)
        _CostCenterDescription: instance['_CostCenterDescription'], // Column Q (index 16)
        _BusinessSegment: instance['_BusinessSegment'], // Column U (index 20)
        _BusinessSegmentDescription: instance['_BusinessSegmentDescription'], // Column S (index 18)
        DeprecatedOS: instance['DeprecatedOS'], // Column T (index 19)
        DomainJoined: instance['DomainJoined'], // Column W (index 22)
        PatchedtoDate: instance['PatchedtoDate'], // Column F (index 5)
        map_migrated: instance['map-migrated'],
        _SupportTier: instance['_SupportTier'],
        _SupportTierDescription: instance['_SupportTierDescription'],
        _BackupPlan: instance['_BackupPlan'], // Column AD (index 29)
        _BackupPlanDescription: instance['_BackupPlanDescription'], // Column AF (index 31)
        _BusinessContact: instance['_BusinessContact'],
        SecondBusinessContact: instance['SecondBusinessContact'],
        _BusinessContactEmail: instance['_BusinessContactEmail'], // Column AD (index 29)
        _TechnicalContact: instance['_TechnicalContact'],
        _TechnicalContactEmail: instance['_TechnicalContactEmail'], // Column AD (index 29)
        _ProvisioningJustification: instance['_ProvisioningJustification'],
        _NetworkLocation: instance['_NetworkLocation'],
        _BusinessArea: instance['_BusinessArea'],
        _FunctionalArea: instance['_FunctionalArea'],
      });
    console.log(instance);
    // const selectedInstances = selectedOptions.map((option) => option.value);
    setInstanceNames(instance);
    setHoverVisible(true);
    setSelectedInstance(instance);
    console.error(selectedInstances);
  };
 
  const handleCostCenterChange = (event) => {
    const selectedTier = event.target.value; // Get the selected support tier
    const matchingRow = allData.find((row) => row._CostCenter.toString() === selectedTier); // Find the matching row
    const description = matchingRow?._CostCenterDescription || ''; // Get the description if available
 
    // Update formData with the selected tier and its description
    setFormData((prevData) => ({
      ...prevData,
      _CostCenter: selectedTier,
      _CostCenterDescription: description, // Set the corresponding description
    }));
  };
 
  const handleBusinessSegmentChange = (event) => {
    const selectedTier = event.target.value; // Get the selected support tier
    const matchingRow = allData.find((row) => row._BusinessSegment.toString() === selectedTier); // Find the matching row
    const description = matchingRow?._BusinessSegmentDescription || ''; // Get the description if available
 
    // Update formData with the selected tier and its description
    setFormData((prevData) => ({
      ...prevData,
      _BusinessSegment: selectedTier,
      _BusinessSegmentDescription: description, // Set the corresponding description
    }));
  };
    const handleBackupPlanChange = (e) => {
      const value = e.target.value;
     
   
      let label = '';
      if (value === 'GOLD') {
          label = 'Daily backups';
      } else if (value === 'SILVER') {
          label = 'Weekly backups';
      } else if (value === 'BRONZE') {
          label = 'Monthly backups';
      } else if (value === 'PLATINUM') {
        label = 'Multiple backups per day';
      } else if (value === 'N/A') {
        label = 'No Backups';
    }
      // console.log(backupPlanLabel);
      setFormData((prevData) => ({
        ...prevData,
        _BackupPlan: value,
        _BackupPlanDescription: label, // Set the corresponding description
      }));
  };
   
 
    // Update formData with the selected tier and its description
 
    const siteCodeOptions =  [...new Set(Sitecodeoptions)].map((siteCode) => ({
      value: siteCode,
      label: siteCode,
    }));
    const AWSAccoptions =  [...new Set(AWSaccoptions)].map((TargetAWSAccount) => ({
      value: TargetAWSAccount,
      label: TargetAWSAccount,
    }));
    const ContactOptions =  [...new Set(Contactoptions)].map((_BusinessContactEmail) => ({
      value: _BusinessContactEmail,
      label: _BusinessContactEmail,
    }));
    const migrationDispositionOptions = [
      
      { value: "TBD", label: "TBD" },
      { value: "DNR", label: "DNR" },
      { value: "Keep on prem", label: "Keep on prem" },
    ];
    const instanceOptions = allservernames.map((instance) => ({
      value: instance.ServerName,
      label: instance.ServerName,
    }));   const counts = {};
    const handleSiteCodeChange = (selectedOption) => {
      setSelectedSiteCode(selectedOption?.value || '');
   
    allservernames.forEach((instance) => {
      const country = instance['MigrationDisposition'] || 'Unknown'; // Handle missing country
      counts[country] = (counts[country] || 0) + 1;
    });
   setdnrcount(counts);
    };
    const handleAWSAccChange = (selectedOption) => {
      setSelectedAWSacc(selectedOption?.value || '');
     
     
    };
    const handleMDChange = (selectedOption) => {
      setSelectedMD(selectedOption?.value || '');
    };
    const handleContactChange = (selectedOption) => {
      setSelectedContact(selectedOption?.value || '');
     
   
    };
    
    const [isExpanded, setIsExpanded] = useState(false);
    const [isHovered, setIsHovered] = useState(false);
    console.log(isExpanded);
    if (isLoading) {
      return <LoadingPage />; // Show loading page while data is being fetched
    }
  return (
    <div>
      
      

      
      <div className="form-section ">
      <p className={`migration-title ${isExpanded ? "expanded" : ""} ${isHovered ? "hover-effect" : ""} `}>
      Migration Web Form</p>
      <p className="poll-date">Last Poll Date: 23-12-2024</p>
      <div
        className={`collapsible ${isExpanded ? "expanded" : ""} ${isHovered ? "hover-effect" : ""}`}
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="collapsible-icon">
       <IoIosArrowDroprightCircle
            className={`arrow-icon ${isExpanded ? "rotated" : ""}`}
          />
          </div>
      </div>

      <div className={`migration-form-container ${isExpanded ? "expanded" : ""}`}>
        
          <div className={`fixed-top-section  ${isExpanded ? "expanded" : ""}`}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}>
            <label>Site Code</label>
            <Select
              id="serverName"
              value={siteCodeOptions.find(
                (option) => option.value === SelectedSiteCode
              )}
              onChange={handleSiteCodeChange}
              isClearable
              placeholder="Select Site Code"
              options={siteCodeOptions}
            />
            <label>Target AWS Account</label>
            <Select
              id="serverName"
              value={AWSAccoptions.find(
                (option) => option.value === SelectedAWSacc
              )}
              onChange={handleAWSAccChange}
              isClearable
              placeholder="Select AWS Account"
              options={AWSAccoptions}
            />
            <label>Business Contact Email</label>
            <Select
              id="serverName"
              value={ContactOptions.find(
                (option) => option.value === SelectedContact
              )}
              onChange={handleContactChange}
              isClearable
              placeholder="Select Business Contact Email"
              options={ContactOptions}
            />
            <label>Instance Name</label>
            <Select
              options={instanceOptions}
              placeholder="Search and Select Instances"
              onChange={handleServerNameChange}
              isMulti
              isSearchable
              isClearable
            />
            <label>Migration Disposition</label>
            
            <Select
              options={migrationDispositionOptions}
              placeholder="Search and Select Instances"
              onChange={handleMDChange}
              isSearchable
              isClearable
            />
            <button className="form-button" onClick={handlemultiSubmit}>
              Multi-Select
            </button>
            <div className="counts-section">
              <b>
                {[
                  dnrCount > 0 ? `DNR: ${dnrCount}` : null,
                  plannedCount > 0 ? `Planned: ${plannedCount}` : null,
                  preplannedCount > 0 ? `Pre-Planned: ${preplannedCount}` : null,
                  keeponpremCount > 0 ? `Keep On Prem: ${keeponpremCount}` : null,
                  tbdCount > 0 ? `TBD: ${tbdCount}` : null,
                ]
                  .filter(Boolean)
                  .join(" --------------------------- ")}
              </b>
              
            </div>
          </div>
        
        </div>
        <div className={`table-container ${isExpanded ? "shifted" : ""}`}>
       <table className="custom-table">
 <div className="table-wrapper">
       <thead className={`table-header`}>
    <tr>
      <th>Server Name</th>
      <th>Business Area</th>
      <th>Backup Plan</th>
      <th>Backup Plan Description</th>
      <th>Support Tier</th>
      <th>Region</th>
      <th>Country</th>
      <th>IP Address</th>
      <th>Migration Disposition</th>
      <th>Second Business Contact</th>
      <th>Site Code</th>
      <th>Target AWS Account</th>
      <th>Use Case</th>
      <th>Use Case Description</th>
      <th>Cost Center</th>
      <th>Cost Center Description</th>
      <th>Functional Area</th>
      <th>Provisioning Justification</th>
      <th>Technical Contact</th>
      <th>Technical Contact Email</th>
      <th>Business Contact</th>
      <th>Business Contact Email</th>
    </tr>
  </thead>
  <tbody className="table-body">
    {(namelength > 0 ? multiservernames : allservernames).map((instance, index) => (
      <tr
        key={index}
        className={`table-row ${
          selectedInstances.includes(instance['ServerName']) ? 'row-selected' : ''
        }`}
        onDoubleClick={(event) => handleRowClick(instance, event)}
        onClick={() => handlesingleRowClick(instance)}
      >
        <td>{instance['ServerName'] || '--'}</td>
        <td>{instance['_BusinessArea'] || '--'}</td>
        <td>{instance['_BackupPlan'] || '--'}</td>
        <td>{instance['_BackupPlanDescription'] || '--'}</td>
        <td>{instance['_SupportTier'] || '--'}</td>
        <td>{instance['Location'] || '--'}</td>
        <td>{instance['Country'] || '--'}</td>
        <td>{instance['IPAddress'] || '--'}</td>
        <td>{instance['MigrationDisposition'] || '--'}</td>
        <td>{instance['SecondBusinessContact'] || '--'}</td>
        <td>{instance['SiteCode'] || '--'}</td>
        <td>{instance['TargetAWSAccount'] || '--'}</td>
        <td>{instance['UseCase'] || '--'}</td>
        <td>{instance['UseCaseDescription'] || '--'}</td>
        <td>{instance['_CostCenter'] || '--'}</td>
        <td>{instance['_CostCenterDescription'] || '--'}</td>
        <td>{instance['_FunctionalArea'] || '--'}</td>
        <td>{instance['_ProvisioningJustification'] || '--'}</td>
        <td>{instance['_TechnicalContact'] || '--'}</td>
        <td>{instance['_TechnicalContactEmail'] || '--'}</td>
        <td>{instance['_BusinessContact'] || '--'}</td>
        <td>{instance['_BusinessContactEmail'] || '--'}</td>
      </tr>
    ))}
  </tbody></div>
</table>
</div>

     
     
       
 
      </div>
        {/* Button to trigger POST request */}
       
       
       {/* {Object.keys(formData).length > 0 && (  */}
       {isHoverVisible &&selectedInstance && (<div>
         <div className="overlay-layer" onClick={handleCloseHover}></div>
         <form onSubmit={handleSubmit}>
        <div className="hover-card">
        <div className="hover-content">
           
            <div className="form-section">
       
            { formData.array.length === 1 && (
              <div className="row">
        <div className="col-md-6 form-group">
            <label>
              Server Name:</label>
              <input
                type="text"
                name="ServerName"
                value={formData.ServerName || ''}
                onChange={handleInputChange}
              />
            <label/>
           
          </div>
          <div className="col-md-6 form-group">
            <label>
              IP Address:
              <input
                type="text"
                name="IPAddress"
                value={formData.IPAddress || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          </div>)}
       
           
          <div className="row">
          <div className="col-md-6 form-group">
         
            <label>
              Migration Disposition:
              {/* <input
                type="text"
                name="MigrationDisposition"
                value={formData.MigrationDisposition || ''}
                onChange={handleInputChange}
              /> */}
              <select
               name="MigrationDisposition"
               className="form-control"
              value={formData.MigrationDisposition || ''}
              onChange={handleInputChange}
              required
            >
             
                <option value={formData.MigrationDisposition}>
                  {formData.MigrationDisposition}
                </option>
             
              {optionData[1]?.MigrationStatus?.map((tier, idx) => (
                tier !== formData.MigrationDisposition && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
          </div>
          <div className="col-md-6 form-group">
          <label>
          Disposition Defintion 
    {formData.MigrationDisposition === "DNR" && 
    
    <input
      type="text"
      readOnly
      value={"Server is ready for decommissioning."}
    />
    }
    {formData.MigrationDisposition === "Keep on Prem" && 
    <input
    type="text"
    readOnly
    value={"Server will remain on-premises, provided there is a business justification."}
    />
    }
    {formData.MigrationDisposition === "Pre-Planned" && 
    <input
    type="text"
    readOnly
    value={"Servers scoped for migration. They will be moved to the 'Planned' disposition after OS hardening."}
    />
    }
    {formData.MigrationDisposition === "Planned" && 
    <input
    type="text"
    readOnly
    value={"Server is ready for migration."}
    />
    }
    {formData.MigrationDisposition === "TBD" && 
    <input
    type="text"
    readOnly
    value={"Servers whose disposition is yet to be decided. Actionable disposition is pending."}
    />
    }
          </label>
          </div>
  
          {( formData.MigrationDisposition === "Keep on Prem") && (
              <div>
          <div className="row">
         
            <div className="col-md-6 form-group">
            <label>
              Business Justification:
              <input
                type="text"
                name="_ProvisioningJustification"
                value={formData._ProvisioningJustification || ''}
                onChange={handleInputChange}
                required
              />
            </label>
         
          </div>
          <div className="col-md-6 form-group">
            <label>
              Business Contact Email:
              <input
                type="email"
                className="form-control"
                name="_BusinessContactEmail"
                value={formData._BusinessContactEmail || ''}
                onChange={handleInputChange}
                required
              />
            </label>
          </div>
          </div>
         
         
          </div>
             
            )}
         
          {(formData.MigrationDisposition === "DNR" ) && (
          <div>
          <div className="row">
         
            <div className="col-md-6 form-group">
            <label>
              Business Contact:
              <input
                type="text"
                name="_BusinessContact"
                value={formData._BusinessContact || ''}
                onChange={handleInputChange}
                required
              />
            </label>
         
          </div>
          <div className="col-md-6 form-group">
            <label>
              Business Contact Email:
              <input
                type="email"
                className="form-control"
                name="_BusinessContactEmail"
                value={formData._BusinessContactEmail || ''}
                onChange={handleInputChange}
                required
              />
            </label>
          </div>
          </div>
         
         
          </div>
          )}
  
         
    </div>
    {(formData.MigrationDisposition === "Planned" || formData.MigrationDisposition === "Pre-Planned")  && (
      <div>
          <div className="row">
          <div className="col-md-6 form-group">
            <label>
            _SupportTier:
           
              {/* <input
                type="text"
                name="_SupportTier"
                value={formData._SupportTier || ''}
                onChange={handleInputChange}
              /> */}
              <select
              className="form-control"
              name="_SupportTier"
              value={formData._SupportTier || ''}
              onChange={handleSupportTierChange}
              required
            >
             
                <option value={formData._SupportTier}>
                  {formData._SupportTier}
                </option>
             
              {optionData[3]?._SupportTier?.map((tier, idx) => (
                tier !== formData._SupportTier && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
            {console.log(formData)}
           
          </div>
          <div className="col-md-6 form-group">
            <label>
            SupportTierDescription:
              <input
                type="text"
                name="_SupportTierDescription"
                value={formData._SupportTierDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          </div>
         
          <div className="row">
          <div className="col-md-6 form-group">
            <label>
              Use Case:
              {/* <input
                type="text"
                name="UseCase"
                value={formData.UseCase || ''}
                onChange={handleInputChange}
              /> */}
              <select
                name="UseCase"
                className="form-control"
              value={formData.UseCase || ''}
              onChange={handleUseCaseChange}
              required
            >
             
                <option value={formData.UseCase}>
                  {formData.UseCase}
                </option>
             
              {optionData[5]?.UseCase?.map((tier, idx) => (
                tier !== formData.UseCase && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
          </div>
          <div className="col-md-6 form-group">
            <label>
              Use Case Description:
              <input
                type="text"
                name="UseCaseDescription"
                value={formData.UseCaseDescription || ''}
                onChange={handleInputChange}
              />
             
            </label>
          </div></div>
         
          <div className="row">
          <div className="col-md-6 form-group">
          <label>
            _BackupPlan:
           
              {/* <input
                type="text"
                name="_BackupPlan"
                value={formData._BackupPlan || ''}
                onChange={handleInputChange}
              /> */}
              <select
              className="form-control"
                name="_BackupPlan"
              value={formData._BackupPlan || ''}
              onChange={handleBackupPlanChange}
              required
            >
             
                <option value={formData._BackupPlan}>
                  {formData._BackupPlan}
                </option>
             
              {optionData[7]?._BackupPlan?.map((tier, idx) => (
                tier !== formData._BackupPlan && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
          </div>
          <div className="col-md-6 form-group">
            <label>
            _BackupPlanDescription:
              <input
                type="text"
                name="_BackupPlanDescription"
                value={formData._BackupPlanDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          </div>
          <div className="row">
          <div className="col-md-6 form-group">
            <label>
              Target AWS Account:
             
              <select
                name="TargetAWSAccount"
                className="form-control"
              value={formData.TargetAWSAccount || ''}
              onChange={handleInputChange}
              required
            >
             
                <option value={formData.TargetAWSAccount}>
                  {formData.TargetAWSAccount}
                </option>
             
              {optionData[2]?.TargetAWSAccount?.map((tier, idx) => (
                tier !== formData.TargetAWSAccount && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
          </div>
          <div className="col-md-6 form-group">
            <label>
              Business Area:
             
           
            <select
                name="_BusinessArea"
                className="form-control"
              value={formData._BusinessArea || ''}
              onChange={handleInputChange}
              required
            >
             
                <option value={formData._BusinessArea}>
                  {formData._BusinessArea}
                </option>
             
              {optionData[11]?._BusinessArea?.map((tier, idx) => (
                tier !== formData._BusinessArea && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
          </div></div>
          <div className="row">
          <div className="col-md-6 form-group">
            <label>
              Business Segment:
             
               <select
                name="_BusinessSegment"
                className="form-control"
              value={formData._BusinessSegment || ''}
              onChange={handleBusinessSegmentChange}
              required
            >
             
                <option value={formData._BusinessSegment}>
                  {formData._BusinessSegment}
                </option>
             
              {optionData[12]?._BusinessSegment?.map((tier, idx) => (
                tier !== formData._BusinessSegment && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
          </div>
          <div className="col-md-6 form-group">
            <label>
              Business Segment Description:
              <input
                type="text"
                name="_BusinessSegmentDescription"
                value={formData._BusinessSegmentDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div></div>
          <div className="row">
          <div className="col-md-6 form-group">
            <label>
              Cost Center:
             
               <select
                name="_CostCenter"
                className="form-control"
              value={formData._CostCenter || ''}
              onChange={handleCostCenterChange}
              required
            >
             
                <option value={formData._CostCenter}>
                  {formData._CostCenter}
                </option>
             
              {optionData[9]?._CostCenter?.map((tier, idx) => (
                tier !== formData._CostCenter && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
          </div>
          <div className="col-md-6 form-group">
            <label>
              Cost Center Description:
              <input
                type="text"
                name="_CostCenterDescription"
                value={formData._CostCenterDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div></div>
          <div className="row">
          <div className="col-md-6 form-group">
            <label>
              Functional Area:
             
              <select
                name="_FunctionalArea"
                className="form-control"
              value={formData._FunctionalArea || ''}
              onChange={handleInputChange}
              required
            >
             
                <option value={formData._FunctionalArea}>
                  {formData._FunctionalArea}
                </option>
             
              {optionData[14]?._FunctionalArea?.map((tier, idx) => (
                tier !== formData._FunctionalArea && ( // Avoid duplicate default option
                  <option key={idx} value={tier}>
                    {tier}
                  </option>
                )
              ))}
            </select>
            </label>
            </div>
            <div className="col-md-6 form-group">
            <label>
              Business Contact:
              <input
                type="text"
                name="_BusinessContact"
                value={formData._BusinessContact || ''}
                onChange={handleInputChange}
                required
              />
            </label>
          </div></div>
          {/* Add other fields for contact details and functional area */}
          <div className="row">
         
          <div className="col-md-6 form-group">
            <label>
              Business Contact Email:
              <input
                type="email"
                className="form-control"
                name="_BusinessContactEmail"
                value={formData._BusinessContactEmail || ''}
                onChange={handleInputChange}
                required
              />
            </label>
          </div>
         
          <div className="col-md-6 form-group">
            <label>
              Second Business Contact:
              <input
                type="text"
                name="SecondBusinessContact"
                value={formData.SecondBusinessContact || ''}
                onChange={handleInputChange}
                required
              />
            </label>
          </div>
          </div>
          <div className="row">
          <div className="col-md-6 form-group">
            <label>
              Technical Contact:
              <input
                type="text"
                name="_TechnicalContact"
                value={formData._TechnicalContact || ''}
                onChange={handleInputChange}
                required
              />
            </label>
          </div>
          <div className="col-md-6 form-group">
            <label>
              Technical Contact Email:
              <input
                type="email"
                className="form-control"
                name="_TechnicalContactEmail"
                value={formData._TechnicalContactEmail || ''}
                onChange={handleInputChange}
                required
              />
            </label>
          </div></div>
          {formData.MigrationDisposition === "Pre-Planned" &&  <p>IT Cloud Migration engineer will reach out to you to request administrative access to the server</p>
          }
         </div>
         
          )}
           </div>
         
         
          <button type="submit" className="form-button" >
            Submit
          </button>
         
          </div> </div>
         
        </form> </div>  )}
       
     
    </div>
  );
};
 
export default Migration;