AWSTemplateFormatVersion: '2010-09-09'
Description: CloudFormation template for an EC2 instance
 
Parameters:
  InstanceName:
    Type: String
    MaxLength: 15
    Description: "Enter a valid name with the format <P><R><L><D><AZ><E><OS><AC><SN>."
    AllowedPattern: "^[AMO][UFACEIXMK][CEWSONM][1-9][1-4ABCD](SBX|DEV|SUS|SUP|AQ1|AQ2|CIN|UAT|LIV)(MS|LX|UX)((?:[A-Z]{3}))(0[1-9]|[1-9][0-9])$"
    ConstraintDescription: "Must follow the specified format."
 
  InstanceType:
    Type: String
    Default: t3.micro
    Description: EC2 instance type
 
  AMI:
    Type: AWS::EC2::Image::Id
    Default: ami-09b015c9ce5901e19
    Description: AMI ID
 
  VolumeSize:
    Type: Number
    Description: "The size of the EBS volume in GiB"
    Default: 50
 
  VolumeSize1:
    Type: Number
    Description: "[Optional - Set 1, to not create] The size of the EBS volume in GiB"
    Default: 1
 
  VolumeSize2:
    Type: Number
    Description: "[Optional - Set 1, to not create] The size of the EBS volume in GiB"
    Default: 1
 
  VolumeSize3:
    Type: Number
    Description: "[Optional - Set 1, to not create] The size of the EBS volume in GiB"
    Default: 1
 
  VolumeSize4:
    Type: Number
    Description: "[Optional - Set 1, to not create] The size of the EBS volume in GiB"
    Default: 1
 
  VolumeType:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  VolumeType1:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  VolumeType2:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  VolumeType3:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  VolumeType4:
    Type: String
    Default: gp3
    AllowedValues:
      - gp2
      - gp3
      - io1
      - io2
      - st1
      - sc1
      - magnetic
    Description: "Type of the volume."
 
  DeviceName:
    Type: String
    Default: /dev/sda1
    Description: "Device name for the volume."
 
  DeviceName1:
    Type: String
    Default: /dev/xvdcz
    Description: "Device name for the volume."
 
  DeviceName2:
    Type: String
    Default: /dev/sdc
    Description: "Device name for the volume."
 
  DeviceName3:
    Type: String
    Default: /dev/sdd
    Description: "Device name for the volume."
 
  DeviceName4:
    Type: String
    Default: /dev/sde
    Description: "Device name for the volume."
 
  SecurityGroupIds:
    Type: CommaDelimitedList
    Default: sg-07055930c2088fbfc
    Description: Security Group IDs
 
  SubnetId:
    Type: AWS::EC2::Subnet::Id
    Default: subnet-004bf7285a8af8d81
    Description: Subnet ID for the EC2 instance
 
  UseBlockDeviceMappings:
    Type: String
    Default: "yes"
    AllowedValues:
      - "yes"
      - "no"
    Description: "Whether to use block device mappings."
 
  CostCenter:
    Type: String
    Default: "6420"
    Description: Cost center code
 
  CostCenterDescription:
    Type: String
    Default: "Infrastructure and NOC"
    Description: Description of the cost center
 
  SupportTier:
    Type: String
    Default: "TIER3"
    Description: Support tier
 
  SupportTierDescription:
    Type: String
    Default: "ON-DEMAND (automatic first backup)"
    Description: Description of the support tier
 
  InstanceSource:
    Type: String
    Default: "INEMB"
    Description: Source of the instance
 
  ProvisioningEntity:
    Type: String
    Default: "HID Engineer"
    Description: Provisioning entity
 
  ProvisioningJustification:
    Type: String
    Default: "RITM0000000"
    Description: Justification for provisioning
 
  BusinessArea:
    Type: String
    Default: "OtherBA"
    Description: Business area
 
  BusinessContact:
    Type: String
    Default: "Paramjeet Singh"
    Description: Business contact person
 
  BusinessContactEmail:
    Type: String
    Default: "<EMAIL>"
    Description: Business contact email
 
  BusinessSegment:
    Type: String
    Default: "9000"
    Description: Business segment
  
  mapmigrated:
    Type: String
    Description: mapmigrated

  BusinessSegmentDescription:
    Type: String
    Default: "HID Global"
    Description: Description of the business segment
 
  TechnicalContact:
    Type: String
    Default: "Steve Hayter"
    Description: Technical contact person
 
  TechnicalContactEmail:
    Type: String
    Default: "<EMAIL>"
    Description: Technical contact email
 
  Environment:
    Type: String
    Default: "SANDBOX"
    Description: Environment type
 
  # NetworkLocation:
  #   Type: String
  #   Default: "INTERNAL"
  #   Description: Network location
 
  FunctionalArea:
    Type: String
    Default: "IT"
    Description: Functional area
 
  ProvisioningEngineer:
    Type: String
    Default: "Prasana Srinivasan"
    Description: Provisioning engineer
 
  BackupPlan:
    Type: String
    Default: "BRONZE"
    Description: Backup plan
 
Conditions:
  UseBlockDeviceMappingsCondition: !Equals [ !Ref UseBlockDeviceMappings, "yes" ]
  CreateVolume1: !Not [ !Equals [ !Ref VolumeSize1, 1 ] ]
  CreateVolume2: !Not [ !Equals [ !Ref VolumeSize2, 1 ] ]
  CreateVolume3: !Not [ !Equals [ !Ref VolumeSize3, 1 ] ]
  CreateVolume4: !Not [ !Equals [ !Ref VolumeSize4, 1 ] ]
 
Resources:
  MyEC2Instance:
    Type: AWS::EC2::Instance
    Properties:
      InstanceType: !Ref InstanceType
      ImageId: !Ref AMI
      SecurityGroupIds: !Ref SecurityGroupIds
      IamInstanceProfile: !Ref EC2InstanceProfile
      SubnetId: !Ref SubnetId
     
      UserData:
        Fn::Base64: !Sub |
          <powershell>
          Import-Module AWSPowerShell
          Import-Module Microsoft.PowerShell.Management
          Set-ExecutionPolicy Unrestricted -Force
          $LogPath = "C:\domain_join_log.txt"
          "Starting script execution at $(Get-Date)" | Out-File $LogPath -Append

          $semaphoreRestart = "C:\semaphorerestart.txt"
          $semaphoreJoin = "C:\semaphorejoin.txt"

          # Check if the restart semaphore file exists
          if (-not (Test-Path $semaphoreRestart)) {
          New-Item -Path $semaphoreRestart -ItemType File
          "Created restart semaphore file." | Out-File $LogPath -Append

          # Rename computer to InstanceName
          $InstanceName = "${InstanceName}"
          Rename-Computer -NewName $InstanceName -Force
          "Computer renamed to $InstanceName." | Out-File $LogPath -Append

          # Restart the machine
          Shutdown -r -t 0
          "Initiated restart." | Out-File $LogPath -Append
          exit
          }

          # Check if the join semaphore file exists
          if (-not (Test-Path $semaphoreJoin)) {
          $ifIndex = Get-NetAdapter | Select -ExpandProperty ifIndex
          Set-DnsClientServerAddress -InterfaceIndex $ifIndex -ServerAddresses ("***********", "***********")
          $userParameterName = "domainuser"
          $passwordParameterName = "domainpassword"
          $userPlainText = (Get-SSMParameterValue -Name $userParameterName -WithDecryption $true).Parameters[0].Value
          $userSecureString = ConvertTo-SecureString -String $userPlainText -AsPlainText -Force
          $passwordPlainText = (Get-SSMParameterValue -Name $passwordParameterName -WithDecryption $true).Parameters[0].Value
          $passwordSecureString = ConvertTo-SecureString -String $passwordPlainText -AsPlainText -Force
          $credential = New-Object System.Management.Automation.PSCredential ($userPlainText, $passwordSecureString)

          $domain_ad = "ad.global"
          $tokenUri = "http://***************/latest/api/token"
          $tokenHeaders = @{ "X-aws-ec2-metadata-token-ttl-seconds" = "21600" }
          $token = Invoke-RestMethod -Uri $tokenUri -Method PUT -Headers $tokenHeaders

          $uri = "http://***************/latest/meta-data/placement/region"
          $headers = @{ "X-aws-ec2-metadata-token" = $token }
          $region = Invoke-RestMethod -Uri $uri -Method GET -Headers $headers

          $ouPaths = @{
            "us-east-1" = "OU=USAWS,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global";
            "ap-south-1" = "OU=INAWS,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global";
            "eu-central-1" = "OU=DEAWS,OU=Servers,OU=HID,OU=SSC,DC=ad,DC=global";
          }

          $ouPath = $ouPaths[$region]

          # Join the machine to the domain
          Add-Computer -DomainName $domain_ad -Credential $credential -OUPath $ouPath -Restart
          "Initiated domain join for $domain_ad in $ouPath." | Out-File $LogPath -Append

          New-Item -Path $semaphoreJoin -ItemType File
          "Created join semaphore file." | Out-File $LogPath -Append
          }
          </powershell>
          <persist>true</persist>
 
      PropagateTagsToVolumeOnCreation: true
 
      BlockDeviceMappings:
        - DeviceName: !Ref DeviceName
          Ebs:
            VolumeSize: !Ref VolumeSize
            VolumeType: !Ref VolumeType
            Encrypted: true
            DeleteOnTermination: true
      Tags:
        - Key: Name
          Value: !Ref InstanceName
        - Key: _CostCenter
          Value: !Ref CostCenter
        - Key: _CostCenterDescription
          Value: !Ref CostCenterDescription
        - Key: _SupportTier
          Value: !Ref SupportTier
        - Key: _SupportTierDescription
          Value: !Ref SupportTierDescription
        - Key: _InstanceSource
          Value: !Ref InstanceSource
        - Key: _ProvisioningEntity
          Value: !Ref ProvisioningEntity
        - Key: _ProvisioningJustification
          Value: !Ref ProvisioningJustification
        - Key: _BusinessArea
          Value: !Ref BusinessArea
        - Key: _BusinessContact
          Value: !Ref BusinessContact
        - Key: _BusinessContactEmail
          Value: !Ref BusinessContactEmail
        - Key: _BusinessSegment
          Value: !Ref BusinessSegment
        - Key: _BusinessSegmentDescription
          Value: !Ref BusinessSegmentDescription
        - Key: _TechnicalContact
          Value: !Ref TechnicalContact
        - Key: _TechnicalContactEmail
          Value: !Ref TechnicalContactEmail
        - Key: _Environment
          Value: !Ref Environment
        # - Key: _NetworkLocation
        #   Value: !Ref NetworkLocation
        - Key: _FunctionalArea
          Value: !Ref FunctionalArea
        - Key: _ProvisioningEngineer
          Value: !Ref ProvisioningEngineer
        - Key: _BackupPlan
          Value: !Ref BackupPlan
        - Key: map-migrated
          Value: !Ref mapmigrated
 
  MyVolume1:
    Type: AWS::EC2::Volume
    Condition: CreateVolume1
    DependsOn: MyEC2Instance
    Properties:
      AvailabilityZone: !GetAtt MyEC2Instance.AvailabilityZone
      Size: !Ref VolumeSize1
      VolumeType: !Ref VolumeType1
      Encrypted: true
      Tags:
        - Key: Name
          Value: !Ref InstanceName
        - Key: _CostCenter
          Value: !Ref CostCenter
        - Key: _CostCenterDescription
          Value: !Ref CostCenterDescription
        - Key: _SupportTier
          Value: !Ref SupportTier
        - Key: _SupportTierDescription
          Value: !Ref SupportTierDescription
        - Key: _InstanceSource
          Value: !Ref InstanceSource
        - Key: _ProvisioningEntity
          Value: !Ref ProvisioningEntity
        - Key: _ProvisioningJustification
          Value: !Ref ProvisioningJustification
        - Key: _BusinessArea
          Value: !Ref BusinessArea
        - Key: _BusinessContact
          Value: !Ref BusinessContact
        - Key: _BusinessContactEmail
          Value: !Ref BusinessContactEmail
        - Key: _BusinessSegment
          Value: !Ref BusinessSegment
        - Key: _BusinessSegmentDescription
          Value: !Ref BusinessSegmentDescription
        - Key: _TechnicalContact
          Value: !Ref TechnicalContact
        - Key: _TechnicalContactEmail
          Value: !Ref TechnicalContactEmail
        - Key: _Environment
          Value: !Ref Environment
        # - Key: _NetworkLocation
        #   Value: !Ref NetworkLocation
        - Key: _FunctionalArea
          Value: !Ref FunctionalArea
        - Key: _ProvisioningEngineer
          Value: !Ref ProvisioningEngineer
        - Key: _BackupPlan
          Value: !Ref BackupPlan
        - Key: map-migrated
          Value: !Ref mapmigrated
 
  MyVolume2:
    Type: AWS::EC2::Volume
    Condition: CreateVolume2
    DependsOn: MyEC2Instance
    Properties:
      AvailabilityZone: !GetAtt MyEC2Instance.AvailabilityZone
      Size: !Ref VolumeSize2
      VolumeType: !Ref VolumeType2
      Encrypted: true
      Tags:
        - Key: Name
          Value: !Ref InstanceName
        - Key: _CostCenter
          Value: !Ref CostCenter
        - Key: _CostCenterDescription
          Value: !Ref CostCenterDescription
        - Key: _SupportTier
          Value: !Ref SupportTier
        - Key: _SupportTierDescription
          Value: !Ref SupportTierDescription
        - Key: _InstanceSource
          Value: !Ref InstanceSource
        - Key: _ProvisioningEntity
          Value: !Ref ProvisioningEntity
        - Key: _ProvisioningJustification
          Value: !Ref ProvisioningJustification
        - Key: _BusinessArea
          Value: !Ref BusinessArea
        - Key: _BusinessContact
          Value: !Ref BusinessContact
        - Key: _BusinessContactEmail
          Value: !Ref BusinessContactEmail
        - Key: _BusinessSegment
          Value: !Ref BusinessSegment
        - Key: _BusinessSegmentDescription
          Value: !Ref BusinessSegmentDescription
        - Key: _TechnicalContact
          Value: !Ref TechnicalContact
        - Key: _TechnicalContactEmail
          Value: !Ref TechnicalContactEmail
        - Key: _Environment
          Value: !Ref Environment
        # - Key: _NetworkLocation
        #   Value: !Ref NetworkLocation
        - Key: _FunctionalArea
          Value: !Ref FunctionalArea
        - Key: _ProvisioningEngineer
          Value: !Ref ProvisioningEngineer
        - Key: _BackupPlan
          Value: !Ref BackupPlan
        - Key: map-migrated
          Value: !Ref mapmigrated
 
  MyVolume3:
    Type: AWS::EC2::Volume
    Condition: CreateVolume3
    DependsOn: MyEC2Instance
    Properties:
      AvailabilityZone: !GetAtt MyEC2Instance.AvailabilityZone
      Size: !Ref VolumeSize3
      VolumeType: !Ref VolumeType3
      Encrypted: true
      Tags:
        - Key: Name
          Value: !Ref InstanceName
        - Key: _CostCenter
          Value: !Ref CostCenter
        - Key: _CostCenterDescription
          Value: !Ref CostCenterDescription
        - Key: _SupportTier
          Value: !Ref SupportTier
        - Key: _SupportTierDescription
          Value: !Ref SupportTierDescription
        - Key: _InstanceSource
          Value: !Ref InstanceSource
        - Key: _ProvisioningEntity
          Value: !Ref ProvisioningEntity
        - Key: _ProvisioningJustification
          Value: !Ref ProvisioningJustification
        - Key: _BusinessArea
          Value: !Ref BusinessArea
        - Key: _BusinessContact
          Value: !Ref BusinessContact
        - Key: _BusinessContactEmail
          Value: !Ref BusinessContactEmail
        - Key: _BusinessSegment
          Value: !Ref BusinessSegment
        - Key: _BusinessSegmentDescription
          Value: !Ref BusinessSegmentDescription
        - Key: _TechnicalContact
          Value: !Ref TechnicalContact
        - Key: _TechnicalContactEmail
          Value: !Ref TechnicalContactEmail
        - Key: _Environment
          Value: !Ref Environment
        # - Key: _NetworkLocation
        #   Value: !Ref NetworkLocation
        - Key: _FunctionalArea
          Value: !Ref FunctionalArea
        - Key: _ProvisioningEngineer
          Value: !Ref ProvisioningEngineer
        - Key: _BackupPlan
          Value: !Ref BackupPlan
        - Key: map-migrated
          Value: !Ref mapmigrated
 
  MyVolume4:
    Type: AWS::EC2::Volume
    Condition: CreateVolume4
    DependsOn: MyEC2Instance
    Properties:
      AvailabilityZone: !GetAtt MyEC2Instance.AvailabilityZone
      Size: !Ref VolumeSize4
      VolumeType: !Ref VolumeType4
      Encrypted: true
      Tags:
        - Key: Name
          Value: !Ref InstanceName
        - Key: _CostCenter
          Value: !Ref CostCenter
        - Key: _CostCenterDescription
          Value: !Ref CostCenterDescription
        - Key: _SupportTier
          Value: !Ref SupportTier
        - Key: _SupportTierDescription
          Value: !Ref SupportTierDescription
        - Key: _InstanceSource
          Value: !Ref InstanceSource
        - Key: _ProvisioningEntity
          Value: !Ref ProvisioningEntity
        - Key: _ProvisioningJustification
          Value: !Ref ProvisioningJustification
        - Key: _BusinessArea
          Value: !Ref BusinessArea
        - Key: _BusinessContact
          Value: !Ref BusinessContact
        - Key: _BusinessContactEmail
          Value: !Ref BusinessContactEmail
        - Key: _BusinessSegment
          Value: !Ref BusinessSegment
        - Key: _BusinessSegmentDescription
          Value: !Ref BusinessSegmentDescription
        - Key: _TechnicalContact
          Value: !Ref TechnicalContact
        - Key: _TechnicalContactEmail
          Value: !Ref TechnicalContactEmail
        - Key: _Environment
          Value: !Ref Environment
        # - Key: _NetworkLocation
        #   Value: !Ref NetworkLocation
        - Key: _FunctionalArea
          Value: !Ref FunctionalArea
        - Key: _ProvisioningEngineer
          Value: !Ref ProvisioningEngineer
        - Key: _BackupPlan
          Value: !Ref BackupPlan
        - Key: map-migrated
          Value: !Ref mapmigrated
 
  EC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - HID-EC2-SSM-role
       
  # EBS Volume Attachments
  MyVolumeAttachment1:
    Type: AWS::EC2::VolumeAttachment
    Condition: CreateVolume1
    Properties:
      VolumeId: !Ref MyVolume1
      InstanceId: !Ref MyEC2Instance
      Device: !Ref DeviceName1
 
  MyVolumeAttachment2:
    Type: AWS::EC2::VolumeAttachment
    Condition: CreateVolume2
    Properties:
      VolumeId: !Ref MyVolume2
      InstanceId: !Ref MyEC2Instance
      Device: !Ref DeviceName2
 
  MyVolumeAttachment3:
    Type: AWS::EC2::VolumeAttachment
    Condition: CreateVolume3
    Properties:
      VolumeId: !Ref MyVolume3
      InstanceId: !Ref MyEC2Instance
      Device: !Ref DeviceName3
 
  MyVolumeAttachment4:
    Type: AWS::EC2::VolumeAttachment
    Condition: CreateVolume4
    Properties:
      VolumeId: !Ref MyVolume4
      InstanceId: !Ref MyEC2Instance
      Device: !Ref DeviceName4
 
Outputs:
  InstanceId:
    Description: The instance ID of the newly created EC2 instance
    Value: !Ref MyEC2Instance
 
  PrivateIp:
    Description: The private IP address of the EC2 instance
    Value: !GetAtt MyEC2Instance.PrivateIp
 
 
  AdditionalVolume1Id:
    Description: The ID of the first additional EBS volume
    Value: !If [CreateVolume1, !Ref MyVolume1, "-"]
 
  AdditionalVolume2Id:
    Description: The ID of the second additional EBS volume
    Value: !If [CreateVolume2, !Ref MyVolume2, "-"]
 
  AdditionalVolume3Id:
    Description: The ID of the third additional EBS volume
    Value: !If [CreateVolume3, !Ref MyVolume3, ""]
 
  AdditionalVolume4Id:
    Description: The ID of the fourth additional EBS volume
    Value: !If [CreateVolume4, !Ref MyVolume4, ""]
   
  VolumeAttachment1:
    Description: Attachment of the first additional EBS volume
    Value: !If [CreateVolume1, !Ref MyVolumeAttachment1, "Not Attached"]
 
  VolumeAttachment2:
    Description: Attachment of the second additional EBS volume
    Value: !If [CreateVolume2, !Ref MyVolumeAttachment2, "Not Attached"]
 
  VolumeAttachment3:
    Description: Attachment of the third additional EBS volume
    Value: !If [CreateVolume3, !Ref MyVolumeAttachment3, "Not Attached"]
 
  VolumeAttachment4:
    Description: Attachment of the fourth additional EBS volume
    Value: !If [CreateVolume4, !Ref MyVolumeAttachment4, "Not Attached"]
 
  CostCenter:
    Description: Cost center code
    Value: !Ref CostCenter
 
  CostCenterDescription:
    Description: Description of the cost center
    Value: !Ref CostCenterDescription
 
  SupportTier:
    Description: Support tier
    Value: !Ref SupportTier
 
  SupportTierDescription:
    Description: Description of the support tier
    Value: !Ref SupportTierDescription
 
  InstanceSource:
    Description: Source of the instance
    Value: !Ref InstanceSource
 
  ProvisioningEntity:
    Description: Provisioning entity
    Value: !Ref ProvisioningEntity
 
  ProvisioningJustification:
    Description: Justification for provisioning
    Value: !Ref ProvisioningJustification
 
  BusinessArea:
    Description: Business area
    Value: !Ref BusinessArea
 
  BusinessContact:
    Description: Business contact person
    Value: !Ref BusinessContact
 
  BusinessContactEmail:
    Description: Business contact email
    Value: !Ref BusinessContactEmail
 
  BusinessSegment:
    Description: Business segment
    Value: !Ref BusinessSegment
 
  BusinessSegmentDescription:
    Description: Description of the business segment
    Value: !Ref BusinessSegmentDescription
 
  TechnicalContact:
    Description: Technical contact person
    Value: !Ref TechnicalContact
 
  TechnicalContactEmail:
    Description: Technical contact email
    Value: !Ref TechnicalContactEmail
 
  Environment:
    Description: Environment type
    Value: !Ref Environment
 
  # NetworkLocation:
  #   Description: Network location
  #   Value: !Ref NetworkLocation
 
  FunctionalArea:
    Description: Functional area
    Value: !Ref FunctionalArea
 
  ProvisioningEngineer:
    Description: Provisioning engineer
    Value: !Ref ProvisioningEngineer
 
  BackupPlan:
    Description: Backup plan
    Value: !Ref BackupPlan

  mapmigrated:
    Description: mapmigrated
    Value: !Ref mapmigrated