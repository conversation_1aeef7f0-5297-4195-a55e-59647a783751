import React, { useState, useEffect } from 'react';
import axios from 'axios';

const Migration = () => {
  const [instanceNames, setInstanceNames] = useState(['']);
  const [allservernames, setAllServerNames] = useState(['']); // Array of instance names
  const [formData, setFormData] = useState({}); // Stores the selected/filtered data
  const [allData, setAllData] = useState([]); // Stores all fetched data
  const [isLoading, setIsLoading] = useState(false); // Loading state for fetching data
  const [isSubmitEnabled, setIsSubmitEnabled] = useState(false); // Controls submit button visibility
  const [selectedServer, setSelectedServer] = useState(''); 
  // Fetch all data from the backend when the component mounts (only fetch once)
  useEffect(() => {
    axios
      .get('https://umanage.dev.hidglobal.com/api/migration/data') // Adjust the URL to your backend endpoint
      .then((response) => {
        // Extract ServerName from the response
        const servers = response.data.map((item) => item.ServerName);
        setAllServerNames(servers);
        console.log(servers);
      })
      
      .catch((error) => {
        console.error('Error fetching server names:', error);
      });
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/migration/tages');
        setAllData(response.data);
        console.log(allData); // Store fetched data
      } catch (error) {
        console.error('Error fetching data:', error);
        alert('Failed to fetch data from the backend.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData(); // Fetch data once when the component mounts
  }, []); // Empty dependency array means it runs once after the component mounts

  // Handle IP address input blur event
  const handleAddInstanceName = () => {
    setInstanceNames([...instanceNames, '']);
  };


  const handleRemoveInstanceName = (index) => {
    const updatedNames = instanceNames.filter((_, i) => i !== index);
    setInstanceNames(updatedNames);
  };
  const handleInputChange = (event) => {
    const { name, value } = event.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value, // Update the specific field based on the input's name
    }));
  };
  

  // Handle instance name input change
  const handleInputChangenames = (index, value) => {
    console.log(index);
    console.log(value);
    const updatedNames = [...instanceNames];
    updatedNames[index] = value;console.log(updatedNames);
    setInstanceNames(updatedNames);
    setSelectedServer(value);
    console.log(selectedServer);
  };

  // Send a POST request to the backend when the "Fetch" button is clicked
  const handleFetchData = async () => {
   console.log(instanceNames);

    setIsLoading(true);
    try {
      const response = await axios.get('https://umanage.dev.hidglobal.com/api/migration/data');
      //console.log(response);
      // Process the data after fetching
      const filteredData = response.data.find(
         (row) => row.ServerName === instanceNames[0]// && row.IPAddress === formData.IPAddress
      );
      //console.log(filteredData);

      if (filteredData) {
        // Predefine the values based on the returned row
        setFormData({
          array:instanceNames,
          ServerName: instanceNames[0],
          IPAddress: filteredData['IPAddress'],
          newipaddress: filteredData['newipaddress'],
          Country: filteredData['Country'],
          Location: filteredData['Location'],
          DependencyGroup: filteredData['DependencyGroup'],
          MigrationDisposition: filteredData['MigrationDisposition'], // Column G (index 6)
          UseCase: filteredData['UseCase'], // Column C (index 2)
          UseCaseDescription: filteredData['UseCaseDescription'], // Column M (index 12)
          TargetAWSAccount: filteredData['TargetAWSAccount'], // Column O (index 14)
          SiteCode: filteredData['SiteCode'],
          _CostCenter: filteredData['_CostCenter'], // Column P (index 15)
          _CostCenterDescription: filteredData['_CostCenterDescription'], // Column Q (index 16)
          _BusinessSegment: filteredData['_BusinessSegment'], // Column U (index 20)
          _BusinessSegmentDescription: filteredData['_BusinessSegmentDescription'], // Column S (index 18)
          DeprecatedOS: filteredData['DeprecatedOS'], // Column T (index 19)
          DomainJoined: filteredData['DomainJoined'], // Column W (index 22)
          PatchedtoDate: filteredData['PatchedtoDate'], // Column F (index 5)
          map_migrated: filteredData['map-migrated'],
          _SupportTier: filteredData['_SupportTier'],
          _SupportTierDescription: filteredData['_SupportTierDescription'],
          _BackupPlan: filteredData['_BackupPlan'], // Column AD (index 29)
          _BackupPlanDescription: filteredData['_BackupPlanDescription'], // Column AF (index 31)
          _BusinessContact: filteredData['_BusinessContact'],
          SecondBusinessContact: filteredData['SecondBusinessContact'],
          _BusinessContactEmail: filteredData['_BusinessContactEmail'], // Column AD (index 29)
          _TechnicalContact: filteredData['_TechnicalContact'],
          _TechnicalContactEmail: filteredData['_TechnicalContactEmail'], // Column AD (index 29)
          _ProvisioningJustification: filteredData['_ProvisioningJustification'],
          _NetworkLocation: filteredData['_NetworkLocation'],
          _BusinessArea: filteredData['_BusinessArea'],
          _FunctionalArea: filteredData['_FunctionalArea'],
        });

        setIsSubmitEnabled(true);
        alert('Matching data found!');
      } else {
        alert('No matching data found.');
        setFormData({});
        setIsSubmitEnabled(false);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      alert('Failed to fetch data from the backend.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await fetch('https://umanage.dev.hidglobal.com/api/migration/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      console.log(formData);
      if (response.status==200) {
        const result = await response.json();
        alert('Data submitted successfully!');
        console.log('Server response:', result);
      } else {
        console.error('Failed to submit data:', response.status);
        alert('Failed to submit data. Please try again.');
      }
    } catch (error) {
      console.error('Error during submission:', error);
      alert('An error occurred while submitting data.');
    }
  };

  return (
    <div>
      <h2>Instance Details</h2>
      {isLoading && <p>Loading...</p>}
      <div>
        <h3>Instance Names</h3>
        {instanceNames.map((name, index) => (
          <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '10px' }}>
           <select
        id="serverName"
        value={selectedServer}
        onChange={(e) => handleInputChangenames(index, e.target.value)}
        className="form-control"
      >
        <option value="">-- Select Server --</option>
        {allservernames.map((server, index) => (
          <option key={index} value={server}>
            {server}
          </option>
        ))}
      </select>
      
            <button type="button" onClick={() => handleRemoveInstanceName(index)}>
              Remove
            </button>
          </div>
        ))}
        <button type="button" onClick={handleAddInstanceName}>
          Add Instance Name
        </button>
      </div>
      <button type="button" onClick={handleFetchData}>
        Fetch Data
      </button>
        {/* Button to trigger POST request */}
        

      {Object.keys(formData).length > 0 && (
        <form onSubmit={handleSubmit}>
          <div>
            <label>
              Server Name:
              <input
                type="text"
                name="ServerName"
                value={formData.ServerName || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              IP Address:
              <input
                type="text"
                name="IPAddress"
                value={formData.IPAddress || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              New IP Address:
              <input
                type="text"
                name="newipaddress"
                value={formData.newipaddress || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
           <div>
            <label>
              SiteCode:
              <input
                type="text"
                name="SiteCode"
                value={formData.SiteCode || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Country:
              <input
                type="text"
                name="Country"
                value={formData.Country || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Location:
              <input
                type="text"
                name="Location"
                value={formData.Location || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Dependency Group:
              <input
                type="text"
                name="DependencyGroup"
                value={formData.DependencyGroup || ''}
                onChange={handleInputChange}
              />
            </label>
          </div> 
          <div>
            <label>
            ProvisioningJustification:
              <input
                type="text"
                name="_ProvisioningJustification"
                value={formData._ProvisioningJustification || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
            _SupportTier:
           
              <input
                type="text"
                name="_SupportTier"
                value={formData._SupportTier || ''}
                onChange={handleInputChange}
              />
            </label>
          </div><div>
            <label>
            SupportTierDescription:
              <input
                type="text"
                name="_SupportTierDescription"
                value={formData._SupportTierDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Migration Disposition:
              <input
                type="text"
                name="MigrationDisposition"
                value={formData.MigrationDisposition || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Use Case:
              <input
                type="text"
                name="UseCase"
                value={formData.UseCase || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Use Case Description:
              <input
                type="text"
                name="UseCaseDescription"
                value={formData.UseCaseDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
            _BackupPlan: 
           
              <input
                type="text"
                name="_BackupPlan"
                value={formData._BackupPlan || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
            _BackupPlanDescription:
              <input
                type="text"
                name="_BackupPlanDescription"
                value={formData._BackupPlanDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Target AWS Account:
              <input
                type="text"
                name="TargetAWSAccount"
                value={formData.TargetAWSAccount || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Site Code:
              <input
                type="text"
                name="SiteCode"
                value={formData.SiteCode || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Cost Center:
              <input
                type="text"
                name="_CostCenter"
                value={formData._CostCenter || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Cost Center Description:
              <input
                type="text"
                name="_CostCenterDescription"
                value={formData._CostCenterDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          {/* Add other fields for contact details and functional area */}
          <div>
            <label>
              Business Contact:
              <input
                type="text"
                name="_BusinessContact"
                value={formData._BusinessContact || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Business Contact Email:
              <input
                type="email"
                name="_BusinessContactEmail"
                value={formData._BusinessContactEmail || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Second Business Contact:
              <input
                type="text"
                name="SecondBusinessContact"
                value={formData.SecondBusinessContact || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Technical Contact:
              <input
                type="text"
                name="_TechnicalContact"
                value={formData._TechnicalContact || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Technical Contact Email:
              <input
                type="email"
                name="_TechnicalContactEmail"
                value={formData._TechnicalContactEmail || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          {/* Functional area and business segment */}
          <div>
            <label>
              Functional Area:
              <input
                type="text"
                name="_FunctionalArea"
                value={formData._FunctionalArea || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Business Area:
              <input
                type="text"
                name="_BusinessArea"
                value={formData._BusinessArea || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Business Segment:
              <input
                type="text"
                name="_BusinessSegment"
                value={formData._BusinessSegment || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <div>
            <label>
              Business Segment Description:
              <input
                type="text"
                name="_BusinessSegmentDescription"
                value={formData._BusinessSegmentDescription || ''}
                onChange={handleInputChange}
              />
            </label>
          </div>
          <button type="submit" disabled={!isSubmitEnabled}>
            Submit
          </button>
        </form>
      )}
    </div>
  );
};

export default Migration;
