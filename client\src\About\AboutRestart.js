import React from 'react';
import './AboutStart.css'; 
const AboutRestart = () => {
    return (
        <div className="about-section">
                <h2 className="about-start-title">Restart Action</h2>
           <p className="about-start-description">
  The Restart action is used to <strong>reboot an existing instance</strong> without changing its configuration or instance type.
   This is helpful when the instance becomes unresponsive or you need to apply certain updates.
    Restarting stops the instance briefly and then starts it again, allowing you to restore normal operation without
     launching a new instance.
</p>

            <h3 className="about-start-subtitle">Steps to Restart an Instance</h3>
            <ol className="about-start-steps">
              <li className="about-start-step">
                <strong>Select the Account:</strong> Choose the AWS account where the instance is located.
                <div className="about-start-image-container">
                  {/* <img src={Start} alt="Select Account Sample" className="about-start-image" /> */}
                </div>
              </li>
              <li className="about-start-step">
                <strong>Select the Region:</strong> Pick the region associated with that account, as instance availability is region-specific.
              </li>
              <li className="about-start-step">
                <strong>Select the Instance:</strong> Locate the instance by its ID or Name for easy identification.
                
              </li>
              
              <li className="about-start-step">
                <strong>Review Details:</strong> Confirm the instance details, such as configuration and status.
              </li>
              <li className="about-start-step">
                <strong>Acknowledge:</strong> Check the acknowledgment box to confirm your understanding of the action.
              </li>
              <li className="about-start-step">
                <strong>Click the "Restart" Button:</strong> Execute the Restart action. You can either wait in the portal for a 
                status update or log out, as a confirmation email will be sent indicating the result of the action.
              </li>
            </ol>
            
            {/* <h1>About Restart</h1>
            <p>
                The Restart feature allows you to reboot your instances. 
                This is useful for applying configuration changes or troubleshooting issues.
            </p>
            <h2>Key Features</h2>
            <ul>
                <li>Restart running instances with minimal downtime.</li>
                <li>Ensure instance health checks are passed after restart.</li>
                <li>Monitor the restart process in real-time.</li>
            </ul>
            <h2>How to Use</h2>
            <ol>
                <li>Navigate to the Restart page.</li>
                <li>Select the account, availability zone, and instance.</li>
                <li>Confirm the restart operation to apply the changes.</li>
            </ol> */}
        </div>
    );
};

export default AboutRestart;