import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './FeedbackPage.css';
import { useNavigate } from 'react-router-dom';
import HIDlogo from './assets/hidLogo.png';
const FeedbackForm = () => {
  const [rating, setRating] = useState(0);
  const [feedback, setFeedback] = useState('');
  const [submitted, setSubmitted] = useState(false);
  const navigate = useNavigate();
 const [user,setUser] =useState(
  {
    email: '<EMAIL>',
    displayName: 'test displayname',
    firstName: 'test firstname'
  });
  const handleRatingClick = (star) => {
    setRating(star);
  };
  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/profile');
        setUser(response.data.user);
        
      } catch (error) {
      
        setUser(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[navigate]);

  const handleSubmit = (e) => {
    console.log("in submit");
    console.log(user);
    axios.post('https://umanage.dev.hidglobal.com/api/feedback/submit', {
      stars:rating,
      email: user.email,
      firstname:user.displayName,
      feedback:feedback,
      rating:rating
    })
    .then(response => {
       
      })
    .catch(error => {
     
    });
    e.preventDefault();
    setSubmitted(true);
  };

  return (
    <div className="feedback-page">
      {/* Header */}
      <header className="feedback-header">
      <img src={HIDlogo} alt="HID Logo" className="navbar-logo" />
        <h1>🌟 Share Your Experience! 🌟</h1>
        <p>We value your feedback and continuously strive to improve.</p>
      </header>

      {/* Feedback Form */}
      <div className="feedback-container">
        {!submitted ? (
          <form onSubmit={handleSubmit}>
            <h2 className="form-title">Rate Your Experience</h2>
            
            <p className="subtext">How satisfied are you with our service?</p>
            <div className="star-rating">
              {[1, 2, 3, 4, 5].map((star) => (
                <span
                  key={star}
                  className={`star ${star <= rating ? 'selected' : ''}`}
                  onClick={() => handleRatingClick(star)}
                >
                  {star <= rating ? '⭐' : '☆'}
                </span>
              ))}
            </div>

            <p className="subtext">Tell us more about your experience below! 📝</p>
            <textarea
              className="form-control feedback-text"
              placeholder="Write your feedback here..."
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
            />

            <button type="submit" className="btn btn-primary submit-btn">
              🚀 Submit Feedback
            </button>
          </form>
        ) : (
          <div className="thank-you-message">
            <h2 className="pop-effect">🎉 Thank You! 🎉</h2>
            <p>We appreciate your feedback and will use it to improve our services.</p>
            <p>💡 Your opinion helps us grow! 😊</p>
          </div>
        )}
      </div>

      {/* Footer */}
      {/* <footer className="feedback-footer">
        <p>© 2024 YourCompany. All rights reserved.</p>
      </footer> */}
    </div>
  );
};

export default FeedbackForm;
