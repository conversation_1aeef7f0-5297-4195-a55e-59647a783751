import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './Stop.css';
import HIDlogo from './assets/hidLogo.png';
import Select from 'react-select';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser} from '@fortawesome/free-solid-svg-icons';
import { MdInfo  } from "react-icons/md";
import Ulogo from './assets/Ulogo.png';
import { useNavigate } from 'react-router-dom';
import { FaCheckCircle, FaTimesCircle } from 'react-icons/fa';
import { IoIosClose } from "react-icons/io";
import Loading from './assets/Rocket.gif';
import Navbar from './Navbar';
function Access() {
    const [showTicketTooltip, setshowTicketTooltip] = useState(false); 
  const [email, setEmail] = useState("");
  const [username, setUsername] = useState("");
  const [justification, setJustification] = useState("");
  const [account, setAccount] = useState("");
  const [type, setType] = useState("");
  const [accounts, setAccounts] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const[messagestatus, setMessagestatus] = useState();
  const [message, setMessage] = useState('');
  const [data, setData] = useState('');
  const [alertMessage, setAlertMessage] = useState("");

  useEffect(() => {
    async function checkAuth() {
      try {
        const response = await axios.get('https://umanage.dev.hidglobal.com/api/access/accounts');
        const accounts = response.data.map(item => item.account);
        setData(response.data);
        setAccounts(accounts);
        console.log(response.data);
      } catch (error) {
      
        setAccounts(null); // Set user to null in case of an error
      }
      
    }
    checkAuth();
  },[]);
  

  
  const handleTriggerSSM = () => {
    setIsProcessing(true);
    setAlertMessage("Awaiting Process Completion !");
    const selectedAccountObject = data.find(item => item.account === account);
    axios.post('https://umanage.dev.hidglobal.com/api/access/request', {
       
        account: selectedAccountObject.account,
        AAO: selectedAccountObject.AAO,
        AAU: selectedAccountObject.AAU,
        email: email.trim(),
        username:username.trim(),
        justification: justification.trim(),
        type: type
    })
    .then(response => {
       
      setMessage(`Request send successfully!!!`);
      setMessagestatus(true);
      })
    .catch(error => {
      setMessage(`Error: ${error}`);
      setMessagestatus(false);
    });
  };
 
  return (
    <div className="Stop-App">
       {/* <Navbar /> */}
  <div className="full-page-content">
  {(message||alertMessage) &&<div  className="notification-container">
        {alertMessage && !message && (
      <div className="alert-card">
        <div className="alert-header">
          <div className="loading-icon">
            <img src={Loading} alt="Loading" className="loading-gif" />
          </div>
          <p className="alert-message">{alertMessage}</p>
          <button className="close-button" onClick={() => setAlertMessage(null)}><IoIosClose /></button>
        </div>
      </div>
    )}

      {/* Status Message Card */}
      {message && (
        <div className={`status-card ${messagestatus ? 'success' : 'error'}`}>
          <div className={`status-icon ${messagestatus ? 'pop-animation' : 'shake-animation'}`}>
            {messagestatus ? <FaCheckCircle size={24} /> : <FaTimesCircle size={24} />}
          </div>
         <p>{message}</p>
          <button className="close-button"onClick={() => {  setMessage(null); setAlertMessage(null);}}><IoIosClose /></button>
          
       
        </div>
      )}
      </div>}
  <p className="main-title label-with-icon">
   U-Access  </p>
    <div className="info-icon-container">
      <MdInfo
        className="info-icon"
        size={23} // Adjust the size of the icon
        onClick={() => setshowTicketTooltip(!showTicketTooltip)} // Toggle tooltip visibility
      />
    </div>
    {showTicketTooltip && (
      <div className="tooltip-overlay">
        <div className="tooltip-card">
          <p>
           to get access to U-manage/AWS
          </p>
        </div>
      </div>
    )}

  
  {/* Description below the heading */}
  

  <div className="dropdown-container">
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Email Address</h2>
      {/* <p className="dropdown-description">Select the account to manage instances.</p> */}
      <input
  type="email"
  className="form-control"
  value={email} // Use the 'email' state variable
  onChange={(e) => setEmail(e.target.value)} // Update the 'email' state variable
  placeholder="<EMAIL>"
/> </div>

    <div className="dropdown-section">
      <h2 className="dropdown-heading">User Name</h2>
      {/* <p className="dropdown-description">Select the account to manage instances.</p> */}
      <input
  type="text"
  className="form-control"
  value={username} // Use the 'email' state variable
  onChange={(e) => setUsername(e.target.value)} // Update the 'email' state variable
  placeholder="First Name"
/></div>


    <div className="dropdown-section">
      <h2 className="dropdown-heading">Account Name</h2>
      
      <select className="form-control" value={account}onChange={(e) => setAccount(e.target.value)}  >
        <option value="">Select Account</option>
        {accounts.map(region => (
          <option key={region} value={region}>{region}</option>
        ))}
      </select>
    </div>
  </div>

  {account && (
    <div className="dropdown-section">
      <h2 className="dropdown-heading">Select the Type</h2>
      {/* <p className="dropdown-description">Pick an instance from the list.</p>
       */}
       <select className="form-control" value={type}onChange={(e) => setType(e.target.value)}  >
        <option value="">Select type</option>
        <option value="AAO">AAO - AWS Account Owner</option>
        <option value="AAU">AAU - AWS Account User</option>
      </select>
      <h2 className="dropdown-heading">Access Justification</h2>
      {/* <p className="dropdown-description">Select the account to manage instances.</p> */}
      <input
  type="text"
  className="big-input-simulated"
  value={justification} // Use the 'email' state variable
  onChange={(e) => setJustification(e.target.value)} // Update the 'email' state variable
  placeholder="..."
/>

    </div>
  )}

  
  <button 
    className="TriggerSSM" 
    disabled={ !type} 
    onClick={handleTriggerSSM}
  >
    Request Access
  </button>
  
  
</div>

    </div>
  );
}

export default Access;
