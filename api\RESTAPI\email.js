const express = require('express');
const nodemailer = require('nodemailer');
const router = express.Router();
 
module.exports = () => {
  router.post('/', async (req, res) => {
   try {
    console.log(req.body);
    const transporter = nodemailer.createTransport({
      host: 'relay.assaabloy.net',
      port: 25,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: '',
      },
    });
const mail = await transporter.sendMail({
      from: '<EMAIL>',
      to: req.body.to,
      cc: ` ${req.body.cc}`,
      subject: req.body.subject,
      html: `${req.body.body}
      
      <p>Thanks,<br>Enterprise AWS Cloud Security and Governance<br>

***** This message is auto-generated by EIT Cloud Team. For assistance, please reach <NAME_EMAIL> *****</p>
`,

    });

    res.status(200).json({ message: 'Request processed successfully' });
  } catch (error) {
    console.error('Error in handleSSMRequest:', error);
    res.status(500).json({ error: error.message });
  }
  });
 
  return router;
};


