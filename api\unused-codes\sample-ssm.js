const express = require('express');
const router = express.Router();
const AWS = require('aws-sdk');
const { SSMClient, SendCommandCommand, GetCommandInvocationCommand } = require('@aws-sdk/client-ssm');
const { EC2Client, DescribeInstancesCommand } = require('@aws-sdk/client-ec2');
// Export the function to set up the router with necessary utilities
module.exports = (generateTicketNumber, storeDataInS3) => {
  let originalCredentials = AWS.config.credentials;

  // Function to assume an IAM role and return temporary credentials
  const assumeRole = async (accountId,firstname) => {
    const sts = new AWS.STS();
    const params = {
      RoleArn: `arn:aws:iam::************:role/CrossAccountAccessRole`,
      RoleSessionName: firstname,
    };
    console.log(firstname);
    console.log(params);
    try {
      const data = await sts.assumeRole(params).promise();
      return data.Credentials;
    } catch (error) {
      AWS.config.update({ credentials: null });
        console.error('Error assuming role:', error.code, error.message);
        if (error.code === 'AccessDenied') {
          const delay = Math.pow(2, attempt) * 1000; // Convert to milliseconds
          console.log(`Retrying in ${delay / 1000} seconds...`);
          await new Promise(resolve => setTimeout(resolve, delay)); 
          const data = await sts.assumeRole(params).promise();
          return data.Credentials;
            // Handle AccessDenied specifically, maybe log the accounts involved
        
      
    }else{
      console.error('Error assuming role:', error);
      throw error;
    }}
  };

  // Function to initialize AWS SDK with temporary credentials and region
  const initializeAWS = async (credentials, region) => {
    AWS.config.update({
      credentials: new AWS.Credentials(
        credentials.AccessKeyId,
        credentials.SecretAccessKey,
        credentials.SessionToken
      ),
      region: region
    });
  };

  // Function to configure AWS services (SSM, S3, CloudFormation)
  const configureAWS = () => {
    return {
      ssm: new AWS.SSM(),
      s3: new AWS.S3(),
      cloudFormation: new AWS.CloudFormation()
    };
  };

  // Function to get the status of an SSM Automation execution
  const getAutomationExecutionStatus = async (ssm, executionId) => {
    try {
      const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      console.log('Automation execution data:', data); // Log entire response
      const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
      return status;
    } catch (error) {
      console.error('Error fetching execution status:', error);
      throw error;
    }
  };

  // Function to fetch detailed results of an SSM Automation execution
  const getAutomationExecutionDetails = async (ssm, executionId) => {
    try {
      const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
      return data.AutomationExecution;
    } catch (error) {
      console.error('Error fetching execution details:', error);
      throw error;
    }
  };

  // Function to poll the status of an SSM Automation execution
  const pollAutomationExecutionStatus = async (ssm, executionId, interval = 5000) => {
    return new Promise((resolve, reject) => {
      const intervalId = setInterval(async () => {
        try {
          const status = await getAutomationExecutionStatus(ssm, executionId);
          console.log('Current status:', status);

          if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
            clearInterval(intervalId);
            resolve(status);
          }
        } catch (error) {
          clearInterval(intervalId);
          reject(error);
        }
      }, interval);
    });
  };

  // Function to handle SSM requests (start/stop EC2 instances)
  const handleSSMRequest = async (req, res, documentName) => {
    const { instanceId, region, businesscontact, email, accountId,accountname, instancename ,servicenownumber} = req.body;

    let firstname=req.body.firstname;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
    if (!instanceId || !region || !accountId) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    //const accountId = '************'; // Replace with your AWS Account ID
    const params = {
      Region: 'us-west-2', // Replace with the desired region
      
    };
    
    const initializeEC2Client = async (accountId, params) => {
      const credentials = await assumeRole(accountId, params.Region);
    
      return new EC2Client({
        region: params.Region,
        credentials: {
          accessKeyId: credentials.AccessKeyId,
          secretAccessKey: credentials.SecretAccessKey,
          sessionToken: credentials.SessionToken
        }
      });
    };
    const getInstanceNames = async (accountId, params, instanceName) => {
      try {
        const ec2Client = await initializeEC2Client(accountId, params);
    
        // Describe all instances
        const command = new DescribeInstancesCommand({});
        const data = await ec2Client.send(command);
        
        // Loop through instances to find the matching instance name
        for (const reservation of data.Reservations) {
          for (const instance of reservation.Instances) {
            const nameTag = instance.Tags?.find(tag => tag.Key === 'Name');
            console.log(nameTag);
            if (nameTag && nameTag.Value === instanceName) {
              console.log('Duplicate EC2 instance found with Name:', instanceName);
              return true;
            }
          }
        }
    
        console.log('No duplicate instance found. Proceeding...');
        return false;
      } catch (error) {
        console.error('Error checking instances:', error);
        return false;
      }
    };getInstanceNames(accountId, params, 'AUE2AAQ1MSSWL09');
    try {
      // AWS.config.update({ credentials: originalCredentials });
      if(accountId!=************){
      const credentials = await assumeRole(accountId,y);
      await initializeAWS(credentials, region);}else{AWS.config.update({
        region: region
      });}
      const { ssm } = configureAWS();

      const ticketNumber = generateTicketNumber();
      let executionID;
      const startTime = new Date();
      const data = await ssm.startAutomationExecution(params).promise();
      executionID = data.AutomationExecutionId;
      console.log(originalCredentials);
      AWS.config.update({ credentials: originalCredentials });
      // Poll status and fetch detailed result
      const status = await pollAutomationExecutionStatus(ssm, executionID);
      const executionDetails = await getAutomationExecutionDetails(ssm, executionID);

      console.log('Execution details:', executionDetails);
      //const startTime = new Date(executionDetails.ExecutionStartTime * 1000).toISOString();
//const endTime = new Date(executionDetails.ExecutionEndTime * 1000).toISOString();
     // const startTime = executionDetails.ExecutionStartTime;
      const endTime =new Date();
      //await storeDataInS3(ticketNumber, executionID,instancename,accountId, executionDetails. DocumentName, startTime,endTime, executionDetails. AutomationExecutionStatus, executionDetails. Parameters. InstanceId,businesscontact,email,accountname,servicenownumber);
      AWS.config.update({ credentials: originalCredentials });
      if(executionDetails.StepExecutions[0].StepStatus=='Failed'){
        AWS.config.update({ credentials: originalCredentials });
        res.status(200).json({message:executionDetails.StepExecutions[0].FailureMessage, ticketNumber,
          executionId: executionID,
          status,
          executionDetails });
      }else{
        AWS.config.update({ credentials: originalCredentials });
      res.status(200).json({
        message: 'SSM Automation Document triggered successfully!',
        ticketNumber,
        executionId: executionID,
        status,
        executionDetails // Include details in the response
      });}
    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
      res.status(500).json({ error: err.message });
    }
  };
  const handleRunTerraformRequest = async (req, res, documentName) => {
    console.log("in function");
    const { instanceId, region, businesscontact, email, accountId, accountname, instancename, servicenownumber } = req.body;

    let firstname=req.body.firstname;
    let x=firstname.replace(/\s+/g,'');
    let result=x.substring(0,8);
    let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y = result + randomValue.toString();
    if (!instanceId || !region || !accountId) {
        return res.status(400).json({ error: 'Missing required fields' });
    }

    const params = {
        DocumentName: 'AWS-RunPowerShellScript',
        Parameters: {
            commands: [
                `$domain = (Get-WmiObject Win32_ComputerSystem).Domain;` +
                `if ($domain -ne "WORKGROUP") {` +
                `    Write-Output "This instance is part of the domain: $domain"` +
                `} else {` +
                `    Write-Output "This instance is not part of a domain."` +
                `}`
            ]
        },
        Targets: [
            {
                Key: 'instanceids',
                Values: [instanceId]
            }
        ],
        Comment: 'Running PowerShell script from Node.js'
    };

    try {
        if (accountId != ************) {
            const credentials = await assumeRole(accountId, y);
            await initializeAWS(credentials, region);
        } else {
            AWS.config.update({ region: region });
        }
        const { ssm } = configureAWS();

        const ticketNumber = generateTicketNumber();
        const data = await ssm.sendCommand(params).promise();
        const commandID = data.Command.CommandId;
        
        // Polling for command status
        const commandStatus = await pollCommandStatus(ssm, commandID);
        AWS.config.update({ credentials: originalCredentials });
        // Handle the command output here
        const commandDetails = await getCommandInvocation(ssm, commandID, instanceId);
        console.log('Command execution status:', commandDetails.Status);
        console.log('Command output:', commandDetails.CommandPlugins[0].Output);

        //await storeDataInS3(ticketNumber, commandID, accountId, commandDetails.CommandPlugins[0].Output,businesscontact, email, accountname, instancename, servicenownumber);
        AWS.config.update({ credentials: originalCredentials });
        res.status(200).json({
            message: 'SSM Command executed successfully!',
            
            status: commandDetails.CommandPlugins[0].Output,
           
        });
    } catch (err) {
      AWS.config.update({ credentials: originalCredentials });
        res.status(500).json({ error: err.message });
    }
};

// Function to poll command status
const pollCommandStatus = async (ssm, commandID) => {
    let status;
    do {
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait for 5 seconds
        const commandInvocations = await ssm.listCommandInvocations({
            CommandId: commandID,
            Details: true,
        }).promise();
        status = commandInvocations.CommandInvocations[0].Status; // Get the status of the first invocation
    } while (status === 'Pending' || status === 'InProgress');

    return status;
};

// Function to get command invocation details
const getCommandInvocation = async (ssm, commandID, instanceId) => {
    const commandInvocations = await ssm.listCommandInvocations({
        CommandId: commandID,
        Details: true,
    }).promise();
    return commandInvocations.CommandInvocations.find(invocation => invocation.InstanceId === instanceId);
};


  
const handledomainSSMRequest = async (req, res, documentName) => {
  const { instanceId, region,  accountId, instanceName,group1,group2,group3,group4} = req.query;
  
console.log(req.body);
  res.setHeader('Content-Type', 'text/event-stream');
res.setHeader('Cache-Control', 'no-cache');
res.setHeader('Connection', 'keep-alive');
const sendUpdate = (message) => {
  res.write(`data: ${JSON.stringify({ message })}\n\n`);
};
  let firstname=req.query.firstname;
  let x=firstname.replace(/\s+/g,'');
  let result=x.substring(0,8);
  let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
console.log(req.query);
  
 let params;
 if (documentName === "windows_domain_join") {
  params = {
    DocumentName: "Stack-Pipeline",
    Parameters: {
      ContactingCustomer: ["<EMAIL>"],
      InstanceName: ['AUE2AAQ1MSSQL87'],
     // Assumerole: [`arn:aws:iam::************:role/CrossAccountAccessRole`],
      RequestedFor:["<EMAIL>"],
      AffectingUser:["<EMAIL>"],
      ShortDescription:[`creating by dev`],
      Description:[`Instance in AccountId:  `],
      DomainJoin: ['No'],
      AMI: ['ami-0ad2d93529377143b'],
      SubnetId: [`subnet-0573936e5d1d10897`],
      mapmigrated:['migSITGOMR8R2'],
      SecurityGroupIds:['sg-06511b1092c90682f'],
      
      // Add other parameters required by your SSM document here
    }
  };
 } else {
  params = {
    DocumentName: "Stack-Pipeline",
    Parameters: {
      ContactingCustomer: ["<EMAIL>"],
      InstanceName: ['AUE2AAQ1MSSQL87'],
     
      RequestedFor:["<EMAIL>"],
      AffectingUser:["<EMAIL>"],
      ShortDescription:[`creating by dev`],
      Description:[`Instance in AccountId:  ${req.query.accountId} is added to Domain Instance Details Instance Name: ${req.query.instanceName} Instance Id: ${req.query.instanceId} Region: ${req.query.region} `],
      DomainJoin: ['No'],
      AMI: ['ami-0ad2d93529377143b'],
      SubnetId: [`subnet-0573936e5d1d10897`],
      mapmigrated:['migSITGOMR8R2'],
      SecurityGroupIds:['sg-06511b1092c90682f'],
      
      // Add other parameters required by your SSM document here
    }
  };
 }console.log(params);
  try {
    // AWS.config.update({ credentials: originalCredentials });
    console.log('in try');
    if(accountId!=************){
      console.log('in tryin if');
    const credentials = await assumeRole(accountId,y);
    console.log('in try after if');
    await initializeAWS(credentials, region);}else{AWS.config.update({
      region: region
    });}
    const { ssm } = configureAWS();

    const ticketNumber = generateTicketNumber();
    let executionID;
    const startTime = new Date();
    try {
      console.log('in try before ssm');
      const data = await ssm.startAutomationExecution(params).promise();
      console.log('in try after ssm');
      executionID = data.AutomationExecutionId;
    } catch (error) {
      console.error('Error starting automation execution:', error);
    }
   
    // Poll status and fetch detailed result
    const getAutomationssmExecutionStatus = async (ssm, executionId) => {
      try {
        const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
        console.log('Automation execution data:', data); // Log entire response
        const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
        return data;
      } catch (error) {
        console.error('Error fetching execution status:', error);
        throw error;
      }
    };
    let servicenownumber;
      // Poll status and fetch detailed result
      const pollAutomationssmExecutionStatus = async (ssm, executionId, interval = 1000) => {
        return new Promise((resolve, reject) => {
          const intervalId = setInterval(async () => {
            try {
              const data = await getAutomationssmExecutionStatus(ssm, executionId);
              const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
              console.log('Current status:', status);
              const targetStep = data.AutomationExecution.StepExecutions.find(
                step => step.StepName === 'CloseNotes'
              );
          
              if (targetStep && targetStep.Outputs) {
                console.log("Step Outputs:", targetStep.Outputs);
               //servicenownumber= targetStep.Outputs.RITM[0];
                console.log("Step Outputs:", targetStep.Outputs);
              } else {
                console.log("No outputs found for the specified step.");
              }
              sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
              if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
               
                clearInterval(intervalId);
                resolve(status);
              }
            } catch (error) {
              clearInterval(intervalId);
              reject(error);
            }
          }, interval);
        });
      }; console.log('in try before status');
    const status = await pollAutomationssmExecutionStatus(ssm, executionID);
    const executionDetails = await getAutomationssmExecutionStatus(ssm, executionID);
  
  //   if(getAutomationssmExecutionStatus(ssm, executionID).AutomationExecution.AutomationExecutionStatus=='Success'){
  //   sendUpdate(`Please wait Service now ticket is creating  `);
  //   AWS.config.update({ credentials: originalCredentials });
  // const paramsforservice = {
  //   DocumentName: 'User-Snow-Ticket', // Replace with your SSM Automation Document name
  //   Parameters: {
  //     Description: [` ${params} `],
  //     ContactingCustomer:[req.query.email],
  //     AffectingUser:[req.query.email], // Pass the email as a parameter
  //     ShortDescription: [`Domain join Action`],
  //     RequestedFor:[req.query.email],
  //     WatchList:[`<EMAIL>,<EMAIL>`] // Pass the description as a parameter
  //   },
  //    };
  //    const data = await ssm.startAutomationExecution(paramsforservice).promise();
  //    executionID = data.AutomationExecutionId;
  //    try{
      
  //    }catch{

  //    }
  //    sendUpdate(`Service now Ticket created successfully`); 
  // }
  //   console.log('Execution details:', executionDetails);
    //const startTime = new Date(executionDetails.ExecutionStartTime * 1000).toISOString();
//const endTime = new Date(executionDetails.ExecutionEndTime * 1000).toISOString();
   // const startTime = executionDetails.ExecutionStartTime;
    const endTime =new Date();
    const data2 = await getAutomationssmExecutionStatus(ssm, executionID);
    const status2= data2.AutomationExecution ? data2.AutomationExecution.AutomationExecutionStatus : 'Unknown';
    console.log("status2",data2);
    console.log(data2.Outputs);
    const x=data2.AutomationExecution.StepExecutions.find(
      step => step.StepName === 'RetrieveRITM'
    );
    console.log(x);
    console.log(x.Outputs);
    console.log(x.Outputs.RITM);
    //sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
      AWS.config.update({ credentials: originalCredentials });
      sendUpdate('SSM Automation Document execution completed.');
      sendUpdate(`RITM Number is  ${x.Outputs.RITM} And been closed`);
     // sendUpdate('OK');
      res.end();
  } catch (err) {
    sendUpdate(`Error: ${err}`);
    res.end();
  }
};




  
const handledomainlinuxSSMRequest = async (req, res, documentName) => {
  const { instanceId, region,  accountId, instanceName,group1,group2,group3,group4} = req.query;
  
console.log(req.body);
  res.setHeader('Content-Type', 'text/event-stream');
res.setHeader('Cache-Control', 'no-cache');
res.setHeader('Connection', 'keep-alive');
const sendUpdate = (message) => {
  res.write(`data: ${JSON.stringify({ message })}\n\n`);
};
  let firstname=req.query.firstname;
  let x=firstname.replace(/\s+/g,'');
  let result=x.substring(0,8);
  let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
console.log(req.query);
  if (!instanceId || !region || !accountId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  const groups=`${req.query.group1}, ${req.query.group2}, ${req.query.group3}, ${req.query.group4}`;
 let params;
 if (documentName === "linux_domain_join"||documentName === "linux_domain_prerequisite") {
   params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [req.query.instanceId],
      InstanceName: [req.query.instanceName],
      Assumerole: [`arn:aws:iam::${req.query.accountId}:role/CrossAccountAccessRole`],
      
      GroupName: [groups],
      ContactingCustomer:[req.query.email],
      AffectingUser:[req.query.email],
      ShortDescription:[`Windows Domain Join`],
      Description:[`Instance in AccountId:  ${req.query.accountId} is added to Domain Instance Details Instance Name: ${req.query.instanceName} Instance Id: ${req.query.instanceId} Region: ${req.query.region} `],
      RequestedFor:[req.query.email],
     // WatchList:["<EMAIL>,<EMAIL>"],
     // Add other parameters required by your SSM document here
    }
  };
 } else {
  params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId],
      InstanceName: [req.query.instanceName],
      Assumerole: [`arn:aws:iam::${req.query.accountId}:role/CrossAccountAccessRole`],
      ContactingCustomer:[req.query.email],
      AffectingUser:[req.query.email],
      ShortDescription:[`Windows Domain Join`],
      Description:[`Instance in AccountId:  ${req.query.accountId} is added to Domain Instance Details Instance Name: ${req.query.instanceName} Instance Id: ${req.query.instanceId} Region: ${req.query.region} `],
      RequestedFor:[req.query.email],
     
      // Add other parameters required by your SSM document here
    }
  };
 }console.log(params);
  try {
    // AWS.config.update({ credentials: originalCredentials });
    console.log('in try');
    if(accountId!=************){
      console.log('in tryin if');
    const credentials = await assumeRole(accountId,y);
    console.log('in try after if');
    await initializeAWS(credentials, region);}else{AWS.config.update({
      region: region
    });}
    const { ssm } = configureAWS();

    const ticketNumber = generateTicketNumber();
    let executionID;
    const startTime = new Date();
    try {
      console.log('in try before ssm');
      const data = await ssm.startAutomationExecution(params).promise();
      console.log('in try after ssm');
      executionID = data.AutomationExecutionId;
    } catch (error) {
      console.error('Error starting automation execution:', error);
    }
   
    // Poll status and fetch detailed result
    const getAutomationssmExecutionStatus = async (ssm, executionId) => {
      try {
        const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
        console.log('Automation execution data:', data); // Log entire response
        const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
        return data;
      } catch (error) {
        console.error('Error fetching execution status:', error);
        throw error;
      }
    };
    let servicenownumber;
      // Poll status and fetch detailed result
      const pollAutomationssmExecutionStatus = async (ssm, executionId, interval = 1000) => {
        return new Promise((resolve, reject) => {
          const intervalId = setInterval(async () => {
            try {
              const data = await getAutomationssmExecutionStatus(ssm, executionId);
              const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
              console.log('Current status:', status);
              const targetStep = data.AutomationExecution.StepExecutions.find(
                step => step.StepName === 'CloseNotes'
              );
          
              if (targetStep && targetStep.Outputs) {
                console.log("Step Outputs:", targetStep.Outputs);
               //servicenownumber= targetStep.Outputs.RITM[0];
                console.log("Step Outputs:", targetStep.Outputs);
              } else {
                console.log("No outputs found for the specified step.");
              }
              sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
              if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
               
                clearInterval(intervalId);
                resolve(status);
              }
            } catch (error) {
              clearInterval(intervalId);
              reject(error);
            }
          }, interval);
        });
      }; console.log('in try before status');
    const status = await pollAutomationssmExecutionStatus(ssm, executionID);
    const executionDetails = await getAutomationssmExecutionStatus(ssm, executionID);
  
  //   if(getAutomationssmExecutionStatus(ssm, executionID).AutomationExecution.AutomationExecutionStatus=='Success'){
  //   sendUpdate(`Please wait Service now ticket is creating  `);
  //   AWS.config.update({ credentials: originalCredentials });
  // const paramsforservice = {
  //   DocumentName: 'User-Snow-Ticket', // Replace with your SSM Automation Document name
  //   Parameters: {
  //     Description: [` ${params} `],
  //     ContactingCustomer:[req.query.email],
  //     AffectingUser:[req.query.email], // Pass the email as a parameter
  //     ShortDescription: [`Domain join Action`],
  //     RequestedFor:[req.query.email],
  //     WatchList:[`<EMAIL>,<EMAIL>`] // Pass the description as a parameter
  //   },
  //    };
  //    const data = await ssm.startAutomationExecution(paramsforservice).promise();
  //    executionID = data.AutomationExecutionId;
  //    try{
      
  //    }catch{

  //    }
  //    sendUpdate(`Service now Ticket created successfully`); 
  // }
  //   console.log('Execution details:', executionDetails);
    //const startTime = new Date(executionDetails.ExecutionStartTime * 1000).toISOString();
//const endTime = new Date(executionDetails.ExecutionEndTime * 1000).toISOString();
   // const startTime = executionDetails.ExecutionStartTime;
    const endTime =new Date();
    const data2 = await getAutomationssmExecutionStatus(ssm, executionID);
    const status2= data2.AutomationExecution ? data2.AutomationExecution.AutomationExecutionStatus : 'Unknown';
    console.log("status2",data2);
    console.log(data2.Outputs);
    const x=data2.AutomationExecution.StepExecutions.find(
      step => step.StepName === 'RetrieveRITM'
    );
    console.log(x);
    console.log(x.Outputs);
    console.log(x.Outputs.RITM);
    //sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
      AWS.config.update({ credentials: originalCredentials });
      sendUpdate('SSM Automation Document execution completed.');
      sendUpdate(`RITM Number is  ${x.Outputs.RITM} And been closed`);
     // sendUpdate('OK');
      res.end();
  } catch (err) {
    sendUpdate(`Error: ${err}`);
    res.end();
  }
};





  
const ssmrestart = async (req, res, documentName) => {
  const { instanceId, region,  accountId, instanceName,group1,group2,group3,group4} = req.query;
  
console.log(req.body);
  res.setHeader('Content-Type', 'text/event-stream');
res.setHeader('Cache-Control', 'no-cache');
res.setHeader('Connection', 'keep-alive');
const sendUpdate = (message) => {
  res.write(`data: ${JSON.stringify({ message })}\n\n`);
};
  let firstname=req.query.firstname;
  let x=firstname.replace(/\s+/g,'');
  let result=x.substring(0,8);
  let randomValue = Math.floor(Math.random() * 9000) + 1000;

// Concatenate the random value to the string
let y = result + randomValue.toString();
console.log(y);
console.log(req.query);
  if (!instanceId || !region || !accountId) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  const groups=`${req.query.group1}, ${req.query.group2}, ${req.query.group3}, ${req.query.group4}`;
 let params;
 if (documentName === "AWS-RestartEC2Instance") {
   params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [req.query.instanceId],
      InstanceName: [req.query.instanceName],
      Assumerole: [`arn:aws:iam::${req.query.accountId}:role/CrossAccountAccessRole`],
     
    }
  };
 } else {
  params = {
    DocumentName: documentName,
    Parameters: {
      InstanceId: [instanceId],
      InstanceName: [req.query.instanceName],
      Assumerole: [`arn:aws:iam::${req.query.accountId}:role/CrossAccountAccessRole`],
      ContactingCustomer:[req.query.email],
      AffectingUser:[req.query.email],
      ShortDescription:[`Windows Domain Join`],
      Description:[`Instance in AccountId:  ${req.query.accountId} is added to Domain Instance Details Instance Name: ${req.query.instanceName} Instance Id: ${req.query.instanceId} Region: ${req.query.region} `],
      RequestedFor:[req.query.email],
     
      // Add other parameters required by your SSM document here
    }
  };
 }console.log(params);
  try {
    // AWS.config.update({ credentials: originalCredentials });
    console.log('in try');
    if(accountId!=************){
      console.log('in tryin if');
    const credentials = await assumeRole(accountId,y);
    console.log('in try after if');
    await initializeAWS(credentials, region);}else{AWS.config.update({
      region: region
    });}
    const { ssm } = configureAWS();

    const ticketNumber = generateTicketNumber();
    let executionID;
    const startTime = new Date();
    try {
      console.log('in try before ssm');
      const data = await ssm.startAutomationExecution(params).promise();
      console.log('in try after ssm');
      executionID = data.AutomationExecutionId;
    } catch (error) {
      console.error('Error starting automation execution:', error);
    }
   
    // Poll status and fetch detailed result
    const getAutomationssmExecutionStatus = async (ssm, executionId) => {
      try {
        const data = await ssm.getAutomationExecution({ AutomationExecutionId: executionId }).promise();
        console.log('Automation execution data:', data); // Log entire response
        const status = data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
        return data;
      } catch (error) {
        console.error('Error fetching execution status:', error);
        throw error;
      }
    };
    let servicenownumber;
      // Poll status and fetch detailed result
      const pollAutomationssmExecutionStatus = async (ssm, executionId, interval = 1000) => {
        return new Promise((resolve, reject) => {
          const intervalId = setInterval(async () => {
            try {
              const data = await getAutomationssmExecutionStatus(ssm, executionId);
              const status= data.AutomationExecution ? data.AutomationExecution.AutomationExecutionStatus : 'Unknown';
              console.log('Current status:', status);
              const targetStep = data.AutomationExecution.StepExecutions.find(
                step => step.StepName === 'CloseNotes'
              );
          
              if (targetStep && targetStep.Outputs) {
                console.log("Step Outputs:", targetStep.Outputs);
               //servicenownumber= targetStep.Outputs.RITM[0];
                console.log("Step Outputs:", targetStep.Outputs);
              } else {
                console.log("No outputs found for the specified step.");
              }
              sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
              if (['Success', 'Failed', 'TimedOut', 'Cancelled'].includes(status)) {
               
                clearInterval(intervalId);
                resolve(status);
              }
            } catch (error) {
              clearInterval(intervalId);
              reject(error);
            }
          }, interval);
        });
      }; console.log('in try before status');
    const status = await pollAutomationssmExecutionStatus(ssm, executionID);
    const executionDetails = await getAutomationssmExecutionStatus(ssm, executionID);
  
  //   if(getAutomationssmExecutionStatus(ssm, executionID).AutomationExecution.AutomationExecutionStatus=='Success'){
  //   sendUpdate(`Please wait Service now ticket is creating  `);
  //   AWS.config.update({ credentials: originalCredentials });
  // const paramsforservice = {
  //   DocumentName: 'User-Snow-Ticket', // Replace with your SSM Automation Document name
  //   Parameters: {
  //     Description: [` ${params} `],
  //     ContactingCustomer:[req.query.email],
  //     AffectingUser:[req.query.email], // Pass the email as a parameter
  //     ShortDescription: [`Domain join Action`],
  //     RequestedFor:[req.query.email],
  //     WatchList:[`<EMAIL>,<EMAIL>`] // Pass the description as a parameter
  //   },
  //    };
  //    const data = await ssm.startAutomationExecution(paramsforservice).promise();
  //    executionID = data.AutomationExecutionId;
  //    try{
      
  //    }catch{

  //    }
  //    sendUpdate(`Service now Ticket created successfully`); 
  // }
  //   console.log('Execution details:', executionDetails);
    //const startTime = new Date(executionDetails.ExecutionStartTime * 1000).toISOString();
//const endTime = new Date(executionDetails.ExecutionEndTime * 1000).toISOString();
   // const startTime = executionDetails.ExecutionStartTime;
    // const endTime =new Date();
    // const data2 = await getAutomationssmExecutionStatus(ssm, executionID);
    // const status2= data2.AutomationExecution ? data2.AutomationExecution.AutomationExecutionStatus : 'Unknown';
    // console.log("status2",data2);
    // console.log(data2.Outputs);
    // const x=data2.AutomationExecution.StepExecutions.find(
    //   step => step.StepName === 'RetrieveRITM'
    // );  
    // console.log(x);
    // console.log(x.Outputs);
    // console.log(x.Outputs.RITM);
    // //sendUpdate(`${data.AutomationExecution.CurrentStepName} step is executing `);
    //   AWS.config.update({ credentials: originalCredentials });
    //   sendUpdate('SSM Automation Document execution completed.');
    //   sendUpdate(`RITM Number is  ${x.Outputs.RITM} And been closed`);
     // sendUpdate('OK');
      res.end();
  } catch (err) {
    sendUpdate(`Error: ${err}`);
    res.end();
  }
};

  // Route to stop an EC2 instance
  router.post('/stop', (req, res) => handleSSMRequest(req, res, 'AWS-StopEC2Instance'));
 

  // Route to start an EC2 instance
  router.post('/start', (req, res) => handleSSMRequest(req, res, 'AWS-StartEC2Instance'));
  router.get('/domainjoin/linux', (req, res) => handledomainlinuxSSMRequest(req, res, 'linux_domain_join'));
  router.get('/domainjoin/prereqlinux', (req, res) => handledomainlinuxSSMRequest(req, res, 'linux_domain_prerequisite'));
  router.get('/domainunjoin/linux', (req, res) => handledomainlinuxSSMRequest(req, res, 'linux_domain_unjoin'));
  router.get('/domainjoin/windos', (req, res) => handledomainSSMRequest(req, res, 'windows_domain_join'));
  router.get('/domainunjoin/windos', (req, res) => handledomainSSMRequest(req, res, 'windows_domain_unjoin'));
  router.post('/run-terraform', (req, res) => handleRunTerraformRequest(req, res, 'AWS-RunPowerShellScript'));

  return router; 
};
